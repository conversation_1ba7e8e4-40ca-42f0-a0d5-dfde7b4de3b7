Note: by contributing code to the Redis project in any form, including sending
a pull request via Github, a code fragment or patch via private email or
public discussion groups, you agree to release your code under the terms
of the BSD license that you can find in the COPYING file included in the Redis
source distribution. You will include BSD license in the COPYING file within
each source file that you contribute.

# IMPORTANT: HOW TO USE REDIS GITHUB ISSUES

* Github issues SHOULD ONLY BE USED to report bugs, and for DETAILED feature
  requests. Everything else belongs to the Redis Google Group:
      
      https://groups.google.com/forum/m/#!forum/Redis-db

  PLEASE DO NOT POST GENERAL QUESTIONS that are not about bugs or suspected
  bugs in the Github issues system. We'll be very happy to help you and provide
  all the support at the Reddit sub:

      http://reddit.com/r/redis

  There is also an active community of Redis users at Stack Overflow:

      http://stackoverflow.com/questions/tagged/redis

# How to provide a patch for a new feature

1. If it is a major feature or a semantical change, please post it as a new submission in r/redis on Reddit at http://reddit.com/r/redis. Try to be passionate about why the feature is needed, make users upvote your proposal to gain traction and so forth. Read feedbacks about the community. But in this first step **please don't write code yet**.

2. If in step 1 you get an acknowledgment from the project leaders, use the
   following procedure to submit a patch:

    a. Fork Redis on github ( http://help.github.com/fork-a-repo/ )
    b. Create a topic branch (git checkout -b my_branch)
    c. Push to your branch (git push origin my_branch)
    d. Initiate a pull request on github ( https://help.github.com/articles/creating-a-pull-request/ )
    e. Done :)

For minor fixes just open a pull request on Github.

Thanks!
