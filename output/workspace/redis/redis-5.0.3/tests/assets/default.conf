# Redis configuration for testing.

always-show-logo yes
notify-keyspace-events KEA
daemonize no
pidfile /var/run/redis.pid
port 6379
timeout 0
bind 127.0.0.1
loglevel verbose
logfile ''
databases 16
latency-monitor-threshold 1

save 900 1
save 300 10
save 60 10000

rdbcompression yes
dbfilename dump.rdb
dir ./

slave-serve-stale-data yes
appendonly no
appendfsync everysec
no-appendfsync-on-rewrite no
activerehashing yes
