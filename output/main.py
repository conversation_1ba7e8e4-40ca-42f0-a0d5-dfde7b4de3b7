#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/09/21 17:00:00
"""

import logging
from flask import Flask
from celery import Celery
from flask_migrate import Migrate
from flask_bootstrap import Bootstrap

from app.utils import logs
from app.models.exts import get_db
import config
from app.analysis import analysis

logs.init_log("./log/my_program")
logger = logging.getLogger()
prefix = 'sqlite:////'
app = Flask(__name__)
bootstap = Bootstrap(app)

app.config.from_object(config.Config)
with app.app_context():
    from app.evaluate.evaluate import evaluate_bp
    from app.evaluate.evaluate_celery import make_celery
    from app.reports import report
    from app.evaluate.up_query import up_query
    from app.debugs.debug import debugs
    from app.con_env import environment
    from app.register.register_service import register_bp
    from app.compare import compares, test_report, testimage

    db = get_db()
    print db
    db.init_app(app)

    migrate = Migrate(app, db)
    # db.drop_all()
    # db.create_all()
    # db.reflect(app=app)
    tables = db.metadata.tables

    celery = make_celery(app)

app.register_blueprint(debugs, url_prefix='/')
app.register_blueprint(report, url_prefix='/evaluate')
app.register_blueprint(evaluate_bp, url_prefix='/')
app.register_blueprint(analysis, url_prefix='/evaluate/analysis')
app.register_blueprint(analysis, url_prefix='/analysis')
app.register_blueprint(compares, url_prefix='/')
app.register_blueprint(up_query, url_prefix='/')
app.register_blueprint(environment, url_prefix='/')
app.register_blueprint(register_bp, url_prefix='/')
app.register_blueprint(test_report, url_prefix='/evaluate')
app.register_blueprint(testimage, url_prefix='/')

if __name__ == '__main__':
    app.run(debug=True)
