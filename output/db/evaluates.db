SQLite format 3   @    	   *           �                                                	 .;�   �    ������                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
   
      
   
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  b '3C�r?g�c ^��?�ˋDW.1695187272.922023-09-20 13:26:12ngdv711ngdv711_2023-09-20 13:26:12   
�    )����������                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 %H   >   "7   /   &               
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
    � �5 � �����                                                                                                                                                                                                                            �>�_tabletasktaskCREATE TABLE task (
	id INTEGER NOT NULL, 
	version VARCHAR(100) NOT NULL, 
	env_name VARCHAR(100) NOT NULL, 
	engine VARCHAR(100) NOT NULL, 
	result_dir VARCHAR(200), 
	task_id VARCHAR(200) NOT NULL, 
	create_time VARCHAR(200), 
	finish_time VARCHAR(200), 
	msg VARCHAR(500), 
	PRIMARY KEY (id)
)�v--�tableentitys_analysisentitys_analysisCREATE TABLE entitys_analysis (
	id INTEGER NOT NULL, 
	testcasecount INTEGER NOT NULL, 
	correct_count INTEGER, 
	error_count INTEGER, 
	correct_percent INTEGER, 
	error_percent INTEGER, 
	task_id VARCHAR(200) NOT NULL, 
	finish_time VARCHAR(200), 
	version VARCHAR(100), 
	version_finish_time VARCHAR(200), 
	PRIMARY KEY (id)
)P
++Ytablesqlite_sequencesqlite_sequence CREATE TABLE sqlite_sequence(name,seq)
   � �|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       �y
//�!tableattitude_analysisattitude_analysisCREATE TABLE attitude_analysis (
	id INTEGER NOT NULL, 
	testcasecount INTEGER NOT NULL, 
	correct_count INTEGER, 
	error_count INTEGER, 
	correct_percent INTEGER, 
	error_percent INTEGER, 
	task_id VARCHAR(200) NOT NULL, 
	finish_time VARCHAR(200), 
	version VARCHAR(100), 
	version_finish_time VARCHAR(200), 
	PRIMARY KEY (id)
)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
   ! !                                                                                                                                                                                                                                                                                    �\--�ktableintents_analysisintents_analysisCREATE TABLE intents_analysis (
	id INTEGER NOT NULL, 
	testcasecount INTEGER NOT NULL, 
	response_count INTEGER NOT NULL, 
	response_count_claify INTEGER NOT NULL, 
	response_count_not_claify INTEGER NOT NULL, 
	response_correct_count INTEGER, 
	clarify_correct_count INTEGER, 
	call_percent_include_clarify INTEGER, 
	call_percent_not_include_clarify INTEGER, 
	correct_call_percent_include_clarify INTEGER, 
	correct_call_percent_not_include_clarify INTEGER, 
	prec_percent_include_clarify INTEGER, 
	prec_percent_not_include_clarify INTEGER, 
	task_id VARCHAR(200) NOT NULL, 
	finish_time VARCHAR(200), 
	version VARCHAR(200), 
	version_finish_time VARCHAR(200), 
	PRIMARY KEY (id)
)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
    � 1 � �                                                                                                                            �#�tableanalysisanalysisCREATE TABLE analysis (
	id INTEGER NOT NULL, 
	testcasecount INTEGER NOT NULL, 
	response_count INTEGER NOT NULL, 
	no_response_count INTEGER NOT NULL, 
	correct_count INTEGER, 
	error_count INTEGER, 
	call_percent INTEGER, 
	prec_percent INTEGER, 
	task_id VARCHAR(200) NOT NULL, 
	finish_time VARCHAR(200), 
	version VARCHAR(100), 
	version_finish_time VARCHAR(200), 
	PRIMARY KEY (id)
)�L--�Ktableend2end_analysisend2end_analysis	CREATE TABLE end2end_analysis (
	id INTEGER NOT NULL, 
	testcasecount INTEGER NOT NULL, 
	response_count INTEGER NOT NULL, 
	no_response_count INTEGER NOT NULL, 
	return_correct_count INTEGER, 
	return_error_count INTEGER, 
	call_percent INTEGER, 
	correct_percent INTEGER, 
	task_id VARCHAR(200) NOT NULL, 
	finish_time VARCHAR(200), 
	version VARCHAR(100), 
	version_finish_time VARCHAR(200), 
	PRIMARY KEY (id)
)
   � �Z�U��                                                                                                                                                                                                                                                                                                                                                                                    `
 33;?�򆼡�'?�򆼡�)local-1697014617.132023-10-11 16:56:57730730-2023-10-11 16:56:57g
    13Cr?g�c ^��?�ˋDW.local-1695187785.62023-09-20 13:29:45ngdv711ngdv711-2023-09-20 13:29:45U
 '3CNNN1695186368.542023-09-20 13:09:56ngdv711ngdv711_2023-09-20 13:09:56U
 	'3CNNN1695186264.822023-09-20 13:05:41ngdv711ngdv711_2023-09-20 13:05:41U
 	'3CNNN1695185958.762023-09-20 13:00:35ngdv711ngdv711_2023-09-20 13:00:35U
 	'3CNNN1695185272.542023-09-20 12:50:26ngdv711ngdv711_2023-09-20 12:50:26M
 	'3;NNN1695184832.152023-09-20 12:41:43730730_2023-09-20 12:41:43
   � G�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �6 [3�yEvaluate-Test-Report20230920133911.xlsx2023-09-20 13:39:11/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/com_reports/Evaluate-Test-Report20230920133911.xlsx�6 [3�yEvaluate-Test-Report20230920133850.xlsx2023-09-20 13:38:50/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/com_reports/Evaluate-Test-Report20230920133850.xlsx
    = ) = �                                               �i�9tableenvenvCREATE TABLE env (
	id INTEGER NOT NULL, 
	version VARCHAR(100) NOT NULL, 
	env_name VARCHAR(100) NOT NULL, 
	backend_address VARCHAR(40) NOT NULL, 
	core_address VARCHAR(40) NOT NULL, 
	username VARCHAR(40) NOT NULL, 
	uid VARCHAR(40) NOT NULL, 
	account_type VARCHAR(40) NOT NULL, 
	bot_name VARCHAR(100) NOT NULL, 
	no_answer TEXT NOT NULL, 
	clarify_answer TEXT NOT NULL, 
	"agentId" VARCHAR(100) NOT NULL, 
	"botToken" VARCHAR(100) NOT NULL, 
	authorization VARCHAR(100) NOT NULL, 
	nlu_enable BOOLEAN NOT NULL, 
	thread_num INTEGER NOT NULL, 
	user_name VARCHAR(100) NOT NULL, large_model_no_answer TEXT NULL, knowledge_address STRING, tenantId STRING, bot_id STRING, 
	PRIMARY KEY (id), 
	CHECK (nlu_enable IN (0, 1))
)�T##�otabletest_reporttest_reportCREATE TABLE test_report (
	id INTEGER NOT NULL, 
	report_name VARCHAR(100) NOT NULL, 
	upload_time VARCHAR(40) NOT NULL, 
	file_path VARCHAR(100) NOT NULL, 
	PRIMARY KEY (id)
)
    # m�[�? � #             �> %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231012203229.xlsx1697113929.422023-10-12 20:32:092023-10-12 20:32:29�= %#�33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231012202935.xlsxlocal-1697113871.62-2023-10-12 20:31:11�< %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231012202935.xlsx1697113749.392023-10-12 20:29:092023-10-12 20:29:35�; %#�33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011213703.xlsxlocal-1697031769.49-2023-10-11 21:42:49�: %#�33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011213703.xlsxlocal-1697031709.45-2023-10-11 21:41:49�9 %#�33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011213703.xlsxlocal-1697031584.51-2023-10-11 21:39:44�8 %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231011213703.xlsx1697031403.542023-10-11 21:36:432023-10-11 21:37:03   �    '����                                                                                                                                                                                         � 799!/Q�	U/]'730730沙盒环境效果http://10.27.65.2:8930http://10.27.65.2:8931dcj29441337292992PASSPORTBot1-问答效果抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。bc1e941b-4bc7-415d-adfa-fd08d2b2f488Bot1-问答效果NGD 5bd2416a-d524-463e-ac76-c39bc93e40bfzhangjiabin01�! %GG#+Q�	U]]'730finch_onlinehttps://ics.bce.baidu.com/ngdhttps://ics.bce.baidu.com/ngduser1t1000000001fake大模型测试抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。fcca60e7-43ad-4d30-bf62-cb7a69e4342cNGD 0556778e-e142-4b72-9ee6-c3226085aa4eNGD 0556778e-e142-4b72-9ee6-c32260   !	            �    *�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                (
    �  � �                                                                                                                                                                                                         �>%%�?tablefaq_analysisfaq_analysisCREATE TABLE faq_analysis (
	id INTEGER NOT NULL, 
	testcasecount INTEGER NOT NULL, 
	hit_clarify_count INTEGER, 
	hit_count INTEGER, 
	clarify_count INTEGER, 
	response_count INTEGER NOT NULL, 
	no_response_count INTEGER NOT NULL, 
	call_percent INTEGER, 
	correct_call_percent_include_clarify INTEGER, 
	correct_call_percent_not_include_clarify INTEGER, 
	prec_percent_include_clarify INTEGER, 
	task_id VARCHAR(200) NOT NULL, 
	finish_time VARCHAR(200), 
	version VARCHAR(100), 
	version_finish_time VARCHAR(200), 
	PRIMARY KEY (id)
)�d�'tablequeryqueryCREATE TABLE "query" (
	id INTEGER NOT NULL, 
	file_name VARCHAR(100) NOT NULL, 
	upload_time VARCHAR(40) NOT NULL, 
	file_path VARCHAR(100) NOT NULL, 
	resource VARCHAR(100) NOT NULL, 
	PRIMARY KEY (id)
)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
    � , �                                                                                                                         �$55�ktablelarge_model_analysislarge_model_analysisCREATE TABLE large_model_analysis
(
    id                   INTEGER      not null
        primary key,
    testcasecount        INTEGER      not null,
    response_count       INTEGER      not null,
    no_response_count    INTEGER      not null,
    return_correct_count INTEGER,
    call_percent         INTEGER,
    correct_percent      INTEGER,
    scoring_rate_percent INTEGER,
    task_id              VARCHAR(200) not null,
    finish_time          VARCHAR(200),
    version              VARCHAR(100),
    version_finish_time  VARCHAR(200)
, acceptability TEXT NULL, acceptable_count TEXT NULL, avg_score TEXT NULL)�Q!!�mtabletrain_datatrain_dataCREATE TABLE train_data (
	id INTEGER NOT NULL, 
	report_name VARCHAR(100) NOT NULL, 
	upload_time VARCHAR(40) NOT NULL, 
	file_path VARCHAR(100) NOT NULL, 
	PRIMARY KEY (id)
)
    � m�H�" �                                                                                                                           � %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920005337.xlsx1695142411.182023-09-20 00:53:312023-09-20 00:53:37� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920005053.xlsx1695142243.882023-09-20 00:50:432023-09-20 00:50:53� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920004703.xlsx1695142015.732023-09-20 00:46:552023-09-20 00:47:03� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920004612.xlsx1695141960.292023-09-20 00:46:002023-09-20 00:46:13� %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920004415.xlsx1695141846.62023-09-20 00:44:062023-09-20 00:44:15� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920003344.xlsx1695141185.262023-09-20 00:33:052023-09-20 00:33:44
r  L n�I� L �r                                                      � %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920101621.xlsx1695145765.842023-09-20 01:49:252023-09-20 10:16:21� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920101616.xlsx1695145895.332023-09-20 01:51:352023-09-20 10:16:16   D                                                                � %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920013516.xlsx1695144908.262023-09-20 01:35:082023-09-20 01:35:16� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920013220.xlsx1695144733.122023-09-20 01:32:132023-09-20 01:32:20� %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920012520.xlsx1695144315.12023-09-20 01:25:152023-09-20 01:25:20�
 %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920011835.xlsx1695143909.62023-09-20 01:18:292023-09-20 01:18:35
    ' 	 '                           �_ 799!/Q�	U/]'�/730730沙盒环境效果http://10.27.65.2:8930http://10.27.65.2:8931dcj29441337292992PASSPORTBot1-问答效果抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。bc1e941b-4bc7-415d-adfa-fd08d2b2f488Bot1-问答效果NGD 5bd2416a-d524-463e-ac76-c39bc93e40bfzhangjiabin01当前文档无法回答你的问题，我可以尝试用我的常识来回答你�t %GG#+Q�	U]]'�/730finch_onlinehttps://ics.bce.baidu.com/ngdhttps://ics.bce.baidu.com/ngduser1t1000000001fake大模型测试抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。fcca60e7-43ad-4d30-bf62-cb7a69e4342cNGD 0556778e-e142-4b72-9ee6-c3226085aa4eNGD 0556778e-e142-4b72-9ee6-c3226085aa4ezhangjiabin01当前文档无法回答你的问题，我可以尝试用我的常识来回答你
    L m)�r � L                                                      �& %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008212952.xlsx1696771770.692023-10-08 21:29:302023-10-08 21:29:52�% %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008211253.xlsx1696770745.872023-10-08 21:12:252023-10-08 21:12:53�$ %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008204958.xlsx1696769375.72023-10-08 20:49:352023-10-08 20:49:58�# %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008204724.xlsx1696769222.912023-10-08 20:47:032023-10-08 20:47:25�" %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008204159.xlsx1696768882.52023-10-08 20:41:222023-10-08 20:41:59B!
 % '3  730finch_online大模型1696768456.282023-10-08 20:34:16�  %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008202938.xlsx1696768139.162023-10-08 20:28:592023-10-08 20:29:38
                        �b AA#Q�	U]]'�/ngdv711ngdv711http://10.188.102.155:8726http://10.188.102.155:8724user1t1000000001faketest抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。631b8039-e5f9-4dee-820d-cf267c9ee73fNGD a942d849-0fcb-44a8-8303-09dfc7bd2a6bNGD 761eb200-1c21-448f-968e-1368e4b46e79zhangjiabin01当前文档无法回答你的问题，我可以尝试用我的常识来回答你�z 7GG1Q�	U]]'�/730730线上系统实体https://ics.bce.baidu.com/ngdhttps://ics.bce.baidu.com/ngd智能外呼压测34494312UCtest抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。fc669756-84f9-48e7-bf81-9056e003dac0NGD 1c35f694-229b-475d-97be-e6e907b1025dNGD e9aa428a-daa9-4577-b6a9-31d229a131b5zhangjiabin01当前文档无法回答你的问题，我可以尝试用我的常识来回答你   �    &��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         $   #	
    = �� =                                               �u GG#!Q�	


'�/7303test3https://ics.bce.baidu.com/ngdhttps://ics.bce.baidu.com/ngduser1t1000000001fakeStar Robot抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。zhangjiabin01当前文档无法回答你的问题，我可以尝试用我的常识来回答你�# GG#!Q�	


' 7302test2https://ics.bce.baidu.com/ngdhttps://ics.bce.baidu.com/ngduser1t1000000001fakeStar Robot抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。zhangjiabin01�" GG#!Q�	


' 7301testhttps://ics.bce.baidu.com/ngdhttps://ics.bce.baidu.com/ngduser1t1000000001fakeStar Robot抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。zhangjiabin01
    � m�G�! � L                                                         C                                                               � %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920011023.xlsx1695143415.42023-09-20 01:10:152023-09-20 01:10:23� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920010521.xlsx1695143113.792023-09-20 01:05:132023-09-20 01:05:21�
 %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920010453.xlsx1695143083.822023-09-20 01:04:432023-09-20 01:04:53�	 %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920010218.xlsx1695142931.242023-09-20 01:02:112023-09-20 01:02:18� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920005729.xlsx1695142641.122023-09-20 00:57:212023-09-20 00:57:29� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920005701.xlsx1695142612.232023-09-20 00:56:522023-09-20 00:57:01
    | m�G� | 4                                 H                                                                    � )�'33 ngdv711ngdv711tableqa_rounds/data/result_dir/tableqa_rounds_result_20230920125026.xlsx1695185272.542023-09-20 12:47:522023-09-20 12:50:26� 7)�'33 730730沙盒环境效果tableqa_rounds/data/result_dir/tableqa_rounds_result_20230920124143.xlsx1695184832.152023-09-20 12:40:322023-09-20 12:41:43� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920112906.xlsx1695180541.722023-09-20 11:29:012023-09-20 11:29:06� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920102044.xlsx1695176440.752023-09-20 10:20:402023-09-20 10:20:44� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920101822.xlsx1695176284.252023-09-20 10:18:042023-09-20 10:18:22� %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20230920101623.xlsx1695175001.972023-09-20 09:56:412023-09-20 10:16:23
�  } i�;� } 9                                   � D                                                                � %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008202304.xlsx1696767765.112023-10-08 20:22:452023-10-08 20:23:04� )�13 ngdv711ngdv711tableqa_rounds/data/result_dir/sys_entity_evaluate_result_20230920132612_old.xlsxlocal-1695187785.6-2023-09-20 13:29:45    � %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231008202026.xlsx1696767624.012023-10-08 20:20:242023-10-08 20:20:26� )�'33 ngdv711ngdv711tableqa_rounds/data/result_dir/tableqa_rounds_result_20230920130956.xlsx1695186368.542023-09-20 13:06:082023-09-20 13:09:56� )�'33 ngdv711ngdv711tableqa_rounds/data/result_dir/tableqa_rounds_result_20230920130541.xlsx1695186264.822023-09-20 13:04:242023-09-20 13:05:41� )�'33 ngdv711ngdv711tableqa_rounds/data/result_dir/tableqa_rounds_result_20230920130035.xlsx1695185958.762023-09-20 12:59:182023-09-20 13:00:35
   	 i n��S�� � i                                                                               �	/ %)�33 730finch_onlinetableqa_rounds/data/result_dir/largeModel_res_result_20231011164158.xlsxlocal-1697014617.13-2023-10-11 16:56:57�. %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20231011164158.xlsx1697013699.22023-10-11 16:41:392023-10-11 16:41:59B-
 % '3  730finch_online大模型1697013573.022023-10-11 16:39:33B,
 % '3  730finch_online大模型1697012857.412023-10-11 16:27:37B+
 % '3  730finch_online大模型1697012646.672023-10-11 16:24:06B*
 % '3  730finch_online大模型1697012330.072023-10-11 16:18:50B)
 % '3  730finch_online大模型1697012161.592023-10-11 16:16:01�( %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231011110515.xlsx1696993499.562023-10-11 11:04:592023-10-11 11:05:15�' %�%33 730finch_online大模型/data/result_dir/largeModel_res_result_20231010142243.xlsx1696918957.22023-10-10 14:22:372023-10-10 14:22:43
      �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
    � � �                                                                                                                                                             �[	 AA#!Q�	UU]	'�/740740http://10.188.102.155:8735http://10.188.102.155:8733user1t1000000001fakemind效果抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。26dde01c-64ac-400b-b210-527dc0ce3d749dc2a560-b0a6-4538-b44f-011a47a8ac9cNGD aa90c964-3e85-4f35-99cb-471212de5165zhangjiabin01当前文档无法回答你的问题，我可以尝试用我的常识来回答你�v GG#!Q�	


'�/test4test4https://ics.bce.baidu.com/ngdhttps://ics.bce.baidu.com/ngduser1t1000000001fakeStar Robot抱歉,我不太理解您的意思请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。zhangjiabin01当前文档无法回答你的问题，我可以尝试用我的常识来回答你
    5 w��� y 5                             B7
 % '3  730finch_online大模型1697031355.682023-10-11 21:35:55�6 %#�33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011204718.xlsxlocal-1697031272.69-2023-10-11 21:34:32�5 %#�33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011204718.xlsxlocal-1697029580.45-2023-10-11 21:06:20�4 %#�13 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011204718.xlsxlocal-1697029440.7-2023-10-11 21:04:00�3 %�'33 730finch_online大模型/data/result_dir/largeModel_res_result_20231011204718.xlsx1697028393.462023-10-11 20:46:332023-10-11 20:47:18B2
 % '3  730finch_online大模型1697027800.442023-10-11 20:36:40�
1 %#�	33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011164158 (1).xlsxlocal-1697025733.12-2023-10-11 20:02:13�0 %#�33 730finch_onlinelarge_model/data/result_dir/largeModel_res_result_20231011164158.xlsxlocal-1697025692.84-2023-10-11 20:01:32
   	 b �Q��!�$ � b                                                                        b	 33;?ה5�
yClocal-1697031584.512023-10-11 21:39:44730730_2023-10-11 21:39:440.000.0\ '3;?ה5�
yC1697031403.542023-10-11 21:37:03730730_2023-10-11 21:37:030.000.0� 33;//?↼��(l?�5�
yC^?ǔ5�
yClocal-1697031272.692023-10-11 21:34:32730730_2023-10-11 21:34:320.26315789473684250.368421052631579i
 33;?↼��(l?�5�
yC^?ǔ5�
yClocal-1697029580.452023-10-11 21:06:20730730_2023-10-11 21:06:20h
 13;	
?�Pה5�
?�򆼡�(?�5�
yC^local-1697029440.72023-10-11 21:04:00730730_2023-10-11 21:04:00b
 	'3;?ה5�
yC?�򆼡�(?�5�
yC^1697028393.462023-10-11 20:47:18730730_2023-10-11 20:47:18`
 33;?�򆼡�(?�򆼡�(local-1697025733.122023-10-11 20:02:13730730_2023-10-11 20:02:13`
 33;?�򆼡�(?�򆼡�(local-1697025692.842023-10-11 20:01:32730730_2023-10-11 20:01:32K
 %))?�򆼡�(?�򆼡�(1697013699.22023101116415873020231011164158
    T ���s � T                                                            g 	'3;dd?���Q�?���
=p�1699861196.412023-11-13 15:43:29740740_2023-11-13 15:43:290.19190.26Y 			'3;?�      1699860989.982023-11-13 15:36:57740740_2023-11-13 15:36:571.011.0Y 			'3;?�      1699859040.122023-11-13 15:04:30740740_2023-11-13 15:04:301.011.0�  '3;//?ה5�
yC?ǔ5�
yC1697113929.422023-10-12 20:32:29730730_2023-10-12 20:32:290.36842105263157970.368421052631579�
 33;//?↼��(l?�򆼡�(?�Pה5�
local-1697113871.622023-10-12 20:31:11730730_2023-10-12 20:31:110.36842105263157970.473684210526316� 	'3;//?ה5�
yC?�򆼡�(?�ה5�
y1697113749.392023-10-12 20:29:35730730_2023-10-12 20:29:350.21052631578947440.263157894736842� 33;//?ה5�
yC?ǔ5�
yClocal-1697031769.492023-10-11 21:42:49730730_2023-10-11 21:42:490.36842105263157970.368421052631579b
 33;?ה5�
yClocal-1697031709.452023-10-11 21:41:49730730_2023-10-11 21:41:490.000.0
   
 v v; �; v � � v ;                                ;                                                       9H
  '3  740740大模型1699868841.362023-11-13 17:47:219G
  '3  740740大模型1699867985.892023-11-13 17:33:05�F �'33 740740大模型/data/result_dir/largeModel_res_result_20231113164643.xlsx1699861853.872023-11-13 15:50:532023-11-13 16:46:45�E �'33 740740大模型/data/result_dir/largeModel_res_result_20231113154329.xlsx1699861196.412023-11-13 15:39:562023-11-13 15:43:309D
  '3  740740大模型1699861091.832023-11-13 15:38:11�C �'33 740740大模型/data/result_dir/largeModel_res_result_20231113153657.xlsx1699860989.982023-11-13 15:36:302023-11-13 15:36:579B
  '3  740740大模型1699860307.842023-11-13 15:25:079A
  '3  740740大模型1699860131.742023-11-13 15:22:119@
  '3  740740大模型1699860013.622023-11-13 15:20:13�? �'33 740740大模型/data/result_dir/largeModel_res_result_20231113150430.xlsx1699859040.122023-11-13 15:04:002023-11-13 15:04:30
   0 �0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    e 	'3;?�      ?�      1699869195.432023-11-13 17:54:36740740_2023-11-13 17:54:361.041.75g 	'3;dd+?ۅ�Q�?���Q�1699861853.872023-11-13 16:46:43740740_2023-11-13 16:46:430.94941.37
    I��                                                                                                                                                                                                                                                                        �8
 ??U? doc3doc3http://10.27.241.53:30930http://10.27.241.53:31888user1user1fake80srysrya18f30f3-6145-4c0a-84c4-4afd4978d4c7xxxxansiqixxhttp://10.27.241.53:31888 >bl   �8 ??U? doc2doc2http://10.27.241.53:30930http://10.27.241.53:31888user1user1fake80srysry3606e2de-36ad-4749-97b8-31d020b7ffefxxxxansiqixxhttp://10.27.241.53:31888 >bl   �8 ??U? doc1doc1http://10.27.241.53:30930http://10.27.241.53:31888user1user1fake80srysryc2a4b2eb-8616-4563-8dce-baaa11d06872xxxxansiqixxhttp://10.27.241.53:31888 >bl   �4
 ??U? 8181http://10.27.241.53:30930http://10.27.241.53:31888user1user1fake80srysry6eb9e7ad-fc34-4fd4-b12f-05d7dc048c59xxxxansiqixxhttp://10.27.241.53:31888 >bl   
    d J��, d                                                                                  �E Y3�%【澳门旅游局】query集 (1).xlsx2023-11-13 17:52:34/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/testreport_file/【澳门旅游局】query集 (1).xlsxCustomUpload�= Q3�w%【澳门旅游局】query集.xlsx2023-11-13 17:32:13/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/testreport_file/【澳门旅游局】query集.xlsxCustomUpload�9 M3�s%大模型上传文件示例.xlsx2023-10-08 20:28:02/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/testreport_file/大模型上传文件示例.xlsxCustomUpload� 33�Y%sys_entity_big.xlsx2023-09-20 13:19:10/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/testreport_file/sys_entity_big.xlsxCustomUpload�3 G3�m%tableqa_query_707_rounds.xlsx2023-09-20 12:34:14/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/testreport_file/tableqa_query_707_rounds.xlsxCustomUpload
   v v                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            �I �'33 740740大模型/data/result_dir/largeModel_res_result_20231113175436.xlsx1699869195.432023-11-13 17:53:152023-11-13 17:54:36
   I�*�I                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         g 33k%macau_template.xlsx2024-03-12 21:00:00./app/data/llm_search/macau/macau_template.xlsxCustomUploads
 ;3{%guangdong_template.xlsx2024-03-12 21:00:00./app/data/llm_search/guangdong/guangdong_template.xlsxCustomUpload   g	 33k%hebei_template.xlsx2024-03-12 21:00:00./app/data/llm_search/hebei/hebei_template.xlsxCustomUploadk 93m%faq_test_template.xlsx2024-03-12 21:00:00./app/data/llm_search/faq/faq_test_template.xlsxCustomUpload