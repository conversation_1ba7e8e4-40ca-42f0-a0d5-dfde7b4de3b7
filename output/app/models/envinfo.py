#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
创建数据库env.db
"""
import os
# 导入SQLite驱动:
import sqlite3

db_file = os.path.join(os.path.dirname(__file__), 'env.db')
if os.path.isfile(db_file):
    os.remove(db_file)

# 连接到SQLite数据库
# 数据库文件是test.db
# 如果文件不存在，会自动在当前目录创建:
conn = sqlite3.connect('env.db')
# 创建一个Cursor:
cursor = conn.cursor()
# 执行一条SQL语句，创建user表:
cursor.execute(
    'create table env (id varchar(20) primary key, backend_address varchar(40), '
    'core_address varchar(40), username varchar(40), uid varchar(40), account_type varchar(40))')
conn.commit()
# 关闭Cursor:
cursor.close()
# 关闭Connection:
conn.close()
