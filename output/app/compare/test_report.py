#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/5/16 20:05:00
"""
import os
import time

from app.reports import report
from flask import request, send_file, jsonify

from . import test_report
from ..models.models import Task, Analysis, TestReport, TrainData
from ..models.exts import get_db
from datetime import datetime
from app.evaluate.up_query import strftime
global db
db = get_db()
session = db.session


@test_report.route('getTestReportList', methods=['GET'])
def get_test_report_list():
    """
    获取效果报告列表
    """
    data_args = request.args
    pn = data_args.get('pn', 1, type=int)
    ps = data_args.get('ps', 20, type=int)
    task_list = []
    # print pn, ps
    try:
        print pn, ps
        tmp_data = session.query(TestReport)
        # 查询数据库中总条数
        total = tmp_data.count()
        task_info_list = tmp_data.order_by(TestReport.id.desc()).offset((pn - 1) * ps).limit(ps).all()
        for task in task_info_list:
            print task.taskToDict()
            task_list.append(task.taskToDict())
        res = {
            'code': 200,
            'data': {
                'pn': pn,
                'ps': ps,
                'total': total,
                'list': task_list
            },
            'msg': 'OK'
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        res = {
            'code': 500,
            'msg': '服务器异常'
        }
        return jsonify(res), 500


@test_report.route('deleteTestReport', methods=['POST'])
def delete_test_report():
    """
    删除报告
    :return:
    """
    data_form = request.form
    print data_form
    if data_form is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    ids = data_form.get('ids')
    if ids is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    try:
        print type(ids)
        id_list = ids.split(',')
        print id_list
        for db_id in id_list:
            print db_id
            task = session.query(TestReport).filter(TestReport.id == db_id).first()
            session.query(TestReport).filter(TestReport.id == db_id).delete()
            file_path = task.file_path.encode('utf-8')
            if os.path.exists(file_path):
                os.remove(file_path)
        session.commit()
        res = {
            'code': 200,
            'msg': "OK"
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        session.rollback()
        res = {
            'code': 500,
            'msg': '服务器异常，删除失败'
        }
        return jsonify(res), 500


@test_report.route('uploadTestReport', methods=['post'])
def upload_test_report():
    """
    上传效果测试报告
    :return:
    """
    file = request.files.get('file')
    print(type(file))
    print(file)
    if not file.filename.endswith('.xlsx'):
        return {'code': 400, 'msg': '请上传xlsx类型的文件'}
    # 判断目录是否存在，不存在则创建
    cur_path = os.getcwd()
    path = cur_path + '/testreport_file/'
    if not os.path.exists(path):
        os.makedirs(path)
    res_path = path + file.filename
    file.save(res_path.encode('utf-8'))
    end = strftime(time.time())
    new_report = TestReport(report_name=file.filename,
                      upload_time=end,
                      file_path=res_path
                      )
    try:
        if (TestReport.query.filter_by(report_name=file.filename).count()) > 0:
            return {'code': 200, 'msg': '文件已存在', 'status': 1}
        else:
            db.session.add(new_report)
            db.session.commit()
            # print Query.query.all()
    except Exception as e:
        print(e)
        msg = e
    return {'code': 200, 'msg': '文件上传成功', 'filename': file.filename,
            'time': str(end), 'path': path, 'status': 0}

@test_report.route('getTrainDataList', methods=['GET'])
def get_train_data_list():
    """
    获取训练数据列表
    """
    data_args = request.args
    pn = data_args.get('pn', 1, type=int)
    ps = data_args.get('ps', 20, type=int)
    task_list = []
    # print pn, ps
    try:
        print pn, ps
        tmp_data = session.query(TrainData)
        # 查询数据库中总条数
        total = tmp_data.count()
        task_info_list = tmp_data.order_by(TrainData.id.desc()).offset((pn - 1) * ps).limit(ps).all()
        for task in task_info_list:
            print task.taskToDict()
            task_list.append(task.taskToDict())
        res = {
            'code': 200,
            'data': {
                'pn': pn,
                'ps': ps,
                'total': total,
                'list': task_list
            },
            'msg': 'OK'
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        res = {
            'code': 500,
            'msg': '服务器异常'
        }
        return jsonify(res), 500


@test_report.route('deleteTrainData', methods=['POST'])
def delete_train_data():
    """
    删除训练数据
    :return:
    """
    data_form = request.form
    print data_form
    if data_form is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    ids = data_form.get('ids')
    if ids is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    try:
        print type(ids)
        id_list = ids.split(',')
        print id_list
        for db_id in id_list:
            print db_id
            task = session.query(TrainData).filter(TrainData.id == db_id).first()
            session.query(TrainData).filter(TrainData.id == db_id).delete()
            file_path = task.file_path.encode('utf-8')
            if os.path.exists(file_path):
                os.remove(file_path)
        session.commit()
        res = {
            'code': 200,
            'msg': "OK"
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        session.rollback()
        res = {
            'code': 500,
            'msg': '服务器异常，删除失败'
        }
        return jsonify(res), 500


@test_report.route('uploadTrainData', methods=['post'])
def upload_train_data():
    """
    上传训练数据
    :return:
    """
    file = request.files.get('file')
    print(type(file))
    print(file)
    # 判断目录是否存在，不存在则创建
    cur_path = os.getcwd()
    path = cur_path + '/traindata_file/'
    if not os.path.exists(path):
        os.makedirs(path)
    res_path = path + file.filename
    file.save(res_path.encode('utf-8'))
    end = strftime(time.time())
    new_report = TrainData(report_name=file.filename,
                      upload_time=end,
                      file_path=res_path
                      )
    try:
        if (TrainData.query.filter_by(report_name=file.filename).count()) > 0:
            return {'code': 200, 'msg': '文件已存在', 'status': 1}
        else:
            db.session.add(new_report)
            db.session.commit()
            # print Query.query.all()
    except Exception as e:
        print(e)
        msg = e
    return {'code': 200, 'msg': '文件上传成功', 'filename': file.filename,
            'time': str(end), 'path': path, 'status': 0}

