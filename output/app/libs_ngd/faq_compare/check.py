#!/usr/bin/python
# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/10/26
"""

from ..utils.core_query import CoreQuery
from ..utils.log import LOG
from ..utils.nlu_recognize import NluRecognize


class FaqQueryCompare:
    """
    faq
    """

    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.conf = conf
        self.url = conf.url

    def checkResult(self, sQuery, expectResult):
        """
        check
        Args:
            sQuery:
            expectResult:

        Returns:

        """
        # 获取nlu分析结果，并校验
        nluRes = self.getNluResult(sQuery)
        bNluRes = True
        for i in range(len(expectResult)):
            if not expectResult[i] in nluRes:
                bNluRes = False
                LOG.error("sQuery: %s , nlu-result： %s, expectResult: %s" % (sQuery, nluRes, expectResult))

        # 获取corequery结果,并校验
        coreRes = self.getCoreQueryResult(sQuery)
        bCoreRes = True
        if coreRes != expectResult:
            bCoreRes = False
            LOG.error("sQuery: %s , core-result： %s, expectResult: %s" % (sQuery, coreRes, expectResult))

        result = bNluRes and bCoreRes
        return nluRes, coreRes, result

    def getNluResult(self, sQuery):
        """
        nlu
        Args:
            sQuery:

        Returns:

        """
        nluRecog = NluRecognize()
        nluResult = nluRecog.nlu_faq_recognize(sQuery, self.conf)
        nluRes = []
        try:
            for i in range(len(nluResult["data"]["list"])):
                standardQuestion = nluResult["data"]["list"][i]["standardQuestion"]
                if standardQuestion not in nluResult:
                    nluRes.append(standardQuestion)
        except KeyError as e:
            LOG.error("访问 %s parseNluResult出错" % sQuery)
        return nluRes

    def getCoreQueryResult(self, sQuery):
        """
        core
        Args:
            sQuery:

        Returns:

        """
        corequery = CoreQuery(self.conf)
        coreResult = corequery.core_query(sQuery).json()

        coreRes = []
        try:
            detail = coreResult["data"]["debug"]["detail"]
            if "clarifyType" in detail:
                coreRes = detail["clarifyQuestions"]["text"]["list"]
            else:
                coreRes.append(detail["standardQuestion"])
            return coreRes
        except Exception as e:
            print(e)
            LOG.error("抛出异常：访问【%s】解析corequery结果出错" % sQuery)
