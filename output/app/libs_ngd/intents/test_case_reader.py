#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""

import openpyxl

class IntentsTestReader:
    """
    FaqTestReader
    """
    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[0])

        test_cases = []

        skip_first = True
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue

            query = row_i[1].value
            standard_id = row_i[0].value
            standard_question = row_i[2].value
            test_cases.append({'query': query, 'standard_question': standard_question, 'standard_id': standard_id})

        return test_cases
