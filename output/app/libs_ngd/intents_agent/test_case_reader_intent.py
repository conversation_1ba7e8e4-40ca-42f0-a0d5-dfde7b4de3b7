#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""

import openpyxl

from ..utils.log import LOG


class IntentsAgentTestReader:
    """
    FaqTestReader
    """

    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        # 第二个tab的意图示例
        sheet = workbook.get_sheet_by_name(sheet_names[1])

        test_cases = []

        skip_first = True
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue

            query = row_i[1].value
            standard_id = row_i[0].value
            test_cases.append({'query': u'%s' % query, 'standard_id': standard_id})
            LOG.info({'query': u'%s' % query, 'standard_id': standard_id})
        return test_cases

    def read_entity(self, test_cases_query, test_list):
        """
        read_entity
        :param test_cases_query:
        :param test_list:
        :return:
        """
        for query_list in test_cases_query:
            query = query_list['query']
            entity_list = []
            entity_nameZh_list = []
            for data in test_list:
                if data['query'] in query:
                    entity_list.append(data['entity_answer'])
                    entity_nameZh_list.append(data['entity_nameZh'])
            query_list['entity_list'] = entity_list
            query_list['entity_nameZh_list'] = entity_nameZh_list
        # LOG.info("test_cases_query : %s" % test_cases_query)
        return test_cases_query
