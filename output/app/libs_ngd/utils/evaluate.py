#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This self.module provide http request.

Authors: <AUTHORS>
Date:    2020/04/09 22:04:06
"""
import time
import logging
from ..entity import entity_evaluate
from ..entity.test_case_reader import EntityTestReader
from ..intents.intents_evaluate import MulitIntents
from ..intents.intents_query import IntentsQuery
from ..intents.test_case_reader import IntentsTestReader
from ..utils.agent.agent import Agent
from ..utils.bot.bot import Bot
from ..utils.entity.open_switch import Switches
from ..utils.log import LOG
from ..utils.seeting.setting import Setting

import os
import sys
dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(dir)


class Evaluate(object):
    """
    @info:评测逻辑
    """

    def __init__(self, config, module, if_auto_run):
        self.config = config
        self.module = module
        self.if_auto_run = if_auto_run

    def prepareAgent(self, agent_file_path, if_create_bot):
        """
        准备数据
        :return:
        """
        # 导入agent
        agent = Agent(self.config)
        importAgentName = agent.getImportAgentName(agent_file_path)
        rootRes = agent.checkAgentIsExist(importAgentName, 1)
        if rootRes is None:
            agent.importAgent(importAgentName, agent_file_path)
            rootRes = agent.checkAgentIsExist(importAgentName, 10)

        self.config['authorization'] = rootRes["developerAccessToken"]
        self.config['agentId'] = rootRes["id"]
        bot = Bot(self.config)
        if if_create_bot == "true":
            # 创建bot
            # botName = "我是自动测评要用的bot"
            botName = self.config['bot_name']
            existBotInfo = bot.checkBotIsExist(botName)
            if None is existBotInfo:
                self.config['botToken'] = "NGD %s" % bot.createBot(botName)
            else:
                self.config['botToken'] = "NGD %s" % existBotInfo
            LOG.info("botToken:%s" % self.config['botToken'])

        if self.module == "intents":
            # 修改置信度
            setting = Setting(self.config)
            setting.setting()

        # if self.module == "tableqa":
        #     botName = "表格问答bot"
        #     existBotInfo = bot.checkBotIsExist(botName)
        #     if None is existBotInfo:
        #         self.config.botToken = "NGD %s" % bot.createTableBot(botName)
        #     else:
        #         self.config.botToken = "NGD %s" % existBotInfo
        #     LOG.info("botToken:%s" % self.config.botToken)

    # def prepareCompareAgent(self):
    #     """
    #     准备数据
    #     :return:
    #     """
    #     # 导入agent
    #     agent = Agent(self.config)
    #     importAgentName = agent.getImportAgentName(self.config.compare_agent_file_path)
    #     print("importAgentName: " + importAgentName)
    #     rootRes = agent.checkAgentIsExist(importAgentName, 1)
    #     if rootRes is None:
    #         agent.importAgent(importAgentName, self.config.compare_agent_file_path)
    #         rootRes = agent.checkAgentIsExist(importAgentName, 10)
    #
    #     self.config.authorization = rootRes["developerAccessToken"]
    #     self.config.agentId = rootRes["id"]
    #
    #     # 获取bot的token
    #     bot = Bot(self.config)
    #     existBotInfo = bot.checkBotIsExist(self.config.compare_bot_name)
    #     self.config.botToken = "NGD %s" % existBotInfo
    #     LOG.info("botToken:%s" % self.config.botToken)
    #
    # def prepareTableqaAgent(self):
    #     """
    #     准备数据
    #     :return:
    #     """
    #     # 导入agent
    #     agent = Agent(self.config)
    #     importAgentName = agent.getImportAgentName(self.config.tableqa_agent_file_path)
    #     rootRes = agent.checkAgentIsExist(importAgentName, 1)
    #     if rootRes is None:
    #         agent.importAgent(importAgentName, self.config.tableqa_agent_file_path)
    #         rootRes = agent.checkAgentIsExist(importAgentName, 10)
    #
    #     self.config.authorization = rootRes["developerAccessToken"]
    #     self.config.agentId = rootRes["id"]
    #     # 创建bot
    #     botName = "我是自动测评tableqa要用的bot"
    #     bot = Bot(self.config)
    #     existBotInfo = bot.checkBotIsExist(botName)
    #     if None is existBotInfo:
    #         self.config.botToken = "NGD %s" % bot.createTableBot(botName)
    #     else:
    #         self.config.botToken = "NGD %s" % existBotInfo
    #     LOG.info("botToken:%s" % self.config.botToken)

    def intentEvaluate(self, config, file, module, model, agentFlag):
        """
        意图评测
        :param file:
        :return:
        """
        reader = IntentsTestReader(file)
        test_case_list = reader.read()
        intent_query = IntentsQuery(config)
        MulitIntents(test_case_list, intent_query, module, config, model, agentFlag)

    def sys_entity_evaluate(self, module):
        """
        先开开关，然后再进行系统实体的评测
        :param module:
        :return:
        """
        open_s = Switches(self.config)
        open_s.open_switches()
        time.sleep(2)
        reader = EntityTestReader(self.config['entity_file'])
        test_case_list = reader.read()
        entity_switch = entity_evaluate.entity_evaluate(test_case_list, self.config, module)
        LOG.info(entity_switch)
