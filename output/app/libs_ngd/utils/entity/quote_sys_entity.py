#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/04/23 17:23:06
"""
from ..http_request import HttpRequest
from ..log import LOG


class Quote(object):
    """
    引用系统实体
    """
    def __init__(self, conf):
        """
        init
        """
        self.agentId = conf['agentId']
        self.conf = conf

    def new_user_entity(self):
        """
        创建用户实体
        :return:
        """
        # 如果用户实体列表不为空，先删除用户实体
        api = "/api/v2/entities?agentId=" + self.agentId + "&pn=1&ps=50&pageSize=50&permCode=entity:w"
        res = HttpRequest.get(api, self.conf)
        total = int(res['data']['total'])
        while(total != 0):
            self.delete_user_entity(total - 1)
            total = total - 1
        new_user_entity_api = "/api/v2/entities/user_entity/create?agentId=" + self.agentId + "&permCode=entity:w"
        payload = {'alias': "", 'name': "entity", 'nameZh': "引用系统实体", 'type': 0}
        # LOG.info('json : %s' % json)
        res_new_user_entity = HttpRequest.post(new_user_entity_api, payload, self.conf)
        self.checkCreateResult(res_new_user_entity)

    def quote_sys_entity(self):
        """
        引用系统实体
        :param entity_id:
        :return:
        """
        # 获取用户实体id
        api = "/api/v2/entities?agentId=" + self.agentId + "&pn=1&ps=50&pageSize=50"
        res = HttpRequest.get(api, self.conf)
        self.checkGetList(res)
        entityId = res['data']['list'][0]['id']
        sysEntityRelVOList = []
        # 获取系统实体列表
        sys_entity_api = "/api/v2/entities/sys_entity?permCode=entity:w&referer=entity%2Fsys&agentId=" + self.agentId \
                         + "&pn=1&ps=50&pageSize=50&permCode=entity:w"
        sys_res = HttpRequest.get(sys_entity_api, self.conf)

        # 用户实体引用系统实体
        quote_sys_entity_api = "/api/v2/entities/sys_entity/rel/update?agentId=" + self.agentId
        for i in range(int(sys_res['data']['total'])):
            sysentity_description = sys_res['data']['list'][i]['description']
            sysentity_id = sys_res['data']['list'][i]['sysEntityId']
            sysentity_name = sys_res['data']['list'][i]['name']
            sysentity_nameZh = sys_res['data']['list'][i]['nameZh']
            json_last = {"Effect": 0, "addStatus": "true", "agentId": self.agentId,
                         "description": sysentity_description, "entityId": entityId, "entityType": 0,
                         "hasActiveCopy": "false", "sysEntityId": sysentity_id,
                         "sysEntityNameEn": sysentity_name, "sysEntityNameZh": sysentity_nameZh}
            sysEntityRelVOList.append(json_last)
            json = {'entityId': entityId, "sysEntityRelVOList": sysEntityRelVOList}
            # LOG.info('json : %s' % json)
            res_quote_switch = HttpRequest.post(quote_sys_entity_api, json, self.conf)
            self.checkQuoteResult(res_quote_switch)
            LOG.info("result is : %s" % res_quote_switch)

    def delete_user_entity(self, i):
        """
        删除用户实体
        :return:
        """
        api = "/api/v2/entities?agentId=" + self.agentId + "&pn=1&ps=50&pageSize=50&permCode=entity:w"
        res = HttpRequest.get(api, self.conf)
        self.checkGetList(res)
        entityId = res['data']['list'][i]['id']
        delete_user_entity_api = "/api/v2/entities/user_entity/delete?agentId=" \
                                 + self.agentId + "&permCode=entity:w"
        json = {"id": entityId}
        LOG.info('json : %s' % json)
        res_delete_user_entity = HttpRequest.post(delete_user_entity_api, json, self.conf)
        self.checkDeleteResult(res_delete_user_entity)

    def checkCreateResult(self, res):
        """
        :param res:
        :return:
        """
        LOG.info(res)
        assert res['code'] == 4002801 or res['code'] == 4000109
        assert res['msg'] == u"新建实体成功" or res['msg'] == u"同名实体ID已存在"

    def checkQuoteResult(self, res):
        """
        :param res:
        :return:
        """
        LOG.info(res)
        assert res['code'] == 200
        assert res['msg'] == "OK"

    def checkGetList(self, res):
        """
        checkGetList
        Args:
            res:

        Returns:

        """
        LOG.info(res)
        assert res['code'] == 200
        assert res['msg'] == "OK"
        assert res['data']['list'] is not None

    def checkDeleteResult(self, res):
        """
        :param res:
        :return:
        """
        LOG.info(res)
        assert res['code'] == 4002801
        assert res['msg'] == u"删除实体成功"