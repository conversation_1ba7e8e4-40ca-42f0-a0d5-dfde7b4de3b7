#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide http request.

Authors: <AUTHORS>
Date:    2019/09/24 11:04:06
"""
import time

from http_request import HttpRequest
from log import LOG


class NluRecognize(object):
    """
    @info:封装http请求方法
    """

    @classmethod
    def nlu_recognize(cls, query, conf):
        """
        @info: 封装get方法
        :param query: nlu分析的query
        :return: response内容或者None
        """

        api = "/api/v2/nlu/recognize?agentId=" + conf['agentId']
        json_nlu = {'query': query}
        LOG.info(json_nlu)
        flag = True
        while flag:
            try:
                res = HttpRequest.post(api, json_nlu, conf)
                flag = False
                return res
            except Exception:
                time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])

    @classmethod
    def nlu_faq_recognize(cls, query, conf):
        """
        @info: 封装get方法
        :param query: nlu分析的query
        :return: response内容或者None
        """

        api = "/api/v2/nlu/faq/recognize?agentId=" + conf['agentId']
        json_nlu = {'query': query}
        LOG.info(json_nlu)
        flag = True
        while flag:
            try:
                res = HttpRequest.post(api, json_nlu, conf)
                flag = False
                return res
            except Exception:
                # time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])

    @classmethod
    def nlu_chitchat_recognize(cls, query, conf):
        """
        @info: 封装get方法
        :param query: nlu分析的query
        :return: response内容或者None
        """

        api = "/api/v2/nlu/chat/recognize?agentId=" + conf['agentId']
        json_nlu = {'query': query}
        LOG.info(json_nlu)
        flag = True
        while flag:
            try:
                res = HttpRequest.post(api, json_nlu, conf)
                flag = False
                return res
            except Exception:
                # time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])

    @classmethod
    def nlu_tableqa_recognize(cls, query, conf):
        """
        @info: 封装get方法
        :param query: nlu分析的query
        :return: response内容或者None
        """

        api = "/api/v2/tableqa/nlu?agentId=" + conf['agentId']
        json_nlu = {'query': query}
        LOG.info(json_nlu)
        flag = True
        while flag:
            try:
                res = HttpRequest.post(api, json_nlu, conf)
                flag = False
                return res
            except Exception:
                time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])
