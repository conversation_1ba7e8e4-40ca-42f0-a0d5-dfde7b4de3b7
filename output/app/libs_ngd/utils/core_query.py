#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide http request.

Authors: <AUTHORS>
Date:    2020/04/09 22:04:06
"""
import json
import time

import chardet
import requests

from log import LOG


class CoreQuery(object):
    """
    @info:封装http请求方法
    """

    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        print '-----------1111111----------------'
        print conf
        self.url = conf['core_address'].encode('utf-8')
        self.suthorization = conf['botToken'].encode('utf-8')
        self.agentId = conf['agentId']
        self.conf = conf
        # 大模型专用
        # 对应botId字段
        self.large_bot_id = conf.get('bot_id').encode('utf-8') if conf.get('bot_id') else ""
        # 对应可以直接复制的Token字段
        self.large_authorization = self.suthorization

    def core_query(self, query, sessionId="71a43ac0-e77e-4d25-b075-18acc22c6006"):
        """
        :param query: core/query的query
        :return: response内容或者None
        """
        # 5.6
        # core_query_url = self.url + "/api/v2/core/query?version=20180715"
        # 6.1
        core_query_url = self.url + "/core/v3/query?debug=true&agentId=" + self.agentId
        LOG.info(core_query_url)
        # 5.6
        # request_data = {"queryText": query, "sessionId": "71a43ac0-e77e-4d25-b075-18acc22c6006",
        #                "collectConversation": True}
        # 6.1
        request_data = {"queryText": query, "sessionId": sessionId,
                        "collect": True}
        LOG.info(request_data)
        LOG.info(self.suthorization)
        headers = {"Authorization": self.suthorization, "Content-Type": "application/json", "active": "offline"}
        LOG.info(headers)
        flag = True
        data = None
        num = 0
        while flag and num < 3:
            try:
                res = requests.post(core_query_url, headers=headers, json=request_data)
                flag = False
                # LOG.info(res.json())
                if res.status_code != 200:
                    print res.text
                #     print("\n")
                #     print(res['code'])
                #     print(res['msg'])
                return res
            except Exception as e:
                print e
                # time.sleep(2)
                print(u"\nlose connection")
            num = num + 1
            print "当前num:" + str(num)

        # data = res['data']
        if data is None:
            return None

    def core_query_large_model(self, query, sessionId="71a43ac0-e77e-4d25-b075-18acc22c6006"):
        """
        大模型版本core query接口
        :param query:
        :param sessionId:
        :return:
        """
        """
        {   
            "queryText":"你吃饭了吗",
            "sessionId":"b0196210-e0c4-45b6-ac17-4676d9b32653",
            "collect":false,
            "sources":null,
            "vad":null,
            "botId":"8f7c3860-cb5a-4489-bf6a-398bba892292",
            "Authorization":"NGD 0556778e-e142-4b72-9ee6-c3226085aa4e",
            "channel":""
        }
        """
        core_query_url = self.url + "/core/v4/stream/query?debug=true&agentId=" + self.agentId
        print core_query_url
        LOG.info(core_query_url)
        request_data = {
            "queryText": query,
            "sessionId": sessionId,
            "collect": False,
            "sources": None,
            "vad": None,
            "Authorization": self.large_authorization, # 对应可以直接复制的Token字段
            "channel": "",
            "botId": self.large_bot_id  # 对应botId字段
        }
        print request_data
        LOG.info(request_data)
        LOG.info(self.suthorization)
        headers = {"Authorization": self.large_authorization, "Content-Type": "application/json"}
        LOG.info(headers)
        for i in range(3):
            # 尝试请求3次，三次不通，返回None
            res = requests.post(core_query_url, headers=headers, json=request_data)
            if res.status_code != 200:
                LOG.info('请求第' + str(i + 1) + '次，状态码为：' + str(res.status_code))
                # print res.text
            else:
                return res
        return None

    def core_query_large_model_v8(self, query, sessionId="71a43ac0-e77e-4d25-b075-18acc22c6006"):
        """
        8.0版本core query接口
        :param query:
        :param sessionId:
        :return:
        """
        """
        body
        {
            "queryText": "建立健全问题响应机制具体指的什么",
            "sessionId": "*************-4a5c-92ad-c468dab24bc8",
            "agentId": "3ab1515e-c53f-41de-9b62-6653abf20630",
            "variables": {}
        }
        header
        {
            "token": "e98a6e22-5c73-4816-ab6b-0e29cc58fc0e",
            "Content-Type": "application/json"
        }
        """
        core_query_url = self.url + "/core/v5/stream/query"
        request_data = {
            "queryText": query,
            "sessionId": sessionId,
            "agentId": self.agentId,
            "variables": {}
        }
        headers = {
            "token": self.suthorization,
            "Content-Type": "application/json"
        }
        print "请求8.0接口"
        print "URL为" + core_query_url
        print "bady为" + str(request_data)
        print "headers为" + str(headers)
        for i in range(3):
            # 尝试请求3次，三次不通，返回None
            res = requests.post(core_query_url, headers=headers, json=request_data)
            if res.status_code != 200:
                print '请求第' + str(i + 1) + '次，状态码为：' + str(res.status_code)
                # print res.text
            else:
                if 'charset=utf-8' in res.headers.get('content-type', '').lower():
                    res.encoding = 'utf-8'
                else:
                    # 你可以尝试其他编码，如果你知道服务器可能使用的编码
                    res.encoding = res.apparent_encoding
                return res
        return None




if __name__ == '__main__':
    """
    INSERT INTO env (id, version, env_name, backend_address, core_address, username, uid, account_type, bot_name, no_answer, clarify_answer, agentId, botToken, authorization, nlu_enable, thread_num, user_name) VALUES 
    (2, 'ngdv730', 'ngdv730', 'https://ics.bce.baidu.com/ngd/', 'https://ics.bce.baidu.com/ngd/', 'user1', 't1000000001', 'fake', '大模型测试',
     '抱歉,我不太理解您的意思', '请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。', 'fcca60e7-43ad-4d30-bf62-cb7a69e4342c', 'NGD 0556778e-e142-4b72-9ee6-c3226085aa4e', '0556778e-e142-4b72-9ee6-c3226085aa4e', 0, 5, 'zhangjiabin01');

    """
    # conf = {
    #         'id': 2,
    #         'version': 'ngdv730',
    #         'env_name': 'ngdv730',
    #         'backend_address': 'http://**************:8735',
    #         'core_address': 'http://**************:8733',
    #         'username': 'user1',
    #         'uid': 't1000000001',
    #         'account_type': 'fake',
    #         'bot_name': 'mind效果',
    #         'no_answer': '抱歉,我不太理解您的意思',
    #         'clarify_answer': '请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。',
    #         'agentId': '26dde01c-64ac-400b-b210-527dc0ce3d74',
    #         'botToken': '9dc2a560-b0a6-4538-b44f-011a47a8ac9c',
    #         'authorization': 'NGD aa90c964-3e85-4f35-99cb-471212de5165',
    #         'nlu_enable': 0,
    #         'thread_num': 5,
    #         'user_name': 'zhangjiabin01'
    #     }
    # core = CoreQuery(conf)
    # print core.core_query_large_model("今天吃什么").text
    conf = {
        "agentId": "3ab1515e-c53f-41de-9b62-6653abf20630",
        "botToken": "e98a6e22-5c73-4816-ab6b-0e29cc58fc0e",
        "core_address": "https://keyue.cloud.baidu.com",
    }
    core = CoreQuery(conf)
    res = core.core_query_large_model_v8("建立健全问题响应机制具体指的什么").text
    def remove_data_prefix(s):
        """
        去掉data:前缀
        :param s:
        :return:
        """
        if s.startswith("data:"):
            return s[5:]  # 跳过 "data:" 这5个字符
        return s  # 如果字符串不以 "data:" 开头，则返回原始字符串
    res_list = map(remove_data_prefix, filter(lambda x: x, res.split('\n')))
    model_res = ''
    for item in res_list:
        cur_text = json.loads(item).get('answer')[0].get('reply').get('text')
        model_res += cur_text
    print model_res



    # import requests
    # import json
    #
    # url = "https://keyue.cloud.baidu.com/core/v5/stream/query"
    #
    # payload = json.dumps({
    #     "queryText": "建立健全问题响应机制具体指的什么",
    #     "sessionId": "*************-4a5c-92ad-c468dab24bc8",
    #     "agentId": "3ab1515e-c53f-41de-9b62-6653abf20630",
    #     "variables": {}
    # })
    # headers = {
    #     'Connection': 'keep-alive',
    #     'content-type': 'application/json',
    #     'token': 'e98a6e22-5c73-4816-ab6b-0e29cc58fc0e',
    #     'Cookie': 'BAIDUID=16E406688F0E93668C9C0FC3410566C2:FG=1; BAIDUID_BFESS=16E406688F0E93668C9C0FC3410566C2:FG=1'
    # }
    #
    # response = requests.request("POST", url, headers=headers, data=payload)
    # # 检查并设置正确的编码
    # if 'charset=utf-8' in response.headers.get('content-type', '').lower():
    #     response.encoding = 'utf-8'
    # else:
    #     # 你可以尝试其他编码，如果你知道服务器可能使用的编码
    #     response.encoding = response.apparent_encoding
    #
    # print(response.text)




