#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
import os
import random
import time

from ..http_request import HttpRequest
from ..log import LOG


class Train(object):
    """
    打开开关
    """

    def __init__(self, conf):
        """
        init
        """
        self.agentId = conf['agentId']
        self.conf = conf

    def train(self, desc):
        """
        Turn on all switches
        :return:
        """
        # 先检查是否已经存在训练模型
        trainRes = self.checkSpecialTrain(self.checkTrainList(desc), desc)
        if trainRes == "训练中":
            return "训练中"
        else:
            if not trainRes:
                LOG.info("发起训练了：")
                api = "/api/v2/model/train?agentId=" + self.agentId
                LOG.info(desc)
                # LOG.info(desc == "卷积深度模型")
                if desc == "卷积深度模型":
                    json = {"algorithmConfig": {"useFaq": False, "augment": False, "balance": False, "numPasses": 10,
                                                "batchSize": 8, "regularization": 0.0001, "learningRate": 0.001,
                                                "learningMethod": "MiniBatchSGDUpdater", "modelType": "cnn"},
                            "augment": False, "balance": False, "useFaq": False, "desc": desc,
                            "source": "ngdintentTrain"}
                elif desc == "时序深度模型":
                    json = {"algorithmConfig": {"useFaq": False, "augment": False, "balance": False, "numPasses": 10,
                                                "batchSize": 8, "regularization": 0.0001, "learningRate": 0.001,
                                                "learningMethod": "MiniBatchSGDUpdater", "modelType": "sam"},
                            "augment": False, "balance": False, "useFaq": False, "desc": desc,
                            "source": "ngdintentTrain"}
                elif desc == "蒸馏深度模型":
                    json = {"algorithmConfig": {"useFaq": False, "augment": False, "balance": False, "numPasses": 10,
                                                "batchSize": 8, "regularization": 0.0001, "learningRate": 0.001,
                                                "learningMethod": "MiniBatchSGDUpdater", "modelType": "distill"},
                            "augment": False, "balance": False, "useFaq": False, "desc": desc,
                            "source": "ngdintentTrain"}
                elif desc == "小样本融合模型":
                    json = {"source": "fewshotTrain",
                            "algorithmConfig": {"useFaq": False, "augment": False, "balance": False, "numPasses": 20,
                                                "batchSize": 8, "regularization": 0.0001, "learningRate": 0.001,
                                                "learningMethod": "MiniBatchSGDUpdater",
                                                "modelType": "intentSmallSample"}, "augment": False, "balance": False,
                            "useFaq": False, "desc": desc, "remark": desc}
                else:
                    json = {
                        "algorithmConfig": {"useFaq": False, "augment": False, "balance": False, "numPasses": 10,
                                            "batchSize": 8,
                                            "regularization": 0.0001, "learningRate": 0.001,
                                            "learningMethod": "MiniBatchSGDUpdater", "modelType": "lr"},
                        "augment": False,
                        "balance": False, "useFaq": False, "desc": desc, "source": "ngdintentTrain"}

                try:
                    res = HttpRequest.post(api, json, self.conf)
                    # res = requests.post(api, json, self.conf)
                    LOG.info("create train result：%s" % res)
                    print "发起训练，modelId:"
                    # print res.json()
                    # print res.text
                    print res['data']['modelId']
                except Exception as e:
                    time.sleep(2)
                    print("出现如下异常%s" % e)

                if res['code'] != 200:
                    print("\n")
                    print(res['code'])
                    print(res['msg'])
                    raise Exception("setting change error {}".format(res["msg"]))

                if self.checkTrainResultNew(desc, modelId=res['data']['modelId']) and self.deployTestEnv(desc):
                    return True
                else:
                    return False
        print "已有训练好的模型，无需训练，查看是否部署成功"
        trainlist = self.checkTrainList(desc)
        print trainlist
        if self.checkIfDeployTestEnv(trainlist, desc):
            print "部署成功了"
            return True
        else:
            print "还没有部署"
            return self.deployTestEnv(desc)

    def deployTestEnv(self, desc):
        """
        部署测试环境
        :param modelId:
        :return:
        """
        # 先找到modelId
        trainlist = self.checkTrainList(desc)
        # print trainlist
        for data in trainlist['data']['list']:
            print "查看：" + data['modelId']
            if data['remark'] == desc and data['trainStatus'] == "训练成功" and data['trainStatusCode'] == 0:
                # 再生效操作
                api = "/api/v2/deploy/doDeploy?business=IR&modelId=%s&agentId=%s" % (
                    data['modelId'], self.conf['agentId'])
                json = {"toggleStatus": 0}
                try:
                    res = HttpRequest.post(api, json, self.conf)
                    LOG.info("deploy model result：%s" % res)
                except Exception as e:
                    time.sleep(2)
                    print("出现如下异常%s" % e)

                if res['code'] != 200:
                    print("\n")
                    print(res['code'])
                    print(res['msg'])
                    raise Exception(u"deploy model error {}".format(res["msg"]).decode("unicode-escape"))

                flag = 0
                while flag < 20:
                    if self.checkIfDeployTestEnv(self.checkTrainList(desc), desc):
                        LOG.info("测试环境生效成功")
                        return True
                    flag += 1
                    time.sleep(2)
                LOG.info("测试环境生效失败")
                return False
            # return False
        return False

    def checkTrainList(self, desc):
        """
        检查模型训练的列表
        :param desc:
        :return:
        """
        api = "/api/v2/model/models?pn=1&remark=%s&t=1604375354710_320364&ps=10&agentId=%s" % (desc, self.agentId)
        try:
            res = HttpRequest.get(api, self.conf)
            return res
        except Exception as e:
            time.sleep(2)
            print("出现如下异常%s" % e)

    def checkSpecialTrain(self, res, desc):
        """
        从训练列表中检查是否已经存在，不存在时再进行训练
        :param res:
        :param desc:
        :return:
        """
        print "训练列表list："
        print res
        for data in res['data']['list']:
            if data['remark'] == desc and data['trainStatus'] == "训练成功" and data['trainStatusCode'] == 0:
                LOG.info("{} 训练成功".format(data['modelId']))
                return True
            if data['remark'] == desc and data['trainStatus'] == "训练失败" and data['trainStatusCode'] == -1:
                LOG.info("{} 训练失败".format(data['modelId']))
                # return "训练失败"
            if data['remark'] == desc and data['trainStatus'] == "训练中" and data['trainStatusCode'] == 1:
                LOG.info("{} 训练中".format(data['modelId']))
                return "训练中"
            if data['remark'] == desc and data['trainStatus'] == "排队中" and data['trainStatusCode'] == -2:
                LOG.info("{} 排队中".format(data['modelId']))
                return "排队中"
        return False

    def checkSpecialTrainNew(self, res, desc, modelId):
        """
        从训练列表中检查是否已经存在，不存在时再进行训练
        :param res:
        :param desc:
        :return:
        """
        flag = 0
        for data in res['data']['list']:

            if data['modelId'] == modelId:
                LOG.info("checkSpecialTrainNew")
                LOG.info(modelId)
                if data['remark'] == desc and data['trainStatus'] == "训练成功" and data['trainStatusCode'] == 0:
                    LOG.info("训练成功")
                    flag = 1
                    return True
                if data['remark'].encode("utf-8") == desc and data['trainStatus'].encode("utf-8") == "训练失败" \
                        and data['trainStatusCode'] == -1:
                    LOG.info("新发起的 modelId {} 训练失败".format(modelId))
                    return "训练失败"
        return False

    def checkIfDeployTestEnv(self, res, desc):
        """
        从训练列表中检查是否已经测试生效
        :param res:
        :param desc:
        :return:
        """
        print "检查模型的训练以及部署状态"
        for data in res['data']['list']:
            if data['remark'] == desc and data['trainStatus'] == "训练成功" and data['deployStatus'] == "测试生效":
                LOG.info("测试环境已生效")
                return True
        return False

    def checkTrainResult(self, desc):
        """
        开始训练
        :param desc:
        :return:
        """
        LOG.info("开始训练，请稍等～～")
        res = self.checkTrainList(desc)
        flag = 0
        while flag < 200:
            checkRes = self.checkSpecialTrain(res, desc)
            if checkRes:
                LOG.info("训练成功")
                return True
            flag += 1
            time.sleep(3)
            res = self.checkTrainList(desc)
        LOG.info("训练失败")
        return False

    def checkTrainResultNew(self, desc, modelId):
        """
        开始训练
        :param desc:
        :return:
        """
        LOG.info("开始训练，请稍等～～")
        res = self.checkTrainList(desc)
        flag = 0
        while flag < 30:
            checkRes = self.checkSpecialTrainNew(res, desc, modelId)
            LOG.info("新发起的模型训练结果～～" + str(checkRes))
            if checkRes == "训练失败":
                return False
            elif checkRes:
                LOG.info("训练成功")
                return True
            else:
                flag += 1
                time.sleep(30)
                res = self.checkTrainList(desc)
        LOG.info("训练状态不是 成功，也不是 失败，请排查")
        return False

    def faqTrain(self, desc):
        """
        faqTrain
        :param desc:
        :return:
        """
        input = {"remark": "问答模型", "source": "faq", "algorithmConfig": {"numPasses": 10, "batchSize": 64}}
        try:
            if self.checkFaqTrainResult("问答模型"):
                pass
            else:
                api = "/api/v2/model/faq/train?business=FAQ&agentId=" + self.agentId
                res = HttpRequest.post(api, input, self.conf)
                LOG.info("create faq train result：%s" % res)
                if res['code'] != 200:
                    print("\n")
                    print(res['code'])
                    print(res['msg'])
                    raise Exception("setting change error {}".format(res["msg"]))
        except Exception as e:
            time.sleep(2)
            print("出现如下异常%s" % e)

        if self.checkFaqTrainResult("问答模型") == "训练失败":
            return False, "训练失败"
        elif self.checkFaqTrainResult("问答模型") and self.deployFaqModelTestEnv("问答模型"):
            return True, "部署成功"
        else:
            return False, "模型训练未成功，请排查"

    def faqTrainSeven(self, desc):
        """
        faqTrain-711版本
        :param desc:
        :return:
        """
        remark = ""
        if desc == "通用排序模型":
            # remark = desc + str(random.randint(10, 99))
            remark = desc
            input = {"remark": remark, "source": "ngdfaqTrain",
                     "algorithmConfig": {"numPasses": 10, "batchSize": 64, "augment": "false", "balance": "false",
                                         "learningMethod": "MiniBatchSGDUpdater", "learningRate": 0.001,
                                         "modelType": "pointwise", "regularization": 0.0001, "useFaq": "false"}}
        else:
            remark = desc
            input = {"remark": remark, "source": "ngdfaqTrain",
                     "algorithmConfig": {"numPasses": 10, "batchSize": 64, "augment": "false", "balance": "false",
                                         "learningMethod": "MiniBatchSGDUpdater", "learningRate": 0.001,
                                         "modelType": "listwise", "regularization": 0.0001, "useFaq": "false"}}
        res = {}
        try:
            if self.checkFaqTrainResult(remark):
                print "faq训练结果 不是False"
                pass
            else:
                api = "/api/v2/model/faq/train?business=FAQ&agentId=" + self.agentId
                res = HttpRequest.post(api, input, self.conf)
                LOG.info("ngdv711:create faq train result：%s" % res)
                if res['code'] != 200:
                    print("\n")
                    print(res['code'])
                    print(res['msg'])
                    raise Exception("setting change error {}".format(res["msg"]))

        except Exception as e:
            time.sleep(2)
            print("出现如下异常%s" % e)
            return False, res["msg"]

        if self.checkFaqTrainResult(remark) == "训练失败":
            print "train failed"
            return False, "train failed"
        elif self.checkFaqTrainResult(remark) and self.deployFaqModelTestEnv(remark):
            return True, "部署成功"
        else:
            return False, "tain failed or deploy failed"

    def deployFaqModelTestEnv(self, desc):
        """
        部署问答模型到测试环境
        :param modelId:
        :return:
        """
        # 先找到modelId
        print "开始部署faq模型:" + desc
        trainlist = self.checkFaqTrainList(desc)
        print trainlist
        for data in trainlist['data']['list']:
            try:
                if data['remark'] == desc and data['trainStatus'] == "训练成功" and data['trainStatusCode'] == 0:
                    api = "/api/v2/deploy/faq/doDeploy?business=FAQ&modelId=%s&agentId=%s" % (
                        data['modelId'], self.conf['agentId'])
                    json = {"toggleStatus": 0}

                    res = HttpRequest.post(api, json, self.conf)
                    LOG.info("deploy model result：%s" % res)
                    if res['code'] != 200:
                        print("\n")
                        print(res['code'])
                        print(res['msg'])
                        raise Exception("deploy model error {}".format(res["msg"]))
                    break
            except Exception as e:
                time.sleep(2)
                print("出现如下异常%s" % e)

        flag = 0
        while flag < 20:
            if self.checkIfDeployTestEnv(self.checkFaqTrainList(desc), desc):
                LOG.info("测试环境生效成功")
                return True
            flag += 1
            time.sleep(2)
        LOG.info("测试环境生效失败")
        return False
        # return False
        # return False

    def checkFaqTrainList(self, desc):
        """
        检查模型训练的列表
        :param desc:
        :return:
        """
        api = "/api/v2/model/faq/models?remark=%s&t=1640075545844_450984&" \
              "agentId=%s&pn=1&ps=10&pageSize=10&business=FAQ" % (
                  desc, self.agentId)
        try:
            res = HttpRequest.get(api, self.conf)
            return res
        except Exception as e:
            time.sleep(2)
            print("出现如下异常%s" % e)

    def checkFaqTrainResult(self, desc):
        """
        checkFaqTrainResult
        :param desc:
        :return:
        """
        LOG.info("查看是否有训练任务，请稍等～～")
        res = self.checkFaqTrainList(desc)
        flag = 0
        while flag < 35:
            checkRes = self.checkSpecialTrain(res, desc)
            if checkRes == "训练失败":
                LOG.info("训练失败")
                return "训练失败"
            elif checkRes is True:
                LOG.info("训练成功")
                return True
            elif checkRes is False:
                return False
            flag += 1
            time.sleep(60)
            res = self.checkFaqTrainList(desc)

        return False
