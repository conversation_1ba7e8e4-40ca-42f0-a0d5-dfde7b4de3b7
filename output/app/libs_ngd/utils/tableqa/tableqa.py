#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2022/11/15 17:00:00
"""
import time

from ..http_request import HttpRequest


class Tableqa(object):
    """
    init
    """

    def __init__(self, conf):
        """
        init
        :param conf:
        """
        self.conf = conf

    def trainTableqaModel(self):
        """
        :return:
        """
        agentId = self.conf['agentId']
        # 获取类目列表，拿到categoryId
        api = "/api/v2/tableqa/category/all?permCode=tableqa_manage:w&agentId=" + agentId
        res = HttpRequest.get(api, self.conf)
        for i in range(len(res["data"])):
            categoryId = res["data"][i]["id"]
            self.train(agentId, categoryId)

    def train(self, agentId, categoryId):
        """
        :param agentId:
        :param categoryId:
        :return:
        """
        models_api = "/api/v2/model/models?remark=模型&business=TABLEQA&agentId=" + agentId + "&categoryId=" + categoryId
        models_res = HttpRequest.get(models_api, self.conf)
        if len(models_res["data"]["list"]) == 0:
            # 发起模型训练
            api = "/api/v2/model/train/submit?business=TABLEQA&agentId=" + agentId
            json = {"remark": "模型", "categoryId": categoryId, "source": "训练源", "business": "TABLEQA"}
            res = HttpRequest.post(api, json, self.conf)
            print("====表格训练结果=%s" % res)
            # 获取模型列表
            models_res = HttpRequest.get(models_api, self.conf)
            resultList = models_res["data"]["list"]
            flag = 0
            print(len(resultList))
            print(resultList[0]["deployStatus"])
            while ((len(resultList) == 0) | (resultList[0]["deployStatus"] != "测试生效")) & (flag < 120):
                print(resultList[0]["deployStatus"])
                print("训练中，等待 %s s" % flag)
                time.sleep(1)
                models_res = HttpRequest.get(models_api, self.conf)
                print("==models_res")
                print(models_res)
                resultList = models_res["data"]["list"]
                flag += 1
