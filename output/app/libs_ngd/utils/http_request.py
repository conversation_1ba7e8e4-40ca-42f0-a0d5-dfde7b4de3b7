#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
import json
import time

import requests

from log import LOG


class HttpRequest(object):
    """
    @info:封装http请求方法
    """

    @classmethod
    def get(cls, api, conf):
        """
        @info: 封装get方法
        :param api string, 接口地址,
        :return: response内容或者None
        """

        send_url = conf['backend_address'] + api
        LOG.info("send_url:%s" % send_url)
        LOG.info("header:%s" % cls.get_header(conf))
        flag = True
        while flag:
            try:
                req = requests.get(send_url, headers=cls.get_header(conf))
                res = req.json()
                flag = False
                return res
            except Exception as e:
                time.sleep(2)
                print("出现如下异常%s" % e)

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])

    @classmethod
    def post(cls, api, content, conf):
        """
        @info: 封装post方法
        :param content:
        :param api:
        :return: response内容或者None
        """
        send_url = conf['backend_address'] + api
        LOG.info("send_url:%s" % send_url)
        LOG.info("看下post的header")
        LOG.info(cls.get_header(conf))
        LOG.info(json.dumps(content))
        flag = True
        while flag:
            try:
                req = requests.post(send_url, data=json.dumps(content), headers=cls.get_header(conf))
                res = req.json()
                # LOG.info(res)
                flag = False
                return res
            except Exception as e:
                time.sleep(2)
                print("出现如下异常%s" % e)

    @classmethod
    def postfile1(cls, api, filepath, filename, post_data, conf):
        """docstring for post_report.

        Args:
            kwargs:
                _file_path: the report path
                _report_name:  the report name
                _post_data: post form-data

        Returns:
            the result of the post request
        Raises:
        :param post_data:
        :param filepath:
        :param filename:
        :param api:
        """
        send_url = conf['backend_address'] + api
        LOG.info("send_url:%s" % send_url)
        # files = {'file': (filename, open(filepath, 'rb'), 'application/x-gzip', {'Expires': '0'})}
        files = {'file': (filename, open(filepath, 'rb'), 'multipart/form-data', {'Expires': '0'})}
        data = post_data
        flag = True
        while flag:
            try:
                req = requests.post(send_url, files=files, data=data, headers=cls.get_header(conf))
                res = req.json()
                LOG.info(req.text)
                flag = False
                return res
            except Exception as e:
                time.sleep(2)
                print("出现如下异常%s" % e)

    @classmethod
    def postfile2(self, api, filepath, filename, conf):
        """

        :param api:
        :param filepath:
        :param filename:
        :param conf:
        :return:
        """
        LOG.info("start post file!")
        send_url = conf['backend_address'] + api
        LOG.info("send_url:" + send_url)
        with open(filepath, "rb") as a_file:
            file_dict = {"files": a_file}
            response = requests.post(send_url, files=file_dict, headers=self.get_header_2(conf))
            res = response.json()
            print("result:" + response.text)
            return res

    @classmethod
    def postfile(cls, api, filepath, conf):
        """docstring for post_report.

        Args:
            kwargs:
                _file_path: the report path
                _report_name:  the report name
                _post_data: post form-data

        Returns:
            the result of the post request
        Raises:
        :param post_data:
        :param filepath:
        :param filename:
        :param api:
        """
        send_url = conf['backend_address'] + api
        LOG.info("send_url:%s" % send_url)
        # 显式地设置文件名，文件类型和请求头
        # files = {'file': (filename, open(filepath, 'rb'), 'multipart/form-data', {'Expires': '0'})}
        files = {'file': open(filepath, 'rb')}
        flag = True
        while flag:
            try:
                req = requests.post(send_url, files=files, headers=cls.get_header_2(conf))
                res = req.json()
                LOG.info("result:" + req.text)
                flag = False
                return res
            except Exception as e:
                time.sleep(2)
                print(u"\n postfile 出现如下异常%s" % e)

    @classmethod
    def postfile_tenant(cls, api, filepath, conf):
        """docstring for post_report.

        Args:
            kwargs:
                _file_path: the report path
                _report_name:  the report name
                _post_data: post form-data

        Returns:
            the result of the post request
        Raises:
        :param post_data:
        :param filepath:
        :param filename:
        :param api:
        """
        send_url = conf['backend_address'] + api
        LOG.info("send_url:%s" % send_url)
        # 显式地设置文件名，文件类型和请求头
        # files = {'file': (filename, open(filepath, 'rb'), 'multipart/form-data', {'Expires': '0'})}
        files = {'file': open(filepath, 'rb')}
        flag = True
        while flag:
            try:
                req = requests.post(send_url, files=files, headers=cls.get_header_tenant(conf))
                res = req.json()
                LOG.info("result:" + req.text)
                flag = False
                return res
            except Exception as e:
                time.sleep(2)
                print(u"\n postfile 出现如下异常%s" % e)

    @staticmethod
    def get_header(conf):
        """
        :param self:
        :return: headers info
        """
        headers = {
            'uid': conf['uid'],
            'username': conf['username'],
            'AuthToken': 'AUTH 69b99fd7-038a-11e9-a79d-88e9fe4ece27',
            'Authorization': conf['authorization'],
            'Content-Type': "application/json",
            'AccountType': conf['account_type']
        }
        return headers

    @staticmethod
    def get_header_2(conf):
        """
        :param self:
        :return: headers info
        error: multipart data POST using python requests: no multipart boundary was found
        res: You should NEVER set that header yourself. We set the header properly with the boundary.
        If you set that header, we won't and your server won't know what boundary to expect
        (since it is added to the header). Remove your custom Content-Type header and you'll be fine.
        """
        headers = {
            'uid': conf['uid'],
            'username': conf['username'],
            'AuthToken': 'AUTH 69b99fd7-038a-11e9-a79d-88e9fe4ece27',
            'Authorization': conf['authorization'],
            # 'Content-Type': "multipart/form-data",
            'AccountType': conf['account_type']
        }
        LOG.info(headers)
        return headers

    @staticmethod
    def get_header_tenant(conf):
        """
        :param self:
        :return: headers info
        """
        headers = {
            'tenantId': conf['tenantId'],
            # 'Content-Type': "multipart/form-data",
            'Authorization': conf['authorization'],
            'uid': conf['uid'],
            'username': conf['username'],
            'AccountType': conf['account_type'],
            'AuthToken': 'AUTH 69b99fd7-038a-11e9-a79d-88e9fe4ece27',
            # 'Agent': conf['agentId'],
            # "requestFrom": "NGD",

        }
        LOG.info(headers)
        return headers
