#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/09/15 22:23:06
"""
import json
import logging
import uuid

from app.libs_ngd.large_model.large_model_data_statistics import data_statistics
from app.libs_ngd.large_model.request_large_model import RequestLargeModelObj
from app.libs_ngd.utils.core_query import CoreQuery
import openpyxl
import threadpool
import ndjson
import time
import os
from app.models.exts import get_db
from config import FileAddress, LargeModelConfig

global db
db = get_db()

class MultiThreadLargeModel:
    """
    多线程大模型处理
    """

    def __init__(self, env_config, large_model_config, data_file_path, task_id=''):
        self.env_config = env_config
        self.large_model_config = large_model_config
        self.data_file_path = data_file_path
        self.threadpool = threadpool.ThreadPool(self.env_config["thread_num"])
        self.core_query_obj = CoreQuery(env_config)
        self.req_large_model_obj = RequestLargeModelObj(large_model_config)
        self.res_data = []
        self.task_id = task_id
        self.uuid_dict = {}

    def read_data_by_large_model(self):
        """
        读取大模型数据，封装成list<dict>的格式，返回数据示例如下：
        [
            {
                'id': 1,
                'prompt_dict': {
                    "query": "6个免费产业都有啥",
                    "reference_answer": "适应产业: 新一代电子信息,模具和机械制造,饲料"
                },
                'source': 'xxx',
                "negative": True
            },
            {
                'id': 2,
                'prompt_dict': {
                    "query": "6个免费产业都xxxxx",
                    "reference_answer": "适应产业: 新一代电子信息,模具和机械制造,饲料",
                },
                'source': 'xxx',
                'negative': True
            },
            ...
        ]
        :param data_file_path:
        :return:
        """
        data_list = []
        excel_file = openpyxl.load_workbook(self.data_file_path.encode('utf-8'))
        sheet = excel_file.active
        # 逐行读取工作表中的数据，跳过首行
        for row in sheet.iter_rows(min_row=2, values_only=True):
            id = row[0]
            query = str(row[1]).encode('utf-8')
            reference_answer = str(row[2]).encode('utf-8')
            source = str(row[3])
            negative = str(row[4])
            print id, query, reference_answer
            query_info = {
                'id': id,
                'prompt_dict': {
                    'query': query,
                    'reference_answer': reference_answer,
                },
                'source': source,
                'negative': negative
            }
            data_list.append(query_info)
        return data_list

    def multi_thread_evaluate(self):
        """
        多线程效果评测，请求ngd后请求大模型，返回文件路径
        :return:
        """
        requests = threadpool.makeRequests(self.process_data, self.read_data_by_large_model())
        # [result] = self.threadpool.putRequests(requests)
        for req in requests:
            self.threadpool.putRequest(req)
        self.threadpool.wait()
        # 创建一个新的Excel工作簿
        wb = openpyxl.Workbook()
        # 第一张工作表，对比结果
        table = wb.active
        table.title = u"数据信息"
        table_head = ['id', '用户query', '标准答案', 'coreQuery结果', '得分', '原因', '第三方评判信息', '预期文档名称', '正负例']
        table.append(table_head)
        for row in self.res_data:
            table.append(row)

        # 统计数据，计算准确率和召回率等
        now_time = time.localtime()
        str_time_now = time.strftime("%Y%m%d%H%M%S", now_time)
        str_time_now1 = time.strftime("%Y-%m-%d %H:%M:%S", now_time)
        analysis_data = data_statistics(self.res_data, self.task_id, self.env_config.get('version'), str_time_now1)
        analysis_data_dict = analysis_data.object2dict()['effectData']
        # 结果写入第二张表
        analysis_data_sheet = wb.create_sheet(title=u'数据统计', index=1)
        analysis_data_list = [
            ['描述项', '数据'],
            ['用例总数', analysis_data_dict['testcasecount']],
            ['高可用case数(得2分视为高可用)', analysis_data_dict['return_correct_count']],
            ['可用case数(得1分及其以上视为可用)', analysis_data_dict['acceptable_count']],
            ['可用度(可用case数/用例总数)', analysis_data_dict['acceptability']],
            ['高可用度(高可用case数/用例总数)', analysis_data_dict['correct_percent']],
            ['平均分(总得分/case数)', analysis_data_dict['avg_score']],
            ['准确率(得分数/总分数)', analysis_data_dict['scoring_rate_percent']]
        ]
        for row in analysis_data_list:
            analysis_data_sheet.append(row)
        base_dir = FileAddress.result_dir
        try:
            if not os.path.exists(base_dir):
                os.makedirs(base_dir)
            dir = base_dir + "/" + "largeModel_res_result_%s.xlsx" % str_time_now
            wb.save(dir)
            # 结果数据入库
            db.session.add(analysis_data)
            db.session.commit()
            return dir
        except Exception as ex:
            print ex
            # 触发异常，判断文件是否存在，如果存在将文件删除
            if os.path.exists(dir):
                os.remove(dir)
            db.session.rollback()
            return None

    def process_data(self, data):
        """
        单条处理数据，先请求ndg，之后在请求大模型
        :param data:
        :return:
        """
        # 1.请求ndg
        ndg_res_text = self.request_ndg_large_model(data)
        # 2.请求大模型
        data['prompt_dict']['model_response'] = ndg_res_text
        large_model_no_answer = '当前文档无法回答你的问题，我可以尝试用我的常识来回答你'
        if self.env_config.get('large_model_no_answer') is not None and \
                self.env_config.get('large_model_no_answer') != '':
            large_model_no_answer = self.env_config.get('large_model_no_answer')
        no_answer = '抱歉,我不太理解您的意思'
        if self.env_config.get('no_answer') is not None and \
                self.env_config.get('no_answer') != '':
            no_answer = self.env_config.get('no_answer')
        if ndg_res_text.startswith(large_model_no_answer) or \
                ndg_res_text.startswith(no_answer):
            # 表示未匹配的情况，此时就不请求大模型了，直接打0分
            large_model_res_text = [u'0', u'并未命中，直接判0分', u'没有请求第三方大模型，并未命中，直接判0分']
        elif ndg_res_text == '请求ngd失败，请检查环境信息是否正确':
            large_model_res_text = [u'0', u'请求ngd失败，直接判0分', u'请求ngd失败，请检查环境信息是否正确']
        else:
            time.sleep(LargeModelConfig.sleep_time)
            large_model_res_text = self.request_large_model(data['prompt_dict'])
        line = [
            data.get('id'),
            data.get('prompt_dict').get('query'),
            data.get('prompt_dict').get('reference_answer'),
            data.get('prompt_dict').get('model_response'),
            large_model_res_text[0],
            large_model_res_text[1],
            large_model_res_text[2],
            data.get('source'),
            data.get('negative')
        ]
        self.res_data.append(line)

    def get_uuid_for_string(self, s):
        if s in self.uuid_dict:
            # 如果字符串已经存在于字典中，则返回对应的 UUID
            return self.uuid_dict[s]
        else:
            # 生成一个新的 UUID，并将其与字符串关联存储在字典中
            new_uuid = str(uuid.uuid4())
            self.uuid_dict[s] = new_uuid
            return new_uuid


    def request_ndg_large_model(self, data):
        """
        只请求ndg，并进行数据处理，返回响应的数据
        :param data: 单条信息
        :return:
        """
        session_id = self.get_uuid_for_string(data['id'])
        query = data['prompt_dict']['query']
        # 1.请求ngd接口
        # 注意，大模型类型http响应返回值是application/x-ndjson类型
        if self.env_config['version'] == '8.0':
            # 8.0版本处理
            ngd_res = self.core_query_obj.core_query_large_model_v8(query, session_id)
            model_response = ''
            if ngd_res is None:
                msg = '请求ngd失败，请检查环境信息是否正确'
                return msg
            else:
                res = ngd_res.text
                print '11111'
                print res
                def remove_data_prefix(s):
                    """
                    去掉data:前缀
                    :param s:
                    :return:
                    """
                    if s.startswith("data:"):
                        return s[5:]  # 跳过 "data:" 这5个字符
                    return s  # 如果字符串不以 "data:" 开头，则返回原始字符串

                res_list = map(remove_data_prefix, filter(lambda x: x, res.split('\n')))
                print '22222'
                print res_list
                model_res = ''
                for item in res_list:
                    if json.loads(item).get('answer')[0].get('reply') is None:
                        continue
                    else:
                        cur_text = json.loads(item).get('answer')[0].get('reply').get('text')
                        print '333333'
                        print cur_text
                        model_res += cur_text
                logging.info('ngd响应结果为' + model_res)
                return model_res
        else:
            # 7.0版本处理
            ngd_res = self.core_query_obj.core_query_large_model(query, session_id)
            model_response = ''
            # 2.处理返回结果
            if ngd_res is None:
                msg = '请求ngd失败，请检查环境信息是否正确'
                return msg
            else:
                # print 'ngd响应结果为：---------------------------------', ngd_res.text
                # print type(ngd_res.text)
                # print ndjson.loads(ngd_res.text.encode('utf-8'))
                for line in ndjson.loads(ngd_res.text.encode('utf-8')):
                    if line:
                        # 在这里处理解析后的JSON对象
                        tmp_answerText = line.get('answer').get('answerText')
                        if tmp_answerText is None or tmp_answerText == '':
                            continue
                        else:
                            model_response += str(tmp_answerText.encode('utf-8'))
                return model_response

    def request_large_model(self, prompt_dict):
        """
        请求第三方大模型
        :param prompt_dict:
        :return:
        """
        return self.req_large_model_obj.request_large_model_and_parse_result(prompt_dict)


if __name__ == '__main__':
    env_config = {
        'id': 2,
        'version': 'ngdv730',
        'env_name': 'ngdv730',
        'backend_address': 'https://ics.bce.baidu.com/ngd',
        'core_address': 'https://ics.bce.baidu.com/ngd',
        'username': 'user1',
        'uid': 't1000000001',
        'account_type': 'fake',
        'bot_name': '大模型测试',
        'no_answer': '抱歉,我不太理解您的意思',
        'clarify_answer': '请问您咨询的是否是以下内容，如果没有您想要咨询的内容，您可以输入“转人工”为您解答。',
        'agentId': 'fcca60e7-43ad-4d30-bf62-cb7a69e4342c',
        'botToken': 'NGD 0556778e-e142-4b72-9ee6-c3226085aa4e',
        'authorization': 'NGD 0556778e-e142-4b72-9ee6-c3226085aa4e',
        'nlu_enable': 0,
        'thread_num': 5,
        'user_name': 'zhangjiabin01'
    }
    curl_command_template = """curl --location --request POST 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=24.6318aa27abf18d308f97c06ab6c2f761.2592000.**********.282335-********' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "messages": [
            {"role":"user","content":$prompt}
        ]
    }'
        """
    prompt_template = """你是专业的评测人员，请根据问题和参考答案对模型输出进行准确性进行打分，分档0、1、2，具体的打分标准请参照分档描述和注意事项，此外你还需要提供打分和对应的评分依据，你的回答请按照
    【得分】xxx
    【原因】xxx
    的格式输出，注意得分和原因之间要进行换行
    下面给出分档描述、问题、参考答案、模型输出以及注意事项
    【分档描述】
    0: 模型输出与参考答案语义和相关数据信息完全不符合
    1:模型输出与参考答案语义和相关数据信息部分符合，但是允许存在不完整、冗余或者部分错误的情况
    2:模型输出与参考答案语义和相关数据洗洗脑完全符合
    【注意事项】
    1.如果模型输出有关于来源的描述，例如如果模型中有"答案由一言生成"、"来源xxx"等内容，请将内容进行忽略
    【问题】{query}
    【参考答案】{reference_answer}
    【模型输出】{model_response}
        """
    large_model_config = {
        'large_model_type': 'other',
        'app_key': None,
        'prompt_template': prompt_template,
        'curl_command_template': curl_command_template,
        'pattern': "【得分】([012])\n【原因】(.*)",
        'route_list': ['result']
    }
    data_file_path = '/Users/<USER>/Documents/大模型上传文件示例1.xlsx'
    obj = MultiThreadLargeModel(env_config, large_model_config, data_file_path)
    prompt_dict = {
        "id": 1,
        "prompt_dict": {
            "query": "6个免费产业都有啥",
            "reference_answer": "适应产业: 新一代电子信息,模具和机械制造,饲料",
        }
    }
    file_path = obj.multi_thread_evaluate()
    print file_path
