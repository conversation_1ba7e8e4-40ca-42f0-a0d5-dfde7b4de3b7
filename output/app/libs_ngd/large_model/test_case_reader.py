#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/09/15 22:23:06
"""

import openpyxl

def read_data_by_large_model(data_file_path):
    """
    读取大模型数据，封装成list<dict>的格式，返回数据示例如下：
    [
        {
            'id': 1,
            'prompt_dict': {
                "query": "6个免费产业都有啥",
                "reference_answer": "适应产业: 新一代电子信息,模具和机械制造,饲料"
            }
        },
        {
            'id': 2,
            'prompt_dict': {
                "query": "6个免费产业都xxxxx",
                "reference_answer": "适应产业: 新一代电子信息,模具和机械制造,饲料"
            }
        },
        ...
    ]
    :param data_file_path:
    :return:
    """
    data_list = []
    excel_file = openpyxl.load_workbook(data_file_path)
    sheet = excel_file.active
    # 逐行读取工作表中的数据，跳过首行
    for row in sheet.iter_rows(min_row=2, values_only=True):
        id = row[0]
        query = row[1]
        reference_answer = row[2]
        print id, query, reference_answer
        query_info = {
                        'id': id,
                        'prompt_dict': {
                            'query': query,
                            'reference_answer': reference_answer
                        }
                     }
        data_list.append(query_info)
    return data_list

if __name__ == '__main__':
    print read_data_by_large_model('/Users/<USER>/Documents/大模型上传文件示例.xlsx')


