#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/04/09 17:23:06
"""
import time

from ..utils.http_request import HttpRequest
from ..utils.log import LOG


class Cluster(object):
    """
    聚类效果收集
    """

    def __init__(self, conf):
        """
        init
        """
        self.agentId = conf.agentId
        self.dict_cluster = []
        self.dict_detail = []
        self.conf = conf

    def cluster2(self):
        """
        :return:
        """
        api = "/api/v2/label/cluster_list?labelListType=0&pn=1&matchType=-1&ps=1000&agentId=" + \
              self.agentId + "&permCode=label_all"
        LOG.info(api)
        flag = True
        while flag:
            try:
                res = HttpRequest.get(api, self.conf)
                flag = False
                LOG.info("res %s " % res)
                # return res
            except Exception:
                time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])

        data = res['data']['list']
        # if data is None:
        #     return None
        for i in data:
            detail = self.cluster_detail(i['clusterId'], self.conf)
            self.dict_cluster.append({'clusterId': i['clusterId'], 'count': i['count'], 'detail': detail})
        LOG.info("dict_cluster %s " % self.dict_cluster)
        return self.dict_cluster

    def cluster(self, conf):
        """
        聚合列表显示长度最大值是1000，此方法可以读取全部，但不能超过10000
        :return:
        """
        pn = 1
        res = self.cluster_list(pn, conf)
        total = res["data"]["total"]
        # 取整
        division_round_num = total // 20
        # 取余
        remainder_num = total % 20
        print("取整=%s, 取余=%s" % (division_round_num, remainder_num))
        if remainder_num > 0:
            division_round_num += 1
        if division_round_num > 1 and division_round_num <= 50:
            print("小于50页：%s" % division_round_num)
            for index in range(2, division_round_num):
                self.cluster_list(index, conf)
        elif division_round_num > 50:
            print("大于50页：%s" % division_round_num)
            for index in range(2, 50):
                self.cluster_list(index, conf)
        LOG.info("dict_cluster %s " % self.dict_cluster)

        return self.dict_cluster

    def cluster_list(self, pn, conf):
        """
        :return:
        """
        api = "/api/v2/label/cluster_list?labelListType=0&pn=" + str(pn) + "&matchType=-1&ps=20&agentId=" + \
              self.agentId + "&permCode=label_all&referer=index"
        LOG.info(api)
        flag = True
        while flag:
            try:
                res = HttpRequest.get(api, conf)
                flag = False
                LOG.info("res %s " % res)
                # return res
            except Exception:
                time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])
        data = res['data']['list']
        # if data is None:
        #     return None
        for i in data:
            detail = self.cluster_detail_v61(i['clusterId'], conf)
            self.dict_cluster.append({'clusterId': i['clusterId'], 'count': i['count'], 'detail': detail})
        return res

    def cluster_detail(self, id, conf):
        """
        :param id: 6.0之前版本
        :return:
        """
        api = "/api/v2/label/list_in_cluster?pn=1&isClusterMode=true&clusterId=" + \
              id + "&source=null&typeId=null&labelListType=0&matchType=-1&ps=2000&agentId=" + \
              self.agentId + "&permCode=label_all"
        flag = True
        while flag:
            try:
                res = HttpRequest.get(api, conf)
                flag = False
            except Exception:
                time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])
        data = res['data']['list']
        if data is None:
            return None
        return data

    def cluster_detail_v61(self, id, conf):
        """
        :param id: 6.1版本
        :return:
        """
        api = "/api/v2/label/record_list?pn=1&isClusterMode=true&clusterId=" + \
              id + "&source=null&typeId=null&labelListType=0&matchType=-1&ps=20&agentId=" + \
              self.agentId + "&permCode=label_assign"
        flag = True
        while flag:
            try:
                res = HttpRequest.get(api, conf)
                flag = False
            except Exception:
                time.sleep(2)
                print(u"\nlose connection")

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])
        data = res['data']['list']
        if data is None:
            return None
        return data
