#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/04/09 22:23:06
"""
from ..utils.core_query import CoreQuery
from ..utils.log import LOG

class ClusterCollectQuery:
    """
    FaqQuery
    """
    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.conf = conf

    def cluster_collect_question(self, test_case_list):
        """
        :param entity_id:
        :param query:
        :return:
        """
        for test_case in test_case_list:
            LOG.info(test_case_list[test_case])
            corequery = CoreQuery(self.conf)
            corequery.core_query(test_case)
