#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2021/02/26
"""

import openpyxl


class TableQaRoundsTestReader:
    """
    FaqTestReader
    """

    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[0])

        test_cases = []

        skip_first = True
        label_list = []
        profession_label_list = []
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue
            if row_i[0].value is not None:
                id = row_i[0].value
                query = row_i[1].value
                coreExpect = row_i[2].value
                professionLabel = row_i[3].value
                label = row_i[4].value
                label = label.encode('utf-8')

                if ',' in label:
                    sub_label_list = label.split(',')
                    for sub_label in sub_label_list:
                        if sub_label not in label_list:
                            label_list.append(sub_label)
                else:
                    if label not in label_list:
                        label_list.append(label)

                if ',' in professionLabel:
                    sub_p_label_list = professionLabel.split(',')
                    for sub_p_label in sub_p_label_list:
                        if sub_p_label not in profession_label_list:
                            profession_label_list.append(sub_p_label)
                else:
                    if professionLabel not in profession_label_list:
                        profession_label_list.append(professionLabel)
            else:
                break

            test_cases.append({'id': id, 'query': query, 'coreExpect': coreExpect, 'professionLabel': professionLabel,
                 'label': label})
        return test_cases, profession_label_list, label_list
