#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
from openpyxl import Workbook

from ..entity_user.entity_query import UserEntityQuery

from ..utils.entity.open_switch import Switches

from ..utils.log import LOG


def user_entity_evaluate(test_case_list, conf):
    """
    :param test_case_list:
    :return:
    """
    output_wookbook = Workbook()
    # 选择第一个工作表
    # result_sheet = output_wookbook.active
    result_sheet = output_wookbook.create_sheet(index=0, title=u'测试')

    result_sheet.title = u"测试结果"

    result_sheet.cell(1, 1, "query")
    result_sheet.cell(1, 2, "id")
    result_sheet.cell(1, 3, "正确结果")
    result_sheet.cell(1, 4, "识别结果")
    result_sheet.cell(1, 5, "是否正确")

    row_index = 0

    correct_count = 0
    wrong_count = 0
    entity_switch = []
    for test_case in test_case_list:

        row_index += 1
        query = test_case['query']
        standard = test_case['entity_answer']
        entity_id = test_case['entity_id']
        entity_nameZh = test_case['entity_nameZh']

        if "sys_" in entity_id:
            open_s = Switches(conf)

            if entity_id not in entity_switch:
                for id in entity_switch:
                    open_s.close_switches(id)
                    entity_switch.remove(id)
                    LOG.info("禁用中：%s" % entity_switch)
                open_s.open_switch(entity_id)
                entity_switch.append(entity_id)
                LOG.info("启用中：%s" % entity_switch)
        LOG.info("query is %s" % query)
        LOG.info("standard is %s " % standard)
        standard = standard.encode('utf8')

        entity = UserEntityQuery(conf)
        data = entity.entity_question(query, entity_id)
        LOG.info("data: % s" % data)
        if data is not None and query is not None:
            response = entity.entity_value(data, entity_id)
            response_nameZh = entity.entity_nameZh(data, entity_id)
            LOG.info("response is %s " % response)
            if response:
                LOG.info("ok")
            else:
                LOG.info("empty")
            if response is not None and response:
                response_flag = True
                res = ','.join(response)

                # if str(standard) in response:
                if standard in response or entity_nameZh in response_nameZh:
                    response_flag = False
                    correct_count += 1
                    result_sheet.cell(row_index + 1, 1, query)
                    result_sheet.cell(row_index + 1, 2, entity_id)
                    result_sheet.cell(row_index + 1, 3, standard)
                    result_sheet.cell(row_index + 1, 4, res)
                    result_sheet.cell(row_index + 1, 5, 1)
                else:
                    wrong_count += 1
                    result_sheet.cell(row_index + 1, 1, query)
                    result_sheet.cell(row_index + 1, 2, entity_id)
                    result_sheet.cell(row_index + 1, 3, standard)
                    result_sheet.cell(row_index + 1, 4, res)
                    result_sheet.cell(row_index + 1, 5, 0)
            else:
                wrong_count += 1
                result_sheet.cell(row_index + 1, 1, query)
                result_sheet.cell(row_index + 1, 2, entity_id)
                result_sheet.cell(row_index + 1, 3, standard)
                result_sheet.cell(row_index + 1, 4, "")
                result_sheet.cell(row_index + 1, 5, 0)

    print("total_count", row_index)
    print("correct_count", correct_count)
    print("wrong_count", wrong_count)

    report_sheet = output_wookbook.create_sheet(index=1, title=u"统计数据")

    report_sheet.cell(1, 1, "用例总数")
    report_sheet.cell(2, 1, "识别正确")
    report_sheet.cell(3, 1, "识别错误")
    report_sheet.cell(4, 1, "正确率")
    report_sheet.cell(5, 1, "错误率")

    report_sheet.cell(1, 2, row_index)
    report_sheet.cell(2, 2, correct_count)
    report_sheet.cell(3, 2, wrong_count)
    report_sheet.cell(4, 2, float(correct_count) / float(row_index))
    report_sheet.cell(5, 2, float(wrong_count) / float(row_index))

    # time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
    # output_wookbook.save("./data/entity_evaluate_result_%s.xlsx" % time_now)
    output_wookbook.save("./output/user_entity_evaluate_result.xlsx")

    return entity_switch

# if __name__ == '__main__':
#     main()
