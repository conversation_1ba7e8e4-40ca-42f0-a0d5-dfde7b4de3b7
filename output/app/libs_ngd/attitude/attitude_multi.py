#!/usr/bin/python
# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/05/25
"""
import time
import os
import sys
import traceback
from openpyxl import Workbook
from ..utils.log import LOG
from app.models.models import Task, AttitudeAnalysis

dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(dir)
from config import FileAddress
from app.models.exts import get_db

global db
db = get_db()


class MutilAttitude:
    """
    多线程
    """

    def __init__(self, test_case_list, attitude_query, conf):
        self.version = conf['version']
        self.output_wookbook = Workbook()
        # 选择第一个工作表
        # result_sheet = output_wookbook.active
        self.result_sheet = self.output_wookbook.create_sheet(index=0, title=u'测试')
        self.result_sheet.title = u"测试结果"

        self.result_sheet.cell(1, 1, "query")
        self.result_sheet.cell(1, 2, "正确结果")
        self.result_sheet.cell(1, 3, "识别结果")
        self.result_sheet.cell(1, 4, "相似度")
        self.result_sheet.cell(1, 5, "是否正确")

        self.test_case_list = test_case_list
        self.attitude_query = attitude_query
        self.conf = conf
        self.row_index = 0

        self.correct_count = 0
        self.wrong_count = 0
        self.response_correct_count = 0
        self.clarify_correct_count = 0
        self.response_count = 0
        self.clarify_count = 0
        self.sid_result_list = []

    def query_functions(self, test_case):
        """
        获取query结果
        """
        # sid_result_list = []
        self.row_index += 1
        query = test_case['query']
        standard = test_case['attitude_answer']

        LOG.info("standard is %s " % standard)
        standard = standard.encode('utf8')

        time.sleep(1)
        response, prob = self.attitude_query.attitude_question(query)

        LOG.info("response is %s " % response)
        if response:
            LOG.info("ok")
        else:
            LOG.info("empty")

        if response is not None and response:
            response_flag = True
            LOG.info("%s-%s" % (response, standard))
            if standard == response:
                response_flag = False
                self.correct_count += 1
                self.sid_result_list.append(
                    query + '%*' + standard + '%*' + response + '%*' +
                    str(prob) + '%*' + "1")

            else:
                self.wrong_count += 1
                self.sid_result_list.append(
                    query + '%*' + standard + '%*' + response + '%*' +
                    str(prob) + '%*' + "0")

        else:
            self.wrong_count += 1
            self.sid_result_list.append(
                query + '%*' + standard + '%*' + "" + '%*' +
                "0" + '%*' + "0")

    def save_attitude_result(self):
        """
        保存结果
        """
        report_sheet = self.output_wookbook.create_sheet(index=1, title=u"统计数据")
        print("total_count", self.row_index)
        print("correct_count", self.correct_count)
        print("wrong_count", self.wrong_count)

        for i in range(0, len(self.sid_result_list)):
            row_num = i + 2
            result_data = self.sid_result_list[i]
            result_data_list = result_data.split("%*")

            for i in range(len(result_data_list)):
                self.result_sheet.cell(row_num, i + 1, result_data_list[i])

        report_sheet.cell(1, 1, "用例总数")
        report_sheet.cell(2, 1, "识别正确")
        report_sheet.cell(3, 1, "识别错误")
        report_sheet.cell(4, 1, "正确率")
        report_sheet.cell(5, 1, "错误率")

        report_sheet.cell(1, 2, self.row_index)
        report_sheet.cell(2, 2, self.correct_count)
        report_sheet.cell(3, 2, self.wrong_count)
        report_sheet.cell(4, 2, float(self.correct_count) / float(self.row_index))
        report_sheet.cell(5, 2, float(self.wrong_count) / float(self.row_index))

        new_analysis = AttitudeAnalysis(testcasecount=self.row_index,
                                   correct_count=self.correct_count,
                                   error_count=self.wrong_count,
                                   task_id=self.conf['task_id'],
                                   correct_percent=float(self.correct_count) / float(self.row_index),
                                   error_percent=
                                   float(self.wrong_count) / float(self.row_index),
                                   )

        msg = ''
        try:
            db.session.add(new_analysis)
            db.session.commit()
        except Exception as e:
            print(e)
            traceback.print_exc()
            msg = e

        time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
        finish_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        base_dir = FileAddress.result_dir
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        dir = base_dir + "/" + "attitude_evaluate_result_%s.xlsx" % (time_now)
        self.output_wookbook.save(dir)

        task = Task.query.filter(Task.task_id == self.conf['task_id']).first()
        print task.id
        print task.version
        task.result_dir = dir.decode('utf-8')
        task.finish_time = finish_time
        time.sleep(2)
        analysis = AttitudeAnalysis.query.filter(AttitudeAnalysis.task_id == self.conf['task_id']).first()
        analysis.version = task.version
        analysis.finish_time = finish_time
        analysis.version_finish_time = task.version + "_" + finish_time
