# -*- coding: utf-8 -*-
"""
@author:xieyongdong
"""
from encodings import utf_8
import numpy as np
from warnings import catch_warnings
import requests
import json
import time
import openpyxl
import re
from datetime import datetime
import argparse


def getAccessToken():
    """
    通过ak、sk获取文心token
    :return: access_token
    """
    url = "https://aip.baidubce.com/oauth/2.0/token?"\
        "grant_type=client_credentials&client_id=hnX8luym9a5HWgLzTqCYXfAg&client_secret=gVwfBpGWIp37fkKxkiZcYj4IbGqhIuMg"

    response = requests.request("GET", url, headers="", data="")
    response = response.json()
    return response['access_token']

def getEmbeddingsBetweenTwoWords(access_token, first, second):
    """
    获取两个query之间的向量
    :param access_token: api token
    :param first: 第一个query
    :param second: 第二个query
    :return:
    """
    url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/embedding-v1?"\
        "access_token={}".format(access_token)

    payload = json.dumps({
    "input": [
        first,
        second
    ]
    })
    headers = {
    'Content-Type': 'application/json'
    }
    json_data = requests.request("POST", url, headers=headers, data=payload)
    json_data = json_data.json()

    # 提取向量 A 和 B
    A = np.array(json_data["data"][0]["embedding"])
    B = np.array(json_data["data"][1]["embedding"])

    # 计算向量 A 和 B 的点积
    dot_product = np.dot(A, B)

    # 计算向量 A 和 B 的模
    magnitude_A = np.linalg.norm(A)
    magnitude_B = np.linalg.norm(B)

    # 计算夹角的余弦值
    cos_theta = dot_product / (magnitude_A * magnitude_B)

    # 计算夹角（弧度）
    theta_radians = np.arccos(cos_theta)

    # 将夹角转换为角度
    theta_degrees = np.degrees(theta_radians)

    # 打印结果
    print("向量 A 和 B 的cos_theta ：", cos_theta)
    return cos_theta


print(getEmbeddingsBetweenTwoWords(getAccessToken(), "好的，已为您预订万豪酒店的大床房，2024-03-02入住，2024-03-03离店，祝您入住愉快", 
"好的，已为您预订阿里巴巴酒店的大床房，2024-03-02入住，2024-03-03离店，祝您入住愉快"))