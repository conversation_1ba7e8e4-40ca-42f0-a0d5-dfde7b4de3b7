# -*- coding: UTF-8 -*-
################################################
# copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
本文件:效果评测

Authors: <AUTHORS>
Date:    2022/11/11
"""
import sys
import time
import traceback
from string import capitalize

from flask import current_app
import json
import os
from flask import Blueprint, request, jsonify

from config import LargeModelConfig
from ..libs_ngd.large_model.request_large_model import RequestLargeModelObj
from ..models.models import Env, Task, Query, EventTracking
from ..models.exts import get_db
from ..libs_ngd.large_model.faq_import_search_statistics import FaqImportSearch
from ..libs_ngd.large_model.large_model_import_search import LargeModelImportSearch
from celery.result import AsyncResult

reload(sys)
sys.setdefaultencoding('utf-8')
evaluate_bp = Blueprint('evaluate_bp', __name__)

root = os.path.dirname(os.path.dirname(os.path.abspath(
    __file__)))
tableqa_query = root + "/data/tableqa_data/戴姆勒表格问答单轮数据/total1.xlsx"

global db
db = get_db()


@evaluate_bp.route('/evaluate/engine', methods=['GET'])
def models_check():
    """
    服务校验：功能; namespace, project_env
    获取执行命令，然后执行
    """
    msg = ""
    task_id = ''
    config = ''
    script = ''
    version = ''
    try:
        diff_version = request.args.get('version')
        project_env = request.args.get('env')  #
        engine = request.args.get('engine')
        datas = request.args.get('data')
        print project_env
        # 查询
        config = Env.query.filter(Env.env_name == project_env).first()
        print('env_name:%s' % config.env_name)
        print('env_version:%s' % config.version)
        version = config.version
        # if isinstance(datas, list):
        # if "," in datas:
        if engine == "intents":
            data_list = datas.split(",")
            script = []
            for data in data_list:
                dataxls = Query.query.filter(Query.file_name == data).first()
                # script = dataxls.file_path.encode('utf-8')
                print('file_name:%s' % dataxls.file_name)
                print('file_path:%s' % dataxls.file_path)
                script.append(dataxls.file_path.encode('utf-8'))
        elif engine == "小模型一键执行":
            print "小模型一键执行"
        else:
            # script = []
            dataxls = Query.query.filter(Query.file_name == datas).first()
            script = dataxls.file_path.encode('utf-8')
            print('file_name:%s' % dataxls.file_name)
            print('file_path:%s' % dataxls.file_path)
            # script.append(scripts)
        print script

    except Exception as e:
        print(e)
        msg = e
        traceback.print_exc()
        db.session.rollback()
    try:
        print type(config)
        new_config = {}
        new_config['backend_address'] = config.backend_address
        new_config['core_address'] = config.core_address
        new_config['knowledge_address'] = config.user_name
        new_config['no_answer'] = config.no_answer
        new_config['clarify_answer'] = config.clarify_answer
        new_config['agentId'] = config.agentId
        new_config['tenantId'] = config.tenantId
        new_config['botToken'] = config.botToken

        new_config['nlu_enable'] = config.nlu_enable
        new_config['thread_num'] = config.thread_num

        new_config['uid'] = config.uid
        new_config['username'] = config.username
        new_config['authorization'] = config.authorization
        new_config['account_type'] = config.account_type
        new_config['version'] = config.version
        new_config['env_name'] = config.env_name
        new_config['bot_name'] = config.bot_name
        print new_config
        ran = time.time()
        task_id = str(ran)
        print task_id
        new_config['task_id'] = task_id
        current_app.celery.send_task("evaluate", [new_config, engine, script, version, diff_version], task_id=task_id)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        new_task = Task(version=config.version,
                        env_name=config.env_name,
                        engine=engine,
                        create_time=create_time,
                        # result_dir=,
                        task_id=task_id
                        )
        db.session.add(new_task)
        db.session.commit()
        print Task.query.all()

    except Exception as e:
        print(e)
        msg = e
        traceback.print_exc()

    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "开始效果评测",
            "task_id": task_id,
            "status": 0
        }
    os.system("curl --location --request POST 'http://yq01-aip-huatuo11a1b90c.yq01.baidu.com:8089/projectTest/baidu/ebgqa/amis/kefuamis/projectManage/abilityManage/opterationStatus/add_OpterationStatus_Offline.php' \
         --header 'Content-Type: application/x-www-form-urlencoded' \
         --header 'Cookie: BAIDUID=F0AD7A16A3ABDD4935E81B32372CF6FB:FG=1' \
         --data-urlencode 'productName=29-智能对话' \
         --data-urlencode 'businessLineName=智能客服NGD' \
         --data-urlencode 'ability_type=效果测试' \
         --data-urlencode 'trigger_user={}'".format(config.user_name))

    # 平台打点
    event_tracking()
    capability_push()
    capability_push_user()

    res = json.dumps(result, encoding="utf-8", ensure_ascii=False)
    return res


@evaluate_bp.route('/evaluate/largeModel/test', methods=['POST'])
def large_model_test():
    """
    测试大模型联通性
    :return:
    """
    # 大模型类型other和gpt3.5
    large_model_type = request.form.get("largeModelType")
    # gpt用的appKey
    api_key = request.form.get('apiKey', LargeModelConfig.api_key)
    # prompt模板，通用
    prompt_template = request.form.get('template')
    # 非gpt使用的curl模板
    curl_command_template = request.form.get('curlCommandTemplate', '')
    # 正则表达式，用于提取，通用
    pattern = request.form.get('pattern', LargeModelConfig.default_pattern)
    # 获取大模型响应结果的路径，是个list
    route_list = request.form.get('routeList', '').split(',')

    large_model_config = {
        'large_model_type': large_model_type,
        'api_key': api_key,
        'prompt_template': prompt_template,
        'curl_command_template': curl_command_template,
        'pattern': pattern,
        'route_list': route_list
    }
    print '配置信息'
    print large_model_config
    req_large_model_obj = RequestLargeModelObj(large_model_config)
    is_available, msg = req_large_model_obj.test_available()
    if is_available:
        return {'code': 200, 'msg': msg}, 200
    else:
        return {'code': 400, 'msg': msg}, 400


@evaluate_bp.route('/evaluate/task/largeModel', methods=['POST'])
def large_model_task():
    """
    大模型效果评测
    :return:
    """
    msg = ""

    # 版本信息
    diff_version = request.args.get('version')
    # 环境信息
    project_env = request.args.get('env')
    # 引擎信息
    engine = request.args.get('engine')
    # 文件Excel表
    file_name = request.args.get('query')

    # 大模型类型other和gpt3.5
    large_model_type = request.form.get("largeModelType", "gpt3.5")
    # gpt用的appKey
    api_key = request.form.get('apiKey', LargeModelConfig.api_key)
    # prompt模板，通用
    prompt_template = request.form.get('template')
    # 非gpt使用的curl模板
    curl_command_template = request.form.get('curlCommandTemplate', '')
    # 正则表达式，用于提取，通用
    pattern = request.form.get('pattern', LargeModelConfig.default_pattern)
    # 获取大模型响应结果的路径，是个list
    route_list = request.form.get('routeList', '').split(',')

    large_model_config = {
        'large_model_type': large_model_type,
        'api_key': api_key,
        'prompt_template': prompt_template,
        'curl_command_template': curl_command_template,
        'pattern': pattern,
        'route_list': route_list
    }

    for key in large_model_config:
        print key, ':', large_model_config[key]

    env_info = Env.query.filter(Env.env_name == project_env).first()
    version = env_info.version
    data_file = Query.query.filter(Query.file_name == file_name).first()
    data_file_path = data_file.file_path.encode('utf-8')

    config = env_info.task2dict()
    for key in config:
        print key, config[key]
    task_id = str(time.time())
    config['task_id'] = task_id
    current_app.celery.send_task("large_model_evaluate", [config, data_file_path, large_model_config, task_id], task_id=task_id)
    create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    try:
        new_task = Task(version=config['version'],
                        env_name=config['env_name'],
                        engine=engine,
                        create_time=create_time,
                        # result_dir=,
                        task_id=task_id
                        )
        db.session.add(new_task)
        db.session.commit()
    except Exception as e:
        print(e)
        msg = e
        traceback.print_exc()

    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "开始效果评测",
            "task_id": task_id,
            "status": 0
        }
    # os.system("curl --location --request POST 'http://yq01-aip-huatuo11a1b90c.yq01.baidu.com:8089/projectTest/baidu/ebgqa/amis/kefuamis/projectManage/abilityManage/opterationStatus/add_OpterationStatus_Offline.php' \
    #      --header 'Content-Type: application/x-www-form-urlencoded' \
    #      --header 'Cookie: BAIDUID=F0AD7A16A3ABDD4935E81B32372CF6FB:FG=1' \
    #      --data-urlencode 'productName=29-智能对话' \
    #      --data-urlencode 'businessLineName=智能客服NGD' \
    #      --data-urlencode 'ability_type=效果测试' \
    #      --data-urlencode 'trigger_user={}'".format(config.user_name))

    # 平台打点
    event_tracking()
    capability_push()
    capability_push_user()
    return result, 200


@evaluate_bp.route('/evaluate/task/faqSearch', methods=['POST'])
def large_model_faq_search_task():
    """
    大模型效果评测
    :return:
    """
    msg = ""
    # 版本信息
    diff_version = request.args.get('version')
    # 环境信息
    project_env = request.args.get('env')
    # 引擎信息-faq search
    engine = request.args.get('engine')
    # 测试集文件
    file_name = request.args.get('query')

    # faq 检索参数
    min_score = request.form.get("min_score")
    max_score = request.form.get("max_score")
    dataset_id = request.form.get("dataset_id")
    top_k_faq = request.form.get("top_k_faq")
    num_groups = request.form.get("num_groups")
    dir_id = request.form.get("dir_id")
    cookie = request.form.get("cookie")

    faq_search_config = {
        "min_score": float(min_score),
        "max_score": float(max_score),
        "dataset_id": dataset_id,
        "top_k_faq": int(top_k_faq),
        "num_groups": int(num_groups),
        "dir_id": dir_id,
        "cookie": cookie
    }

    for key in faq_search_config:
        print key, ':', faq_search_config[key]

    env_info = Env.query.filter(Env.env_name == project_env).first()
    data_file = Query.query.filter(Query.file_name == file_name).first()
    data_file_path = data_file.file_path.encode('utf-8')
    config = env_info.task2dict()
    for key in config:
        print key, config[key]
    task_id = str(time.time())
    config['task_id'] = task_id
    current_app.celery.send_task("evaluate_faq_search", [config, faq_search_config, data_file_path, task_id],
                                 task_id=task_id)

    create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    try:
        new_task = Task(version=config['version'],
                        env_name=config['env_name'],
                        engine=engine,
                        create_time=create_time,
                        task_id=task_id
                        )
        db.session.add(new_task)
        db.session.commit()
    except Exception as e:
        print(e)
        msg = e
        traceback.print_exc()

    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "开始效果评测",
            "task_id": task_id,
            "status": 0
        }

    # 平台打点
    event_tracking()
    capability_push()
    capability_push_user()
    return result, 200


@evaluate_bp.route('/evaluate/faqSearch/import', methods=['POST'])
def faq_search_data_import():
    """
    导入faq检索数据
    :return:
    """
    msg = ""
    # 版本信息
    diff_version = request.args.get('version')
    # 环境信息
    project_env = request.args.get('env')
    # 引擎信息-faq search
    engine = request.args.get('engine')
    # 测试集文件
    file_name = request.args.get('query')

    # faq 检索参数
    min_score = request.form.get("min_score")
    max_score = request.form.get("max_score")
    dataset_id = request.form.get("dataset_id")
    top_k_faq = request.form.get("top_k_faq")
    num_groups = request.form.get("num_groups")
    dir_id = request.form.get("dir_id")
    cookie = request.form.get("cookie")

    faq_search_config = {
        "min_score": float(min_score),
        "max_score": float(max_score),
        "dataset_id": str(dataset_id),
        "top_k_faq": int(top_k_faq),
        "num_groups": int(num_groups),
        "dir_id": str(dir_id),
        "cookie": str(cookie)
    }

    for key in faq_search_config:
        print key, ':', faq_search_config[key]

    env_info = Env.query.filter(Env.env_name == project_env).first()
    data_file = Query.query.filter(Query.file_name == file_name).first()
    data_file_path = data_file.file_path.encode('utf-8')

    config = env_info.task2dict()
    for key in config:
        print key, config[key]
    task_id = str(time.time())
    config['task_id'] = task_id

    faq_search_evaluate_obj = FaqImportSearch(config, faq_search_config, data_file_path, task_id)

    msg = faq_search_evaluate_obj.import_faq_to_tenant()

    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "接口调用异常，请登陆环境查看",
            "task_id": task_id,
            "status": 0
        }
    return result, 200


@evaluate_bp.route('/evaluate/docSearch/import', methods=['POST'])
def doc_search_data_import():
    """
    导入faq检索数据
    :return:
    """
    msg = ""
    # 版本信息
    diff_version = request.args.get('version')
    # 环境信息
    project_env = request.args.get('env')
    # 引擎信息-faq search
    engine = request.args.get('engine')
    # 测试集文件
    file_name = request.args.get('query')

    # faq 检索参数
    llm_search_industry = request.form.get("llm_search_industry")
    threshold = request.form.get("threshold")
    dataset_id = request.form.get("dataset_id")
    dir_id = request.form.get("dir_id")
    cookie = request.form.get("cookie")

    llm_search_config = {
        "llm_search_industry": int(llm_search_industry),
        "threshold": float(threshold),
        "dataset_id": str(dataset_id),
        "dir_id": str(dir_id),
        "cookie": str(cookie)
    }

    for key in llm_search_config:
        print key, ':', llm_search_config[key]

    env_info = Env.query.filter(Env.env_name == project_env).first()
    data_file = Query.query.filter(Query.file_name == file_name).first()
    data_file_path = data_file.file_path.encode('utf-8')
    print('【】')
    print(data_file, data_file_path)
    config = env_info.task2dict()
    for key in config:
        print key, config[key]
    task_id = str(time.time())
    config['task_id'] = task_id

    doc_search_evaluate_obj = LargeModelImportSearch(config, llm_search_config, data_file_path, task_id)

    msg = doc_search_evaluate_obj.llm_import_documents()

    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "接口调用异常，请登陆环境查看",
            "task_id": task_id,
            "status": 0
        }
    return result, 200


@evaluate_bp.route('/evaluate/task/docSearch', methods=['POST'])
def large_model_doc_search_task():
    """
    大模型效果评测
    :return:
    """
    msg = ""
    # 版本信息
    diff_version = request.args.get('version')
    # 环境信息
    project_env = request.args.get('env')
    # 引擎信息-faq search
    engine = request.args.get('engine')
    # 测试集文件
    file_name = request.args.get('query')

    # doc 检索参数
    llm_search_industry = request.form.get("llm_search_industry")
    threshold = request.form.get("threshold")
    dataset_id = request.form.get("dataset_id")
    dir_id = request.form.get("dir_id")
    cookie = request.form.get("cookie")

    # prompt模板，通用
    prompt_template = request.form.get('template')

    llm_search_config = {
        "llm_search_industry": int(llm_search_industry),
        "threshold": float(threshold),
        "dataset_id": str(dataset_id),
        "dir_id": str(dir_id),
        "cookie": str(cookie)
    }

    for key in llm_search_config:
        print key, ':', llm_search_config[key]

    env_info = Env.query.filter(Env.env_name == project_env).first()
    data_file = Query.query.filter(Query.file_name == file_name).first()
    data_file_path = data_file.file_path.encode('utf-8')

    config = env_info.task2dict()
    for key in config:
        print key, config[key]
    task_id = str(time.time())
    config['task_id'] = task_id

    current_app.celery.send_task("evaluate_doc_search", [config, llm_search_config, data_file_path,
                                                         task_id, prompt_template], task_id=task_id)

    create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    try:
        new_task = Task(version=config['version'],
                        env_name=config['env_name'],
                        engine=engine,
                        create_time=create_time,
                        # result_dir=,
                        task_id=task_id
                        )
        db.session.add(new_task)
        db.session.commit()
    except Exception as e:
        print(e)
        msg = e
        traceback.print_exc()

    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "开始效果评测",
            "task_id": task_id,
            "status": 0
        }

    # 平台打点
    event_tracking()
    capability_push()
    capability_push_user()
    return result, 200


@evaluate_bp.route('/evaluate/task/endToEnd', methods=['POST'])
def end_to_end_task():
    """
    端到端效果评测
    :return:
    """
    msg = ""

    # 版本信息
    diff_version = request.args.get('version')
    # 环境信息
    project_env = request.args.get('env')
    # 引擎信息
    engine = request.args.get('engine')
    # 文件Excel表
    file_name = request.args.get('query')

    # gpt用的appKey
    api_key = LargeModelConfig.api_key
    # prompt模板，通用
    prompt_template = LargeModelConfig.template
    # 正则表达式，用于提取，通用
    pattern = LargeModelConfig.default_pattern

    large_model_config = {
        'api_key': api_key,
        'prompt_template': prompt_template,
        'pattern': pattern
    }

    for key in large_model_config:
        print key, ':', large_model_config[key]

    env_info = Env.query.filter(Env.env_name == project_env).first()
    version = env_info.version
    data_file = Query.query.filter(Query.file_name == file_name).first()
    data_file_path = data_file.file_path.encode('utf-8')

    config = env_info.task2dict()
    for key in config:
        print key, config[key]
    task_id = str(time.time())
    config['task_id'] = task_id
    current_app.celery.send_task("end_to_end", [config, data_file_path, large_model_config, task_id], task_id=task_id)
    create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    try:
        new_task = Task(version=config['version'],
                        env_name=config['env_name'],
                        engine=engine,
                        create_time=create_time,
                        # result_dir=,
                        task_id=task_id
                        )
        db.session.add(new_task)
        db.session.commit()
    except Exception as e:
        print(e)
        msg = e
        traceback.print_exc()

    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "开始效果评测",
            "task_id": task_id,
            "status": 0
        }

    # 平台打点
    event_tracking()
    capability_push()
    capability_push_user()
    return result, 200

@evaluate_bp.route('/evaluate/task/stop', methods=['GET'])
def task_stop():
    """
    任务停止：task_id
    """
    msg = ""
    task_id = ""
    try:
        task_id = request.args.get('task_id').encode('utf-8')
        task = current_app.celery.AsyncResult(id=task_id, app=current_app.celery)
        print "终止任务"
        print task.state
        task.revoke(terminate=True)
    except Exception as e:
        print(e)
        msg = e
        traceback.print_exc()
    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            "msg": "任务停止成功",
            "task_id": task_id,
            "status": 0
        }

    res = json.dumps(result, encoding="utf-8", ensure_ascii=False)
    return res


@evaluate_bp.route('/evaluate/status', methods=['GET'])
def evaluate_status():
    """
    服务校验：功能; task_id
    获取执行命令，然后执行
    """
    response = {}
    try:
        task_id = request.args.get('task_id').encode('utf-8')
        task = current_app.celery.AsyncResult(id=task_id, app=current_app.celery)
        if task.state == 'PENDING':
            # 任务还没开始
            response = {
                'state': task.state,
                'status': '排队中...'
            }
        elif task.state == 'REVOKED':
            response = {
                'state': task.state,
                'status': '已终止'
            }
        elif task.state != 'FAILURE':
            info = task.info.encode('utf-8')

            response = {
                'state': task.state,
                'status': task.info
            }

        else:
            # 如果不是PENDING，或者SUCCESS，那么可能是出现异常了
            response = {
                'state': task.state,
                'status': str(task.info),  # 返回错误信息
            }
    except Exception as e:
        print(e)
        traceback.print_exc()

    return jsonify(response)


@evaluate_bp.route('/evaluate/env/list', methods=['GET'])
def env_list():
    """
    获取环境列表，返回 env_name 列表
    """
    envs_name_list = []
    msg = ''
    try:
        # 查询
        envs = Env.query.all()
        print envs
        for env in envs:
            envs_name_list.append(env.env_name)
            print('env_name:%s' % env.env_name)
    except Exception as e:
        print(e)
        traceback.print_exc()
        db.session.rollback()
    if msg != "":
        result = {
            "msg": msg,
            "status": 1
        }
    else:
        result = {
            'env_name': envs_name_list,
            "status": 0
        }

    res = json.dumps(result, encoding="utf-8", ensure_ascii=False)
    return res


@evaluate_bp.route('/evaluate/env/add', methods=['POST'])
def models_add():
    """
    添加环境; namespace, project_env
    """
    env_name = request.form.get('env_name')  #
    version = request.form.get('version')
    backend_address = request.form.get('backend_address')
    core_address = request.form.get('core_address')
    knowledge_address = request.form.get('knowledge_address')
    username = request.form.get('username')
    uid = request.form.get('uid')
    account_type = request.form.get('account_type')
    agentId = request.form.get('agentId')
    tenantId = request.form.get('tenantId')
    botToken = request.form.get('botToken')
    thread_num = request.form.get('thread_num')
    bot_name = request.form.get('bot_name')
    no_answer = request.form.get('no_answer')
    large_model_no_answer = request.form.get('large_model_no_answer')
    nlu_enable = request.form.get('nlu_enable')
    authorization = request.form.get('authorization')
    clarify_answer = request.form.get('clarify_answer')
    user_name = request.form.get('user_name')
    bot_id = request.form.get('bot_id')
    if capitalize(str(nlu_enable)) == 'True':
        nlu_enable = True
    else:
        nlu_enable = False
    new_env = Env(env_name=env_name,
                  version=version,
                  backend_address=backend_address,
                  core_address=core_address,
                  knowledge_address=knowledge_address,
                  username=username,
                  uid=uid,
                  account_type=account_type,
                  agentId=agentId,
                  tenantId=tenantId,
                  botToken=botToken,
                  authorization=authorization,
                  thread_num=thread_num,
                  nlu_enable=nlu_enable,
                  bot_name=bot_name,
                  bot_id=bot_id,
                  no_answer=no_answer,
                  large_model_no_answer=large_model_no_answer,
                  clarify_answer=clarify_answer,
                  user_name=user_name,
                  active_count=0)

    msg = ''
    try:
        if (Env.query.filter_by(env_name=env_name).count()) > 0:
            return {'code': 201, 'msg': '环境名称已存在,请更改环境名称', 'status': 1}
        else:
            db.session.add(new_env)
            db.session.commit()
            print Env.query.all()
    except Exception as e:
        print(e)
        traceback.print_exc()
        msg = e

    if msg != "":
        result = {
            "msg": msg,
            "code": 500
        }
    else:
        result = {
            "msg": "添加环境成功:{}".format("aa"),
            "code": 200
        }

    res = json.dumps(result, encoding="utf-8", ensure_ascii=False)
    return res

def add_or_update_event_tracking(execute_user, execute_time, execute_number):
    """
    更新EventTracking表
    :return:
    """
    # 首先查询是否存在具有相同 execute_user 和 execute_time 的记录
    existing_event = db.session.query(EventTracking).filter_by(
        execute_user=execute_user,
        execute_time=execute_time
    ).first()

    if existing_event:
        # 如果存在，更新 execute_number
        existing_event.execute_number = execute_number
        db.session.commit()
    else:
        # 如果不存在，插入新的记录
        new_event_tracking = EventTracking(
            execute_user=execute_user,
            execute_time=execute_time,
            execute_number=execute_number
        )
        db.session.add(new_event_tracking)
        db.session.commit()
# 平台打点
def event_tracking():
    """
    统计用户执行次数
    :return:
    """
    project_env = request.args.get('env')
    config = Env.query.filter(Env.env_name == project_env).first()

    config.active_count = config.active_count + 1
    db.session.commit()
    add_or_update_event_tracking(config.user_name, time.strftime("%Y-%m-%d", time.localtime()), config.active_count)

    user_datas = [
        {"user_name_prefix": config.user_name, "active_count": config.active_count},
    ]
    # 构建 JSON 数据
    data = {
        "platform_id": 20,
        "token": "8ee31706-d121-4e08-b920-f766c8312864",
        "user_day": time.strftime("%Y-%m-%d", time.localtime()),
        "trigger_user": config.user_name,
        "user_datas": user_datas
    }
    # 将 JSON 数据转换为字符串
    json_data = json.dumps(data)

    # 执行 curl 命令
    os.system("curl --location --request POST 'http://cne-online.baidu-int.com:8016/platform/v1/user/activeinfo' "
              "--header 'Content-Type: application/json' "
              "--data '{}'".format(json_data))

# 能力数据推送
def capability_push():
    """
    统计执行次数
    :return:
    """
    # 根据执行日期统计总执行次数
    events = db.session.query(EventTracking).filter_by(execute_time=time.strftime("%Y-%m-%d", time.localtime())).all()

    # 计算 execute_number 的总和
    total_execute_number = sum(event.execute_number for event in events)

    task_datas = [
        {"product_line_name": "29-智能客服",
         "product_name": "智能客服NGD",
         "function_tag": "tag_llm_algorithm_test",
         "task_data": {"execute_number": str(total_execute_number)}
         }
    ]
    # 构建 JSON 数据
    data = {
        "platform_id": 20,
        "token": "8ee31706-d121-4e08-b920-f766c8312864",
        "task_day": time.strftime("%Y-%m-%d", time.localtime()),
        "task_datas": task_datas
    }
    # 将 JSON 数据转换为字符串
    json_data = json.dumps(data)

    # # 执行 curl 命令
    os.system("curl --location --request POST 'http://cne-online.baidu-int.com:8016/platform/v1/task/info' "
              "--header 'Content-Type: application/json' "
              "--data '{}'".format(json_data))

def capability_push_user():
    """
    统计执行次数细分到用户
    :return:
    """
    project_env = request.args.get('env')
    # 根据执行日期统计总执行次数
    events = db.session.query(EventTracking).filter_by(execute_time=time.strftime("%Y-%m-%d", time.localtime())).all()
    config = Env.query.filter(Env.env_name == project_env).first()
    # 计算 execute_number 的总和
    total_execute_number = sum(event.execute_number for event in events)
    user_datas = [
        {"user_name_prefix": config.user_name, "data": {"execute_number": str(total_execute_number)}},
    ]
    task_datas = [
        {"product_line_name": "29-智能客服",
         "product_name": "智能客服NGD",
         "function_tag": "tag_llm_algorithm_test",
         "user_datas": user_datas
         }
    ]
    # 构建 JSON 数据
    data = {
        "platform_id": 20,
        "token": "8ee31706-d121-4e08-b920-f766c8312864",
        "task_day": time.strftime("%Y-%m-%d", time.localtime()),
        "task_datas": task_datas
    }
    # 将 JSON 数据转换为字符串
    json_data = json.dumps(data)

    # # 执行 curl 命令
    os.system("curl --location --request POST 'http://cne-online.baidu-int.com:8016/platform/v1/task/userinfo' "
              "--header 'Content-Type: application/json' "
              "--data '{}'".format(json_data))