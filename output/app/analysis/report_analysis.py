#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2022/11/01 20:05:00
"""
import os
import time
import uuid
import openpyxl
import dictdiffer
import traceback
import copy
import itertools

from openpyxl.styles import PatternFill, colors, Alignment
import sys
import logging

reload(sys)
sys.setdefaultencoding('utf8')

# 日志相关
logger = logging.getLogger("report")
logger.setLevel(logging.DEBUG)
ch = logging.StreamHandler()
ch.setLevel(logging.DEBUG)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(lineno)d - %(message)s")
ch.setFormatter(formatter)
logger.addHandler(ch)

# 定义颜色
green_fill = PatternFill(fill_type="solid", fgColor=colors.GREEN)
red_fill = PatternFill(fill_type="solid", fgColor=colors.RED)
yellow_fill = PatternFill(fill_type="solid", fgColor=colors.YELLOW)
blue_fill = PatternFill(fill_type="solid", fgColor=colors.BLUE)

# 对齐方式，水平居中
align = Alignment(vertical='center')


def get_data_from_xls(xls_path):
    """
    从xls文件中读取数据，并转为二维数组的格式
    xls_path:xls文件路径
    :param xls_path:
    :return:
    """
    # 判断文件是否存在，不存在抛出异常
    if os.path.exists(xls_path):
        data_dict = {}
        workbook = openpyxl.load_workbook(xls_path)
        sheet = workbook.worksheets[0]
        # 获取工作表中的所有行和列
        rows = sheet.max_row
        columns = sheet.max_column
        # 创建一个空的二维数组，用于存储数据
        data = []
        # 循环遍历所有行和列，将数据存储到二维数组中
        for row in range(1, rows + 1):
            row_data = []
            for column in range(1, columns + 1):
                cell = sheet.cell(row=row, column=column)
                row_data.append(cell.value)
            data.append(row_data)
        # 返回数据
        return data

    else:
        msg = "抱歉，文件不存在"
        raise Exception(msg)


def data_diff(old_table_data, new_table_data, key_index_list, diff_func):
    """
    对比两个二维数组，根据指定的diff方法进行比较
    :param old_table_data: 旧数据
    :param new_table_data: 新数据
    :param key_index_list: key在Excel中的列位置
    :param diff_func: 对比方法参数为(old_data, new_data, key_index_list)，
                      返回对比结果(新数据增加两个信息，对比结果和备注)，
                      注意，只有key相同时才进行对比，key不同时返回None即可，下面有处理新增和缺失的通用逻辑，很精妙
    :return:
    """
    table_header = copy.deepcopy(old_table_data[0])
    table_header.extend(("和版本对比结果", "备注"))
    diff_data = []
    diff_data.append(table_header)
    # 进行上升、下降、以及不变的对比
    for (old_data, new_data) in list(itertools.product(old_table_data[1:], new_table_data[1:])):
        diff_res = diff_func(old_data, new_data)
        if diff_res is None:
            continue
        else:
            diff_data.append(diff_res)
    # 进行新增和缺失的对比
    old_key_set = set()
    old_key_index = {}
    for old_row in old_table_data[1:]:
        key = tuple(old_row[i] for i in key_index_list)
        old_key_set.add(key)
        old_key_index[key] = old_row
    new_key_set = set()
    new_key_index = {}
    for new_row in new_table_data[1:]:
        key = tuple(new_row[i] for i in key_index_list)
        new_key_set.add(key)
        new_key_index[key] = new_row
    # 获取新增的key
    add_key_set = new_key_set - old_key_set
    # 获取缺失的key
    miss_key_set = old_key_set - new_key_set
    # 新增数据处理，即找到新增数据后，将结尾加上新增说明，插入到需要返回的结果中
    for add_key in add_key_set:
        add_row = copy.deepcopy(new_key_index[add_key])
        add_row.extend([u"新增query", u"本条query上个版本没有，本版本有，属于新增"])
        diff_data.append(add_row)
    # 缺失数据处理，即找到缺失数据后，将结尾加上缺失说明，插入到需要返回的结果中
    for miss_key in miss_key_set:
        miss_row = copy.deepcopy(old_key_index[miss_key])
        miss_row.extend([u"缺失query", u"本条query上版本有，本版本没有，属于缺失"])
        diff_data.append(miss_row)

    return diff_data


def save_diff_data_file(old_table_data, new_table_data, diff_data, path='.'):
    """
    保存结果文件，第一个sheet为对比结果，第二个sheet为新数据，第三个sheet为旧数据
    :param old_table_data:
    :param new_table_data:
    :param diff_data:
    :param path:
    :return:
    """
    # 创建一个新的Excel工作簿
    wb = openpyxl.Workbook()
    # 第一张工作表，对比结果
    diff_res_table = wb.active
    diff_res_table.title = u"对比结果"
    for row in diff_data:
        diff_res_table.append(row)
    # 效果上升的数据量
    effect_increase = 0
    # 效果下降的数据量
    effect_decrease = 0
    # 效果不变的数据量
    effect_unchanged = 0
    # query新增的数据量
    query_add = 0
    # query减少的数据量
    query_miss = 0
    for i in range(1, len(diff_data)):
        res_str = str(diff_data[i][-2]).strip()
        if res_str == "效果上升":
            effect_increase += 1
            # 设置该行为绿色
            for j in range(len(diff_data[i])):
                cell = diff_res_table.cell(row=i + 1, column=j + 1)
                cell.fill = green_fill
        elif res_str == "效果下降":
            effect_decrease += 1
            # 设置该行为红色
            for j in range(len(diff_data[i])):
                cell = diff_res_table.cell(row=i + 1, column=j + 1)
                cell.fill = red_fill
        elif res_str == "新增query":
            query_add += 1
            # 设置该行为蓝色
            for j in range(len(diff_data[i])):
                cell = diff_res_table.cell(row=i + 1, column=j + 1)
                cell.fill = blue_fill
        elif res_str == "缺失query":
            query_miss += 1
            # 设置该行为黄色
            for j in range(len(diff_data[i])):
                cell = diff_res_table.cell(row=i + 1, column=j + 1)
                cell.fill = yellow_fill
        else:
            effect_unchanged += 1
    result_data = [
        [None],
        ["和上版本对比结果汇总"],
        ["效果不变", "效果上升", "效果下降", "新增query", "缺失query"],
        [effect_unchanged, effect_increase, effect_decrease, query_add, query_miss]
    ]
    for row in result_data:
        diff_res_table.append(row)

    # 第二张工作表，当前版本数据
    new_data_table = wb.create_sheet(title=u"当前版本数据")
    for row in new_table_data:
        new_data_table.append(row)
    # 第三张工作表，上版本数据
    old_data_table = wb.create_sheet(title=u"上版本数据")
    for row in old_table_data:
        old_data_table.append(row)

    try:
        uid = str(uuid.uuid4())
        diff_res_path = path + '/diff/' + uid + '/'
        os.makedirs(diff_res_path)
        str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        diff_res_file = diff_res_path + "对比结果文件" + str_time + ".xlsx"
        wb.save(diff_res_file)
        wb.close()
        logger.info("diff_res_file : " + diff_res_file)
        return diff_res_file
    except Exception as e:
        s = traceback.format_exc()
        logging.error(s)
        return "ERROR"


def diff_func_tqa(old_row, new_row):
    """
    针对tqa引擎的对比方法
    :param old_row:
    :param new_row:
    :return:
    """
    old_key = old_row[1]
    new_key = new_row[1]
    if old_key == new_key:
        diff_row = copy.deepcopy(new_row)
        if str2bool(old_row[4]) == str2bool(new_row[4]):
            # 效果不变
            diff_row.extend(["效果不变", "本条query效果不变"])
            return diff_row
        elif str2bool(old_row[4]) is True and str2bool(new_row[4]) is False:
            # 效果下降
            annotated = "上版本corequery结果为:" + str(old_row[3]) + \
                        "\n本版本corequery结果为:" + str(new_row[3]) + "\n效果下降"
            diff_row.extend(["效果下降", annotated])
            return diff_row
        elif str2bool(old_row[4]) is False and str2bool(new_row[4]) is True:
            # 效果上升
            annotated = "上版本corequery结果为:" + str(old_row[3]) + \
                        "\n本版本corequery结果为:" + str(new_row[3]) + "\n效果上升"
            diff_row.extend(["效果上升", annotated])
            return diff_row
    else:
        return None


# 查表法，获得对比结果
"""
例如键值对((False, False), (False, True)): (u"效果上升", u"旧版本未命中目标结果，此版本通过澄清命中，效果上升"),
(False, False)代表旧数据是否直接命中为False，是否澄清命中为False
(False, True)代表新数据是否直接命中为False，是否澄清命中为True
(1, u"旧版本未命中目标结果，此版本通过澄清命中，效果上升")中第一个为对比结果，后面为解释内容
"""
res_map = {
    ((False, False), (False, False)): (u"效果不变", u"两版本均未命中目标结果，效果不变"),
    ((False, False), (False, True)): (u"效果上升", u"旧版本未命中目标结果，此版本通过澄清命中，效果上升"),
    ((False, False), (True, False)): (u"效果上升", u"旧版本未命中目标结果，此版本直接命中，效果上升"),
    ((False, True), (False, False)): (u"效果下降", U"旧版本通过澄清命中目标结果，此版本未命中， 效果下降"),
    ((False, True), (False, True)): (u"效果不变", u"旧版本通过澄清命中目标结果，此版本也通过澄清命中，效果不变"),
    ((False, True), (True, False)): (u"效果上升", u"旧版本通过澄清命中目标结果，此版本直接命中，效果上升"),
    ((True, False), (False, False)): (u"效果下降", u"旧版本直接命中目标结果，此版本未命中，效果下降"),
    ((True, False), (False, True)): (u"效果下降", u"旧版本直接命中目标结果，此版本通过澄清命中，效果下降"),
    ((True, False), (True, False)): (u"效果不变", u"旧版本直接命中目标结果，此版本也直接命中，效果不变")
}


def diff_func_intent(old_row, new_row):
    """
    针对intent的对比方法
    :param old_row:
    :param new_row:
    :return:
    """
    old_key = str(old_row[0]).strip()
    new_key = str(new_row[0]).strip()
    if old_key == new_key:
        map_key = ((str2bool(old_row[4]), str2bool(old_row[6])),
                   (str2bool(new_row[4]), str2bool(new_row[6])))
        diff_res = res_map[map_key]
        diff_row = copy.deepcopy(new_row)
        diff_row.extend(diff_res)
        return diff_row
    else:
        return None


def diff_func_entity(old_row, new_row):
    """
    针对entity的对比方法
    :param old_row:
    :param new_row:
    :return:
    """
    old_key = (str(old_row[0]).strip(), str(old_row[1]).strip(), str(old_row[2]).strip())
    new_key = (str(new_row[0]).strip(), str(new_row[1]).strip(), str(new_row[2]).strip())
    if old_key == new_key:
        diff_row = copy.deepcopy(new_row)
        if str2bool(old_row[4]) == str2bool(new_row[4]):
            # 效果不变
            diff_row.extend(["效果不变", "本条query效果不变"])
            return diff_row
        elif str2bool(old_row[4]) is True and str2bool(new_row[4]) is False:
            # 效果下降
            annotated = "上版本corequery结果为:" + str(old_row[3]) + \
                        "\n本版本corequery结果为:" + str(new_row[3]) + "\n效果下降"
            diff_row.extend(["效果下降", annotated])
            return diff_row
        elif str2bool(old_row[4]) is False and str2bool(new_row[4]) is True:
            # 效果上升
            annotated = "上版本corequery结果为:" + str(old_row[3]) + \
                        "\n本版本corequery结果为:" + str(new_row[3]) + "\n效果上升"
            diff_row.extend(["效果上升", annotated])
            return diff_row
    else:
        return None


def diff_func_faq(old_row, new_row):
    """
    针对faq的对比方法
    :param old_row:
    :param new_row:
    :return:
    """
    old_key = str(old_row[0]).strip()
    new_key = str(new_row[0]).strip()
    if old_key == new_key:
        map_key = ((str2bool(old_row[3]), str2bool(old_row[5])),
                   (str2bool(new_row[3]), str2bool(new_row[5])))
        diff_res = res_map[map_key]
        diff_row = copy.deepcopy(new_row)
        diff_row.extend(diff_res)
        return diff_row
    else:
        return None


def diff_func_attitude(old_row, new_row):
    """
    针对attitude的对比方法
    :param old_row:
    :param new_row:
    :return:
    """
    old_key = str(old_row[0]).strip()
    new_key = str(new_row[0]).strip()
    if old_key == new_key:
        diff_row = copy.deepcopy(new_row)
        if str2bool(old_row[4]) == str2bool(new_row[4]):
            # 效果不变
            diff_row.extend(["效果不变", "本条query效果不变"])
            return diff_row
        elif str2bool(old_row[4]) is True and str2bool(new_row[4]) is False:
            # 效果下降
            annotated = "上版本corequery结果为:" + str(old_row[2]) + \
                        "\n本版本corequery结果为:" + str(new_row[2]) + "\n效果下降"
            diff_row.extend(["效果下降", annotated])
            return diff_row
        elif str2bool(old_row[4]) is False and str2bool(new_row[4]) is True:
            # 效果上升
            annotated = "上版本corequery结果为:" + str(old_row[2]) + \
                        "\n本版本corequery结果为:" + str(new_row[2]) + "\n效果上升"
            diff_row.extend(["效果上升", annotated])
            return diff_row
    else:
        return None


def diff_func_end2end_in(old_row, new_row):
    """
    针对end2end_in的对比方法
    :param old_row:
    :param new_row:
    :return:
    """
    old_key = tuple(str(old_row[0]).strip(), str(old_row[1]).strip())
    new_key = tuple(str(new_row[0]).strip(), str(new_row[1]).strip())
    if old_key == new_key:
        diff_row = copy.deepcopy(new_row)
        if str2bool(old_row[6]) == str2bool(new_row[6]):
            # 效果不变
            diff_row.extend(["效果不变", "本条query效果不变"])
            return diff_row
        elif str2bool(old_row[6]) is True and str2bool(new_row[6]) is False:
            # 效果下降
            annotated = "上版本corequery结果为:" + str(old_row[4]) + \
                        "\n本版本corequery结果为:" + str(new_row[4]) + "\n效果下降"
            diff_row.extend(["效果下降", annotated])
            return diff_row
        elif str2bool(old_row[6]) is False and str2bool(new_row[6]) is True:
            # 效果上升
            annotated = "上版本corequery结果为:" + str(old_row[4]) + \
                        "\n本版本corequery结果为:" + str(new_row[4]) + "\n效果上升"
            diff_row.extend(["效果上升", annotated])
            return diff_row
    else:
        return None


def report_compare_diff_by_tqa_v2(old_report, new_report, path):
    """
    报告比较,tqa
    :param old_report:
    :param new_report:
    :param path:
    :return:
    """
    old_data = get_data_from_xls(old_report)
    new_data = get_data_from_xls(new_report)
    diff_data = data_diff(old_data, new_data, [1], diff_func_tqa)
    diff_file_path = save_diff_data_file(old_data, new_data, diff_data, path)
    return diff_file_path


def report_compare_diff_by_intent_v2(old_report, new_report, path):
    """
    报告比较,intent
    :param old_report:
    :param new_report:
    :param path:
    :return:
    """
    old_data = get_data_from_xls(old_report)
    new_data = get_data_from_xls(new_report)
    diff_data = data_diff(old_data, new_data, [0], diff_func_intent)
    diff_file_path = save_diff_data_file(old_data, new_data, diff_data, path)
    return diff_file_path


def report_compare_diff_by_entity_v2(old_report, new_report, path):
    """
    报告比较,entity
    :param old_report:
    :param new_report:
    :param path:
    :return:
    """
    old_data = get_data_from_xls(old_report)
    new_data = get_data_from_xls(new_report)
    print new_data
    diff_data = data_diff(old_data, new_data, [0, 1, 2], diff_func_entity)
    diff_file_path = save_diff_data_file(old_data, new_data, diff_data, path)
    return diff_file_path


def report_compare_diff_by_faq_v2(old_report, new_report, path):
    """
    报告比较,faq
    :param old_report:
    :param new_report:
    :param path:
    :return:
    """
    old_data = get_data_from_xls(old_report)
    new_data = get_data_from_xls(new_report)
    diff_data = data_diff(old_data, new_data, [0], diff_func_faq)
    diff_file_path = save_diff_data_file(old_data, new_data, diff_data, path)
    return diff_file_path

def report_compare_diff_by_attitude_v2(old_report, new_report, path):
    """
    报告比较,attitude
    :param old_report:
    :param new_report:
    :param path:
    :return:
    """
    old_data = get_data_from_xls(old_report)
    new_data = get_data_from_xls(new_report)
    diff_data = data_diff(old_data, new_data, [0], diff_func_attitude)
    diff_file_path = save_diff_data_file(old_data, new_data, diff_data, path)
    return diff_file_path

def report_compare_diff_by_end2end_in_v2(old_report, new_report, path):
    """
    报告比较,end2end_in
    :param old_report:
    :param new_report:
    :param path:
    :return:
    """
    old_data = get_data_from_xls(old_report)
    new_data = get_data_from_xls(new_report)
    diff_data = data_diff(old_data, new_data, [0, 1], diff_func_end2end_in)
    diff_file_path = save_diff_data_file(old_data, new_data, diff_data, path)
    return diff_file_path


# def report_compare_diff_by_tqa(old_report, new_report, path):
#     """
#     根据报告地址，对比两报告之间的差异
#     """
#     try:
#         start = time.time()
#         old_wb = openpyxl.load_workbook(filename=old_report)
#         new_wb = openpyxl.load_workbook(filename=new_report)
#         old_wb.active
#         new_wb.active
#         old_table = old_wb.worksheets[0]
#         new_table = new_wb.worksheets[0]
#         # 存放query和实际结果
#         old_res_dict, new_res_dict = {}, {}
#         # 存放query和行号
#         old_index_dict, new_index_dict = {}, {}
#         for index in range(2, old_table.max_row + 1):
#             if old_table['B' + str(index)].value is None or old_table['B' + str(index)].value == "":
#                 continue
#             key = old_table['B' + str(index)].value.strip()
#             value = old_table['E' + str(index)].value
#             old_res_dict.update({key: str2bool(value)})
#             old_index_dict.update({key: index})
#
#         for index in range(2, new_table.max_row + 1):
#             if new_table['B' + str(index)].value is None or new_table['B' + str(index)].value == "":
#                 continue
#             key = new_table['B' + str(index)].value.strip()
#             value = new_table['E' + str(index)].value
#             new_res_dict.update({key: str2bool(value)})
#             new_index_dict.update({key: index})
#
#         # print(old_res_dict)
#         # print(new_res_dict)
#         # print(old_index_dict)
#         # print(new_index_dict)
#
#         diff_res = []
#         for diff in list(dictdiffer.diff(old_res_dict, new_res_dict)):
#             logger.info(diff)
#             diff_res.append(diff)
#
#         logger.info("对比结果 diff_res : " + str(diff_res))
#
#         diff_res_col = new_table.max_column + 1
#         new_table.cell(column=diff_res_col, row=1).value = '和上版本对比结果'
#         # 统计数据，分别代表
#         # 1. 效果下降的query条数
#         # 2. 效果提升的query条数
#         # 3. 新增的query条数
#         # 4. 缺失的query条数
#         flag1, flag2, flag3, flag4 = 0, 0, 0, 0
#         for change in diff_res:
#             if change[0] == "change":
#                 query = ''.join(change[1])
#                 text = "前版本corequery实际结果为:" + str(old_table.cell(row=old_index_dict[query], column=4).value) + \
#                        "\n本版本corequery实际结果为:" + str(new_table.cell(row=new_index_dict[query], column=4).value)
#                 # new_table['F' + str(new_index_dict[query])] = text
#                 # new_table['F' + str(new_index_dict[query])].alignment = Alignment(wrapText=True)
#                 # change[2]格式为(True, False)
#                 # 设置单元格颜色
#                 if str2bool(change[2][0]) and not str2bool(change[2][1]):
#                     # 设置颜色范围
#                     for i in range(1, diff_res_col + 1):
#                         new_table.cell(row=new_index_dict[query], column=i).fill = red_fill
#                         new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                         # new_table['F' + str(new_index_dict[query])] = text + ',效果下降'
#                     new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果下降'
#                     new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                     new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                     flag1 = flag1 + 1
#                 else:
#                     for i in range(1, diff_res_col + 1):
#                         new_table.cell(row=new_index_dict[query], column=i).fill = green_fill
#                         new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                         # new_table['F' + str(new_index_dict[query])] = text + ',效果提升'
#                     new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果提升'
#                     new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                     new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                     flag2 = flag2 + 1
#             elif change[0] == "add":
#                 # 表示上个版本没有此query，本版本新增了
#                 for res in change[2]:
#                     text = "本条为新增case，上版本没有"
#                     query = res[0]
#                     # new_table['F' + str(new_index_dict[query])] = text
#                     new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text
#                     for i in range(1, diff_res_col + 1):
#                         new_table.cell(row=new_index_dict[query], column=i).fill = yellow_fill
#                     new_table.row_dimensions[new_index_dict[query]].height = 14
#                     flag3 = flag3 + 1
#             elif change[0] == "remove":
#                 new_table.append(['以下数据为上版本有，本版本没有的query'])
#                 # 表示上个版本有此query，本版本没有了
#                 for res in change[2]:
#                     cows = old_index_dict[res[0]]
#                     add_cows = old_table[str(cows)]
#                     lists = []
#                     for add_cell in add_cows:
#                         lists.append(add_cell.value)
#                     new_table.append(lists)
#                     new_table.row_dimensions[new_index_dict[query]].height = 14
#                     flag4 = flag4 + 1
#
#         new_table.append([None])
#         new_table.append(['对比结果统计'])
#         new_table.append(['效果下降', '效果提升', '新增query', '缺失query'])
#         new_table.append([flag1, flag2, flag3, flag4])
#
#         # 复制文件并重命名
#         uid = str(uuid.uuid4())
#         diff_res_path = path + '/diff/' + uid + '/'
#         os.makedirs(diff_res_path)
#         str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
#         diff_res_file = diff_res_path + "对比结果文件" + str_time + ".xlsx"
#         new_wb.save(diff_res_file)
#         new_wb.close()
#         old_wb.close()
#         logger.info("diff_res_file : " + diff_res_file)
#         end = time.time()
#         logger.info("对比耗时 : " + str(end - start))
#         return diff_res_file
#     except Exception as e:
#         s = traceback.format_exc()
#         logging.error(s)
#         return "ERROR"


# def report_compare_diff_by_intent(old_report, new_report, path):
#     """
#     意图文件对比
#     :param old_report: 老文件地址
#     :param new_report: 新文件地址
#     :param path: 放置路径
#     :return: 结果文件路径
#     """
#     start = time.time()
#     old_wb = openpyxl.load_workbook(filename=old_report)
#     new_wb = openpyxl.load_workbook(filename=new_report)
#     old_wb.active
#     new_wb.active
#     old_table = old_wb.worksheets[0]
#     new_table = new_wb.worksheets[0]
#     # 存放query和实际结果
#     old_res_dict, new_res_dict = {}, {}
#     # 存放query和行号
#     old_index_dict, new_index_dict = {}, {}
#     print(old_table.max_row)
#     for index in range(2, old_table.max_row + 1):
#         if old_table['A' + str(index)].value is None or old_table['A' + str(index)].value == "":
#             continue
#         key = old_table['A' + str(index)].value.strip()
#         value = old_table['E' + str(index)].value
#         old_res_dict.update({key: str2bool(value)})
#         old_index_dict.update({key: index})
#
#     for index in range(2, new_table.max_row + 1):
#         if new_table['A' + str(index)].value is None or new_table['A' + str(index)].value == "":
#             continue
#         key = new_table['A' + str(index)].value.strip()
#         value = new_table['E' + str(index)].value
#         new_res_dict.update({key: str2bool(value)})
#         new_index_dict.update({key: index})
#
#     diff_res = []
#     for diff in list(dictdiffer.diff(old_res_dict, new_res_dict)):
#         print(diff)
#         diff_res.append(diff)
#
#     print(diff_res)
#
#     diff_res_col = new_table.max_column + 1
#     new_table.cell(column=diff_res_col, row=1).value = '和上版本对比结果'
#     # 统计数据，分别代表
#     # 1. 效果下降的query条数
#     # 2. 效果提升的query条数
#     # 3. 新增的query条数
#     # 4. 缺失的query条数
#     flag1, flag2, flag3, flag4 = 0, 0, 0, 0
#
#     for change in diff_res:
#         if change[0] == "change":
#             query = ''.join(change[1])
#             text = "前版本corequery实际结果为:" + str(old_table.cell(row=old_index_dict[query], column=4).value) + \
#                    "\n本版本corequery实际结果为:" + str(new_table.cell(row=new_index_dict[query], column=4).value)
#             print(type(new_index_dict[query]))
#             # new_table['F' + str(new_index_dict[query])] = text
#             # new_table['F' + str(new_index_dict[query])].alignment = Alignment(wrapText=True)
#             # change[2]格式为(True, False)
#             # 设置单元格颜色
#             if str2bool(change[2][0]) and not str2bool(change[2][1]):
#                 # 设置颜色范围
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = red_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag1 = flag1 + 1
#             else:
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = green_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag2 = flag2 + 1
#         elif change[0] == "add":
#             # 表示上个版本没有此query，本版本新增了
#             for res in change[2]:
#                 text = "本条为新增case，上版本没有"
#                 query = res[0]
#                 # new_table['F' + str(new_index_dict[query])] = text
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = yellow_fill
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag3 = flag3 + 1
#         elif change[0] == "remove":
#             new_table.append(['以下数据为上版本有，本版本没有的query'])
#             # 表示上个版本有此query，本版本没有了
#             for res in change[2]:
#                 cows = old_index_dict[res[0]]
#                 add_cows = old_table[str(cows)]
#                 print(add_cows)
#                 lists = []
#                 for add_cell in add_cows:
#                     lists.append(add_cell.value)
#                 new_table.append(lists)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag4 = flag4 + 1
#
#     new_table.append([None])
#     new_table.append(['对比结果统计'])
#     new_table.append(['效果下降', '效果提升', '新增query', '缺失query'])
#     new_table.append([flag1, flag2, flag3, flag4])
#
#     # 复制文件并重命名
#     uid = str(uuid.uuid4())
#     diff_res_path = path + '/diff/' + uid + '/'
#     os.makedirs(diff_res_path)
#     str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
#     diff_res_file = diff_res_path + "对比结果文件" + str_time + ".xlsx"
#     new_wb.save(diff_res_file)
#     new_wb.close()
#     old_wb.close()
#     print(diff_res_file)
#     end = time.time()
#     print('耗时' + str(end - start))
#     return diff_res_file


# def report_compare_diff_by_entity(old_report, new_report, path):
#     """
#     实体文件对比
#     :param old_report:
#     :param new_report:
#     :param path:
#     :return:
#     """
#     start = time.time()
#     old_wb = openpyxl.load_workbook(filename=old_report)
#     new_wb = openpyxl.load_workbook(filename=new_report)
#     old_wb.active
#     new_wb.active
#     old_table = old_wb.worksheets[0]
#     new_table = new_wb.worksheets[0]
#     # 存放query和实际结果
#     old_res_dict, new_res_dict = {}, {}
#     # 存放query和行号
#     old_index_dict, new_index_dict = {}, {}
#     print(old_table.max_row)
#     for index in range(2, old_table.max_row + 1):
#         key = old_table['A' + str(index)].value.strip()
#         value = old_table['E' + str(index)].value
#         old_res_dict.update({key: str2bool(value)})
#         old_index_dict.update({key: index})
#
#     for index in range(2, new_table.max_row + 1):
#         key = new_table['A' + str(index)].value.strip()
#         value = new_table['E' + str(index)].value
#         new_res_dict.update({key: str2bool(value)})
#         new_index_dict.update({key: index})
#
#     diff_res = []
#     for diff in list(dictdiffer.diff(old_res_dict, new_res_dict)):
#         print(diff)
#         diff_res.append(diff)
#
#     print(diff_res)
#
#     diff_res_col = new_table.max_column + 1
#     new_table.cell(column=diff_res_col, row=1).value = '和上版本对比结果'
#     # 统计数据，分别代表
#     # 1. 效果下降的query条数
#     # 2. 效果提升的query条数
#     # 3. 新增的query条数
#     # 4. 缺失的query条数
#     flag1, flag2, flag3, flag4 = 0, 0, 0, 0
#
#     for change in diff_res:
#         if change[0] == "change":
#             query = ''.join(change[1])
#             text = "前版本corequery实际结果为:" + str(old_table.cell(row=old_index_dict[query], column=4).value) + \
#                    "\n本版本corequery实际结果为:" + str(new_table.cell(row=new_index_dict[query], column=4).value)
#             print(type(new_index_dict[query]))
#             # new_table['F' + str(new_index_dict[query])] = text
#             # new_table['F' + str(new_index_dict[query])].alignment = Alignment(wrapText=True)
#             # change[2]格式为(True, False)
#             # 设置单元格颜色
#             if str2bool(change[2][0]) and not str2bool(change[2][1]):
#                 # 设置颜色范围
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = red_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag1 = flag1 + 1
#             else:
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = green_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag2 = flag2 + 1
#         elif change[0] == "add":
#             # 表示上个版本没有此query，本版本新增了
#             for res in change[2]:
#                 text = "本条为新增case，上版本没有"
#                 query = res[0]
#                 # new_table['F' + str(new_index_dict[query])] = text
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = yellow_fill
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag3 = flag3 + 1
#         elif change[0] == "remove":
#             new_table.append(['以下数据为上版本有，本版本没有的query'])
#             # 表示上个版本有此query，本版本没有了
#             for res in change[2]:
#                 cows = old_index_dict[res[0]]
#                 add_cows = old_table[str(cows)]
#                 print(add_cows)
#                 lists = []
#                 for add_cell in add_cows:
#                     lists.append(add_cell.value)
#                 new_table.append(lists)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag4 = flag4 + 1
#
#     new_table.append([None])
#     new_table.append(['对比结果统计'])
#     new_table.append(['效果下降', '效果提升', '新增query', '缺失query'])
#     new_table.append([flag1, flag2, flag3, flag4])
#
#     # 复制文件并重命名
#     uid = str(uuid.uuid4())
#     diff_res_path = path + '/diff/' + uid + '/'
#     os.makedirs(diff_res_path)
#     str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
#     diff_res_file = diff_res_path + "对比结果文件" + str_time + ".xlsx"
#     new_wb.save(diff_res_file)
#     new_wb.close()
#     old_wb.close()
#     print(diff_res_file)
#     end = time.time()
#     print('耗时' + str(end - start))
#     return diff_res_file


# def report_compare_diff_by_attitude(old_report, new_report, path):
#     """
#     态度文件对比
#     :param old_report:
#     :param new_report:
#     :param path:
#     :return:
#     """
#     start = time.time()
#     old_wb = openpyxl.load_workbook(filename=old_report)
#     new_wb = openpyxl.load_workbook(filename=new_report)
#     old_wb.active
#     new_wb.active
#     old_table = old_wb.worksheets[0]
#     new_table = new_wb.worksheets[0]
#     # 存放query和实际结果
#     old_res_dict, new_res_dict = {}, {}
#     # 存放query和行号
#     old_index_dict, new_index_dict = {}, {}
#     print(old_table.max_row)
#     for index in range(2, old_table.max_row + 1):
#         key = old_table['A' + str(index)].value.strip()
#         value = old_table['E' + str(index)].value
#         old_res_dict.update({key: str2bool(value)})
#         old_index_dict.update({key: index})
#
#     for index in range(2, new_table.max_row + 1):
#         key = new_table['A' + str(index)].value.strip()
#         value = new_table['E' + str(index)].value
#         new_res_dict.update({key: str2bool(value)})
#         new_index_dict.update({key: index})
#
#     diff_res = []
#     for diff in list(dictdiffer.diff(old_res_dict, new_res_dict)):
#         print(diff)
#         diff_res.append(diff)
#
#     print(diff_res)
#
#     diff_res_col = new_table.max_column + 1
#     new_table.cell(column=diff_res_col, row=1).value = '和上版本对比结果'
#     # 统计数据，分别代表
#     # 1. 效果下降的query条数
#     # 2. 效果提升的query条数
#     # 3. 新增的query条数
#     # 4. 缺失的query条数
#     flag1, flag2, flag3, flag4 = 0, 0, 0, 0
#
#     for change in diff_res:
#         if change[0] == "change":
#             query = ''.join(change[1])
#             text = "前版本corequery实际结果为:" + str(old_table.cell(row=old_index_dict[query], column=4).value) + \
#                    "\n本版本corequery实际结果为:" + str(new_table.cell(row=new_index_dict[query], column=4).value)
#             print(type(new_index_dict[query]))
#             # new_table['F' + str(new_index_dict[query])] = text
#             # new_table['F' + str(new_index_dict[query])].alignment = Alignment(wrapText=True)
#             # change[2]格式为(True, False)
#             # 设置单元格颜色
#             if str2bool(change[2][0]) and not str2bool(change[2][1]):
#                 # 设置颜色范围
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = red_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag1 = flag1 + 1
#             else:
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = green_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag2 = flag2 + 1
#         elif change[0] == "add":
#             # 表示上个版本没有此query，本版本新增了
#             for res in change[2]:
#                 text = "本条为新增case，上版本没有"
#                 query = res[0]
#                 # new_table['F' + str(new_index_dict[query])] = text
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = yellow_fill
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag3 = flag3 + 1
#         elif change[0] == "remove":
#             new_table.append(['以下数据为上版本有，本版本没有的query'])
#             # 表示上个版本有此query，本版本没有了
#             for res in change[2]:
#                 cows = old_index_dict[res[0]]
#                 add_cows = old_table[str(cows)]
#                 print(add_cows)
#                 lists = []
#                 for add_cell in add_cows:
#                     lists.append(add_cell.value)
#                 new_table.append(lists)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag4 = flag4 + 1
#
#     new_table.append([None])
#     new_table.append(['对比结果统计'])
#     new_table.append(['效果下降', '效果提升', '新增query', '缺失query'])
#     new_table.append([flag1, flag2, flag3, flag4])
#
#     # 复制文件并重命名
#     uid = str(uuid.uuid4())
#     diff_res_path = path + '/diff/' + uid + '/'
#     os.makedirs(diff_res_path)
#     str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
#     diff_res_file = diff_res_path + "对比结果文件" + str_time + ".xlsx"
#     new_wb.save(diff_res_file)
#     new_wb.close()
#     old_wb.close()
#     print(diff_res_file)
#     end = time.time()
#     print('耗时' + str(end - start))
#     return diff_res_file


# def report_compare_diff_by_chitchat(old_report, new_report, path):
#     """
#     闲聊的对比
#     :param old_report:
#     :param new_report:
#     :param path:
#     :return:
#     """
#     start = time.time()
#     old_wb = openpyxl.load_workbook(filename=old_report)
#     new_wb = openpyxl.load_workbook(filename=new_report)
#     old_wb.active
#     new_wb.active
#     old_table = old_wb.worksheets[0]
#     new_table = new_wb.worksheets[0]
#     # 存放query和实际结果
#     old_res_dict, new_res_dict = {}, {}
#     # 存放query和行号
#     old_index_dict, new_index_dict = {}, {}
#     print(old_table.max_row)
#     for index in range(2, old_table.max_row + 1):
#         key = old_table['A' + str(index)].value.strip()
#         value = old_table['E' + str(index)].value
#         old_res_dict.update({key: str2bool(value)})
#         old_index_dict.update({key: index})
#
#     for index in range(2, new_table.max_row + 1):
#         key = new_table['A' + str(index)].value.strip()
#         value = new_table['E' + str(index)].value
#         new_res_dict.update({key: str2bool(value)})
#         new_index_dict.update({key: index})
#
#     diff_res = []
#     for diff in list(dictdiffer.diff(old_res_dict, new_res_dict)):
#         print(diff)
#         diff_res.append(diff)
#
#     print(diff_res)
#
#     diff_res_col = new_table.max_column + 1
#     new_table.cell(column=diff_res_col, row=1).value = '和上版本对比结果'
#     # 统计数据，分别代表
#     # 1. 效果下降的query条数
#     # 2. 效果提升的query条数
#     # 3. 新增的query条数
#     # 4. 缺失的query条数
#     flag1, flag2, flag3, flag4 = 0, 0, 0, 0
#
#     for change in diff_res:
#         if change[0] == "change":
#             query = ''.join(change[1])
#             text = "前版本corequery实际结果为:" + str(old_table.cell(row=old_index_dict[query], column=4).value) + \
#                    "\n本版本corequery实际结果为:" + str(new_table.cell(row=new_index_dict[query], column=4).value)
#             print(type(new_index_dict[query]))
#             # new_table['F' + str(new_index_dict[query])] = text
#             # new_table['F' + str(new_index_dict[query])].alignment = Alignment(wrapText=True)
#             # change[2]格式为(True, False)
#             # 设置单元格颜色
#             if str2bool(change[2][0]) and not str2bool(change[2][1]):
#                 # 设置颜色范围
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = red_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag1 = flag1 + 1
#             else:
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = green_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text + '\n效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag2 = flag2 + 1
#         elif change[0] == "add":
#             # 表示上个版本没有此query，本版本新增了
#             for res in change[2]:
#                 text = "本条为新增case，上版本没有"
#                 query = res[0]
#                 # new_table['F' + str(new_index_dict[query])] = text
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = yellow_fill
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag3 = flag3 + 1
#         elif change[0] == "remove":
#             new_table.append(['以下数据为上版本有，本版本没有的query'])
#             # 表示上个版本有此query，本版本没有了
#             for res in change[2]:
#                 cows = old_index_dict[res[0]]
#                 add_cows = old_table[str(cows)]
#                 print(add_cows)
#                 lists = []
#                 for add_cell in add_cows:
#                     lists.append(add_cell.value)
#                 new_table.append(lists)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag4 = flag4 + 1
#
#     new_table.append([None])
#     new_table.append(['对比结果统计'])
#     new_table.append(['效果下降', '效果提升', '新增query', '缺失query'])
#     new_table.append([flag1, flag2, flag3, flag4])
#
#     # 复制文件并重命名
#     uid = str(uuid.uuid4())
#     diff_res_path = path + '/diff/' + uid + '/'
#     os.makedirs(diff_res_path)
#     str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
#     diff_res_file = diff_res_path + "对比结果文件" + str_time + ".xlsx"
#     new_wb.save(diff_res_file)
#     new_wb.close()
#     old_wb.close()
#     print(diff_res_file)
#     end = time.time()
#     print('耗时' + str(end - start))
#     return diff_res_file


# def report_compare_diff_by_faq(old_report, new_report, path):
#     """
#     faq的对比
#     :param old_report:
#     :param new_report:
#     :param path:
#     :return:
#     """
#     start = time.time()
#     old_wb = openpyxl.load_workbook(filename=old_report)
#     new_wb = openpyxl.load_workbook(filename=new_report)
#     old_wb.active
#     new_wb.active
#     old_table = old_wb.worksheets[0]
#     new_table = new_wb.worksheets[0]
#     # 存放query和实际结果
#     old_res_dict, new_res_dict = {}, {}
#     # 存放query和行号
#     old_index_dict, new_index_dict = {}, {}
#     # print(old_table.max_row)
#
#     for index in range(2, old_table.max_row + 1):
#         if old_table['A' + str(index)].value is None or old_table['A' + str(index)].value == "":
#             continue
#         key = old_table['A' + str(index)].value.strip()
#         faq_res = old_table['D' + str(index)].value
#         clarify_res = old_table['F' + str(index)].value
#         old_res_dict.update({key: '[{},{}]'.format(str2int(faq_res), str2int(clarify_res))})
#         old_index_dict.update({key: index})
#
#     # print old_res_dict
#
#     for index in range(2, new_table.max_row + 1):
#         if new_table['A' + str(index)].value is None or new_table['A' + str(index)].value == "":
#             continue
#         key = new_table['A' + str(index)].value.strip()
#         faq_res = new_table['D' + str(index)].value
#         clarify_res = new_table['F' + str(index)].value
#         new_res_dict.update({key: '[{},{}]'.format(str2int(faq_res), str2int(clarify_res))})
#         new_index_dict.update({key: index})
#
#     # print new_res_dict
#
#     diff_res = []
#     for diff in list(dictdiffer.diff(old_res_dict, new_res_dict)):
#         # print(diff)
#         diff_res.append(diff)
#
#     print(diff_res)
#
#     diff_res_col = new_table.max_column + 1
#     new_table.cell(column=diff_res_col, row=1).value = '和上版本对比结果'
#     # 统计数据，分别代表
#     # 1. 效果下降的query条数
#     # 2. 效果提升的query条数
#     # 3. 新增的query条数
#     # 4. 缺失的query条数
#     flag1, flag2, flag3, flag4 = 0, 0, 0, 0
#     # ('change',u'\u64a4\u9500\u5feb\u9012\u6295\u8bc9\u600e\u4e48\u64a4',('[1,0]', '[0,0]'))
#     for change in diff_res:
#         if change[0] == "change":
#             query = ''.join(change[1])
#             if faq_res_compare(change[2][0], change[2][1]) > 0:
#                 # 效果上升
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = green_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = '\n效果提升'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag2 = flag2 + 1
#             else:
#                 # 效果下降
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = red_fill
#                     new_table.cell(row=new_index_dict[query], column=i).alignment = align
#                     # new_table['F' + str(new_index_dict[query])] = text + ',效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = '\n效果下降'
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).alignment = Alignment(wrapText=True)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14 * 3
#                 flag1 = flag1 + 1
#         elif change[0] == "add":
#             # 表示上个版本没有此query，本版本新增了
#             for res in change[2]:
#                 text = "本条为新增case，上版本没有"
#                 query = res[0]
#                 # new_table['F' + str(new_index_dict[query])] = text
#                 new_table.cell(row=new_index_dict[query], column=diff_res_col).value = text
#                 for i in range(1, diff_res_col + 1):
#                     new_table.cell(row=new_index_dict[query], column=i).fill = yellow_fill
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag3 = flag3 + 1
#         elif change[0] == "remove":
#             new_table.append(['以下数据为上版本有，本版本没有的query'])
#             # 表示上个版本有此query，本版本没有了
#             for res in change[2]:
#                 cows = old_index_dict[res[0]]
#                 add_cows = old_table[str(cows)]
#                 print(add_cows)
#                 lists = []
#                 for add_cell in add_cows:
#                     lists.append(add_cell.value)
#                 new_table.append(lists)
#                 new_table.row_dimensions[new_index_dict[query]].height = 14
#                 flag4 = flag4 + 1
#
#     new_table.append([None])
#     new_table.append(['对比结果统计'])
#     new_table.append(['效果下降', '效果提升', '新增query', '缺失query'])
#     new_table.append([flag1, flag2, flag3, flag4])
#
#     # 复制文件并重命名
#     uid = str(uuid.uuid4())
#     diff_res_path = path + '/diff/' + uid + '/'
#     os.makedirs(diff_res_path)
#     str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
#     diff_res_file = diff_res_path + "对比结果文件" + str_time + ".xlsx"
#     new_wb.save(diff_res_file)
#     new_wb.close()
#     old_wb.close()
#     print(diff_res_file)
#     end = time.time()
#     print('耗时' + str(end - start))
#     return diff_res_file


# def faq_res_compare(old_data_str, new_data_str):
#     """
#     :param old_data_str: 旧数据的faq的结果 "[1,0]"代表命中标准问，没有命中澄清
#     :param new_data_str: 新数据的faq的结果 "[0,1]"代表没有命中标准问，命中澄清
#     :return: 1效果上升；-1效果下降；0效果不变
#     """
#     old_data = [int(old_data_str[1]), int(old_data_str[3])]
#     new_data = [int(new_data_str[1]), int(new_data_str[3])]
#     if old_data[0] > new_data[0]:
#         # 效果下降
#         return -1
#     elif old_data[0] < new_data[0]:
#         # 效果上升
#         return 1
#     else:
#         return new_data[1] - old_data[1]


# def str2int(v):
#     """
#     将string变为int
#     :param v:
#     :return:
#     """
#     if v in ("true", "1", "TRUE", "True", 1, True):
#         return 1
#     return 0


def str2bool(v):
    """
    将字符串中值为"true", "1", "TRUE", "True"的转为bool类型的true
    主要是为了解决前后两个版本中core query测试结论类型不一样的问题
    一个版本为TRUE，一个为"TRUE"比较结果一定不同
    """
    if isinstance(v, bool):
        return v
    v = str(v).strip()
    return v.lower() in ("true", "1")


if __name__ == '__main__':
    # new_file = '/Users/<USER>/Documents/faq_evaluate_result_20230427174152_个性化排序模型.xlsx'
    # old_file = '/Users/<USER>/Documents/个性化faq_evaluate_result_711_1.xlsx'
    # report_compare_diff_by_faq(old_file, new_file, "./")
    old_path = '/Users/<USER>/Downloads/sys_intents_evaluate_result_20211125185511_old.xlsx'
    old_data = get_data_from_xls(old_path)
    new_path = '/Users/<USER>/Downloads/sys_intents_evaluate_result_20211125185511_new.xlsx'
    new_data = get_data_from_xls(new_path)
    diff_data = data_diff(old_data, new_data, [0], diff_func=diff_func_intent)
    path = save_diff_data_file(old_data, new_data, diff_data)
    print path
