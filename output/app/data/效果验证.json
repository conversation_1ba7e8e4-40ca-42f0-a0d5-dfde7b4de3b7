{"agentType": 1, "sign": "7e180f6c50eb492318c4343961fb4e65", "exportData": {"dialog": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "dialogNodes": [], "dialogProcess": []}, "chitchat": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "chitchat": []}, "bot": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "bot": [{"id": "7a1c671d-bcc4-460b-b1ea-d5ce6c06d87d", "name": "test", "created": 1600848708000, "updated": 1600848708000, "createdUserId": "4308361932", "createdUserName": "wlj-test", "lastEditUserId": "4308361932", "lastEditUserName": "wlj-test", "auditStatus": "UNVERIFIED", "publishStatus": "OFFLINE", "agentId": "e9f05e30-cab5-4585-bc25-aab2729fe082", "botSettings": {"_effect": 0, "agentId": "e9f05e30-cab5-4585-bc25-aab2729fe082", "id": "4a1feef1-2902-4e4a-a7e0-70d1c6e661cd", "botId": "7a1c671d-bcc4-460b-b1ea-d5ce6c06d87d", "config": "{\"silent\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"kgEnable\":false,\"qaRecommendList\":{\"source\":1,\"list\":[]},\"hangup\":{\"action\":\"\",\"isWebhook\":false,\"text\":\"抱歉,我不太理解您的意思\",\"type\":1},\"relatedQuestion\":{\"autoEnable\":false,\"manualEnable\":false,\"autoCount\":5},\"clarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选问题}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选问题}还是{候选问题}呢?\",\"templateMany\":\"请问您想咨询的是{候选问题}还是{候选问题}还是{候选问题},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":0,\"reply\":\"\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"webhookGlobalValue\":\"暂时无法获取到返回结果\",\"unMatch\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"unMatched\":\"抱歉,我不太理解您的意思\",\"defaultReply\":{\"text\":\"抱歉,我不太理解您的意思\",\"type\":1}},\"entityClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选实体}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选实体}还是{候选实体}呢?\",\"templateMany\":\"请问您想咨询的是{候选实体}还是{候选实体}还是{候选实体},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"repeat\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"interrupt\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"faqConcatEnable\":true,\"gossipEnable\":false,\"welcome\":\"您好，很高兴为您服务！\"}", "sensitiveStrategy": "{\"list\":[{\"name\":\"未命名策略bcf\",\"show\":true,\"repositories\":[],\"strategy\":{\"unMatched\":null,\"defaultReply\":null,\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]},\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]}}}]}", "personalChat": "{\"enable\":false,\"commonInfo\":{\"name\":\"\",\"sex\":0,\"birthday\":\"2020-09-23\",\"workUnit\":\"\"}}", "entityRecommend": "{\"guidance\":\"为您推荐以下选项\",\"enable\":false,\"num\":5,\"model\":0}", "priority": "{\"type\":\"system\",\"config\":[{\"name\":\"模板澄清\",\"description\":\"同一模板同时命中多个意图或faq，则澄清这些意图和faq\",\"condition\":\"templateClarify.isEndProcess()\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"templateClarify\",\"sourceName\":\"模板澄清\",\"sort\":1,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"任务式会话命中模板\",\"description\":\"同一模板只命中意图，且多轮有答案\",\"condition\":\"taskbased.isSolved() && taskbased.getModel() == \\\"template\\\" \",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":10,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"问答库命中模板\",\"description\":\"同一模板只命中faq，且faq有答案\",\"condition\":\"faq.isSolved() && faq.getModel() == \\\"template\\\"\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":20,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"高置信度任务式会话\",\"description\":\"多轮有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"taskbased.isSolved() && taskbased.getModel() == \\\"knn\\\" && taskbased.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":30,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"高置信度知识图谱\",\"description\":\"kg有答案、置信度大于阈值，属于高置信度\",\"condition\":\"kg.isSolved() && kg.getModel() == \\\"knn\\\" && kg.getConfidence() > knnConfidenceThreshold && !kg.isClarify()\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"kg\",\"sourceName\":\"知识图谱\",\"sort\":40,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"高置信度问答库\",\"description\":\"faq有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"faq.isSolved() && faq.getModel() == \\\"knn\\\" && faq.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":50,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"高置信度闲聊\",\"description\":\"chitchat有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"chitchat.isSolved() && chitchat.getModel() == \\\"knn\\\" && chitchat.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"chitchat\",\"sourceName\":\"闲聊\",\"sort\":60,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"任务式会话有答案\",\"description\":\"多轮有答案，不属于子节点anythingElse、顶层节点anythingElse、会话开始条件\",\"condition\":\"taskbased.isSolved() && !taskbased.isAnythingElse() && !taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":70,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"知识图谱有答案\",\"description\":\"kg有答案，不是澄清类型\",\"condition\":\"kg.isSolved() && !kg.isClarify()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"kg\",\"sourceName\":\"知识图谱\",\"sort\":80,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"问答库高置信度澄清\",\"description\":\"faq高置信度澄清，同时命中多个faq标准问且相似度差小于0.05\",\"condition\":\"faqHighConfidenceClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"faqHighConfidenceClarify\",\"sourceName\":\"faq高置信度答案\",\"sort\":90,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"问答库有答案\",\"description\":\"faq有答案\",\"condition\":\"faq.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":100,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"重述策略\",\"description\":\"命中系统重述策略\",\"condition\":\"repeat.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"repeat\",\"sourceName\":\"系统重述策略\",\"sort\":120,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"实体澄清\",\"description\":\"实体澄清\",\"condition\":\"entityClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityClarify\",\"sourceName\":\"实体澄清\",\"sort\":130,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"实体自动填充\",\"description\":\"复合实体自动填充\",\"condition\":\"entityAutoFill.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityAutoFill\",\"sourceName\":\"实体填充\",\"sort\":140,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"任务式会话子节点AnythingElse\",\"description\":\"多轮有答案、命中子节点anythingElse条件\",\"condition\":\"taskbased.isSolved() && taskbased.isAnythingElse() && !taskbased.isTopAnythingElse() && !taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":150,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"意图、问答澄清\",\"description\":\"意图、faq澄清\",\"condition\":\"intentFaqClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"intentFaqClarify\",\"sourceName\":\"意图、faq澄清\",\"sort\":160,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"知识图谱澄清\",\"description\":\"kg澄清\",\"condition\":\"kg.isSolved() && kg.isClarify()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"kg\",\"sourceName\":\"知识图谱\",\"sort\":170,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"敏感词\",\"description\":\"敏感词\",\"condition\":\"sensitive.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"sensitive\",\"sourceName\":\"系统敏感词策略\",\"sort\":180,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"闲聊有答案\",\"description\":\"闲聊有答案\",\"condition\":\"chitchat.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"chitchat\",\"sourceName\":\"闲聊\",\"sort\":190,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"任务式会话命中会话开始\",\"description\":\"多轮、命中会话开始条件\",\"condition\":\"taskbased.isSolved() && taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":200,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"静默策略\",\"description\":\"命中系统静默策略\",\"condition\":\"silent.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"silent\",\"sourceName\":\"系统静默策略\",\"sort\":210,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"打断策略\",\"description\":\"命中系统打断策略\",\"condition\":\"interrupt.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"interrupt\",\"sourceName\":\"系统打断策略\",\"sort\":220,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"实体换一换\",\"description\":\"实体推荐、换一换\",\"condition\":\"entityAskReplaceAns.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityAskReplaceAns\",\"sourceName\":\"实体推荐、换一换\",\"sort\":225,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"多次未匹配\",\"description\":\"多次未匹配回复\",\"condition\":\"multiNoMatch.isEndProcess()\",\"category\":\"noMatch\",\"categoryName\":\"无答案\",\"source\":\"multiNoMatch\",\"sourceName\":\"系统多次未匹配策略\",\"sort\":240,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0},{\"name\":\"任务式会话命中顶层AnythingElse\",\"description\":\"多轮有答案、命中顶层anythingElse条件\",\"condition\":\"taskbased.isTopAnythingElse()\",\"category\":\"noMatch\",\"categoryName\":\"无答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":250,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0}],\"auditStatus\":null,\"version\":1,\"agentType\":0,\"agentId\":null,\"botId\":null,\"importConf\":false,\"systemConf\":true}", "created": 1600848708000, "updated": 1600848708000, "configAll": "{\"entityRecommend\":{\"guidance\":\"为您推荐以下选项\",\"enable\":false,\"num\":5,\"model\":0},\"silent\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"kgEnable\":false,\"qaRecommendList\":{\"source\":1,\"list\":[]},\"sensitive\":{\"list\":[{\"name\":\"未命名策略bcf\",\"show\":true,\"repositories\":[],\"strategy\":{\"unMatched\":null,\"defaultReply\":null,\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]},\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]}}}]},\"priority\":\"{\\\"type\\\":\\\"system\\\",\\\"config\\\":[{\\\"name\\\":\\\"模板澄清\\\",\\\"description\\\":\\\"同一模板同时命中多个意图或faq，则澄清这些意图和faq\\\",\\\"condition\\\":\\\"templateClarify.isEndProcess()\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"templateClarify\\\",\\\"sourceName\\\":\\\"模板澄清\\\",\\\"sort\\\":1,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"任务式会话命中模板\\\",\\\"description\\\":\\\"同一模板只命中意图，且多轮有答案\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.getModel() == \\\\\\\"template\\\\\\\" \\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":10,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"问答库命中模板\\\",\\\"description\\\":\\\"同一模板只命中faq，且faq有答案\\\",\\\"condition\\\":\\\"faq.isSolved() && faq.getModel() == \\\\\\\"template\\\\\\\"\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":20,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"高置信度任务式会话\\\",\\\"description\\\":\\\"多轮有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.getModel() == \\\\\\\"knn\\\\\\\" && taskbased.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":30,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"高置信度知识图谱\\\",\\\"description\\\":\\\"kg有答案、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"kg.isSolved() && kg.getModel() == \\\\\\\"knn\\\\\\\" && kg.getConfidence() > knnConfidenceThreshold && !kg.isClarify()\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"kg\\\",\\\"sourceName\\\":\\\"知识图谱\\\",\\\"sort\\\":40,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"高置信度问答库\\\",\\\"description\\\":\\\"faq有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"faq.isSolved() && faq.getModel() == \\\\\\\"knn\\\\\\\" && faq.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":50,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"高置信度闲聊\\\",\\\"description\\\":\\\"chitchat有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"chitchat.isSolved() && chitchat.getModel() == \\\\\\\"knn\\\\\\\" && chitchat.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"chitchat\\\",\\\"sourceName\\\":\\\"闲聊\\\",\\\"sort\\\":60,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"任务式会话有答案\\\",\\\"description\\\":\\\"多轮有答案，不属于子节点anythingElse、顶层节点anythingElse、会话开始条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && !taskbased.isAnythingElse() && !taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":70,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"知识图谱有答案\\\",\\\"description\\\":\\\"kg有答案，不是澄清类型\\\",\\\"condition\\\":\\\"kg.isSolved() && !kg.isClarify()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"kg\\\",\\\"sourceName\\\":\\\"知识图谱\\\",\\\"sort\\\":80,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"问答库高置信度澄清\\\",\\\"description\\\":\\\"faq高置信度澄清，同时命中多个faq标准问且相似度差小于0.05\\\",\\\"condition\\\":\\\"faqHighConfidenceClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"faqHighConfidenceClarify\\\",\\\"sourceName\\\":\\\"faq高置信度答案\\\",\\\"sort\\\":90,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"问答库有答案\\\",\\\"description\\\":\\\"faq有答案\\\",\\\"condition\\\":\\\"faq.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":100,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"重述策略\\\",\\\"description\\\":\\\"命中系统重述策略\\\",\\\"condition\\\":\\\"repeat.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"repeat\\\",\\\"sourceName\\\":\\\"系统重述策略\\\",\\\"sort\\\":120,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"实体澄清\\\",\\\"description\\\":\\\"实体澄清\\\",\\\"condition\\\":\\\"entityClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityClarify\\\",\\\"sourceName\\\":\\\"实体澄清\\\",\\\"sort\\\":130,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"实体自动填充\\\",\\\"description\\\":\\\"复合实体自动填充\\\",\\\"condition\\\":\\\"entityAutoFill.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityAutoFill\\\",\\\"sourceName\\\":\\\"实体填充\\\",\\\"sort\\\":140,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"任务式会话子节点AnythingElse\\\",\\\"description\\\":\\\"多轮有答案、命中子节点anythingElse条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.isAnythingElse() && !taskbased.isTopAnythingElse() && !taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":150,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"意图、问答澄清\\\",\\\"description\\\":\\\"意图、faq澄清\\\",\\\"condition\\\":\\\"intentFaqClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"intentFaqClarify\\\",\\\"sourceName\\\":\\\"意图、faq澄清\\\",\\\"sort\\\":160,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"知识图谱澄清\\\",\\\"description\\\":\\\"kg澄清\\\",\\\"condition\\\":\\\"kg.isSolved() && kg.isClarify()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"kg\\\",\\\"sourceName\\\":\\\"知识图谱\\\",\\\"sort\\\":170,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"敏感词\\\",\\\"description\\\":\\\"敏感词\\\",\\\"condition\\\":\\\"sensitive.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"sensitive\\\",\\\"sourceName\\\":\\\"系统敏感词策略\\\",\\\"sort\\\":180,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"闲聊有答案\\\",\\\"description\\\":\\\"闲聊有答案\\\",\\\"condition\\\":\\\"chitchat.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"chitchat\\\",\\\"sourceName\\\":\\\"闲聊\\\",\\\"sort\\\":190,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"任务式会话命中会话开始\\\",\\\"description\\\":\\\"多轮、命中会话开始条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":200,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"静默策略\\\",\\\"description\\\":\\\"命中系统静默策略\\\",\\\"condition\\\":\\\"silent.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"silent\\\",\\\"sourceName\\\":\\\"系统静默策略\\\",\\\"sort\\\":210,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"打断策略\\\",\\\"description\\\":\\\"命中系统打断策略\\\",\\\"condition\\\":\\\"interrupt.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"interrupt\\\",\\\"sourceName\\\":\\\"系统打断策略\\\",\\\"sort\\\":220,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"实体换一换\\\",\\\"description\\\":\\\"实体推荐、换一换\\\",\\\"condition\\\":\\\"entityAskReplaceAns.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityAskReplaceAns\\\",\\\"sourceName\\\":\\\"实体推荐、换一换\\\",\\\"sort\\\":225,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"多次未匹配\\\",\\\"description\\\":\\\"多次未匹配回复\\\",\\\"condition\\\":\\\"multiNoMatch.isEndProcess()\\\",\\\"category\\\":\\\"noMatch\\\",\\\"categoryName\\\":\\\"无答案\\\",\\\"source\\\":\\\"multiNoMatch\\\",\\\"sourceName\\\":\\\"系统多次未匹配策略\\\",\\\"sort\\\":240,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0},{\\\"name\\\":\\\"任务式会话命中顶层AnythingElse\\\",\\\"description\\\":\\\"多轮有答案、命中顶层anythingElse条件\\\",\\\"condition\\\":\\\"taskbased.isTopAnythingElse()\\\",\\\"category\\\":\\\"noMatch\\\",\\\"categoryName\\\":\\\"无答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":250,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0}],\\\"auditStatus\\\":null,\\\"version\\\":1,\\\"agentType\\\":0,\\\"agentId\\\":null,\\\"botId\\\":null,\\\"importConf\\\":false,\\\"systemConf\\\":true}\",\"hangup\":{\"action\":\"\",\"isWebhook\":false,\"text\":\"抱歉,我不太理解您的意思\",\"type\":1},\"relatedQuestion\":{\"autoEnable\":false,\"manualEnable\":false,\"autoCount\":5},\"clarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选问题}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选问题}还是{候选问题}呢?\",\"templateMany\":\"请问您想咨询的是{候选问题}还是{候选问题}还是{候选问题},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":0,\"reply\":\"\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"webhookGlobalValue\":\"暂时无法获取到返回结果\",\"unMatch\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"unMatched\":\"抱歉,我不太理解您的意思\",\"defaultReply\":{\"text\":\"抱歉,我不太理解您的意思\",\"type\":1}},\"personalChat\":{\"enable\":false,\"commonInfo\":{\"name\":\"\",\"sex\":0,\"birthday\":\"2020-09-23\",\"workUnit\":\"\"}},\"entityClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选实体}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选实体}还是{候选实体}呢?\",\"templateMany\":\"请问您想咨询的是{候选实体}还是{候选实体}还是{候选实体},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"repeat\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"interrupt\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"faqConcatEnable\":true,\"gossipEnable\":false,\"welcome\":\"您好，很高兴为您服务！\"}"}, "botProcess": [], "botFaq": [{"_effect": 0, "agentId": "e9f05e30-cab5-4585-bc25-aab2729fe082", "id": "5d0b05ce-166f-4c46-aa45-96bc4d77dfc0", "botId": "7a1c671d-bcc4-460b-b1ea-d5ce6c06d87d", "dirId": "0", "dirName": "默认"}]}]}, "faq": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "faq": [{"extend_questions": [], "question": "地址已经更改为新地址为何还在原地址派件", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "亲亲，若您已经更改正确的地址，我司会尽快帮您核实更新物流信息转出的，还请您耐心等待一下。"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我不满意，我要求全额赔偿", "网点说的赔偿金额我不满意", "几千块的东西你们只赔200块我不满意"], "question": "理赔不满", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "理赔不满"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么找到专属业务员", "有客服评价么。", "怎么找到我的专属快递员", "你猜猜我会给你几分啊"], "question": "如何给客服评价", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何给客服评价"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["派送范围", "派件范围是", "地址不能送到村里", "我想问乡镇可以到吗？", "地址能不能到？", "派件范围", "快递派送范围", "快递可以寄外地", "地区不派送"], "question": "查询快递派送范围", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查询快递派送范围"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["不是实名制吗？", "别的快递都不用，你们这寄个快递要实名制？"], "question": "寄件需要实名制吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "寄件需要实名制吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": [], "question": "我没有单号怎么办", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "我没有单号怎么办"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["查询提货码", "丰巢没发取件验证码", "但是都那么久了 还没有提货码", "什么时候发取件码", "没收到取件码怎么办", "没有收到取件码", "没发取件码", "东西已放入快递柜 但是未收到短信", "就是快递已经到了，但是没收到取件码", "查不到取件码", "取件码错误", "我没收到取货码", "取货号没收到", "没收到快递柜的编号", "收不到验证码", "没发给我取件码"], "question": "如何获取快递柜取件码", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何获取快递柜取件码"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可是我昨天我已经拿到了啊，为何还不显示签收"], "question": "已签收物流记录未更新", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "已签收物流记录未更新"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["冷链服务有吗", "冷链服务"], "question": "优鲜送业务", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "优鲜送业务"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["取件员电话是多少？", "有寄件员电话吗？", "你们上门取件的电话多少？"], "question": "上门取件电话是多少", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "上门取件电话是多少"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["过年还能寄快递", "不包装", "今天快递时候停运", "没有包装", "是包装好还是来包装", "带个带子包装", "是想问纸箱钱", "今年物流停运", "原包装扔能提供包装", "需要包装好还是包装", "包还是快递员包", "就想问问气泡袋是免费提供还是需要顾客购买", "今年快递时候停运", "上门取件能带上纸箱", "中通寄递是否提供包装", "快递是时候放假", "纸箱包装费用", "快递时候停止", "想问下电脑没包装可以给包装", "是包装还是人包装", "有冰袋包装", "提供包装要不要钱", "能提供包装", "包裹时候停运", "快递时候停运", "过年时候停快递", "寄皮箱的话是装好箱还是公司有纸箱", "提供包装要钱", "想问快递到春节前停", "过年休息能不能正常收件发出", "春节快递放假", "今年春节快递时停运", "快递提供包装", "快递是否提供快递包", "过年休息时间", "要是寄收纳盒有箱子可以帮包装", "帮忙包装", "过年放假吗", "还要拿点袋子给", "春节快递放假吗", "如果需要包装有包装费", "春运期间快递时候停", "没包装盒负责包装", "时候停快递", "春节停运", "就是寄快递需要包装", "过年放假", "快递时候停", "快递时候放假", "能带个纸箱子", "春节放假", "快递包装", "大概时候停运", "春节快递停运", "快递停运时间", "快递春节继续运营", "提供包装"], "question": "春节快递停运吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "春节快递停运吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["亲你们客服态度特别差", "投诉客服服务", "客服都不作为", "我投诉你这个客服", "你们客服态度这么恶劣吗", "你们这个人工客服也太垃圾了吧", "怎么可以投诉客服", "中通客服态度差", "怎么投诉人工客服？"], "question": "客服服务态度差", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "客服服务态度差"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["确认收货对快递有影响吗", "没收到货误签收怎么办？", "快件没有签收但是已经在淘宝上点确认收货了有影响吗？"], "question": "淘宝确认收货对快递有影响吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "淘宝确认收货对快递有影响吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["寄快递只有订单号没有运单号", "条形码下面的数字是运单号", "但是没有运单号", "快递为什么不发快递单号给", "知道运单号", "寄件单子丢查单号", "根本没给纸质单子", "显示已揽件为什么没出快递单号", "快递小哥已经回来收件可是一直没收到快递单号信息", "是运单号", "货取走不给打快单号过来", "帮查下单号", "没有给单子", "快递还没发货没运单号", "寄快递单号", "预约寄件之后就有单号", "快递单号是", "不知道运单号", "最上面的数字是单号", "想物流单号是", "时候有运单号", "没有快递单号", "昨天寄出快递运单号", "没有给快递单号", "订单号查物流单号", "没有运单号", "快递还没寄出去e寄出去订单号能告诉", "要查快递单号", "方便帮查单号", "在买苹果没给单号", "已寄件单号查快递", "忘记寄件单号查到", "运单号是快递编号", "快递单号没发过来", "单号给下谢谢忘拿", "刚刚寄单  快递员把东西拿走  但是单号没给", "刚才寄出去洗发水有单号", "查运单号", "寄查快递单号", "请尽快告知运单号", "查取订单号", "订单号", "要求查询卢政林发出快递单号", "查单号", "快递编号就是运单号", "也没给单子", "为什么没有快递单", "快递员没给回单", "单尽快处理把运单号发过来", "退货单号在查询", "快递单号打印", "快递单号帮查", "刚刚寄快递能不能把单号给发过来", "查找单号", "中通快递单号查询", "快递取走能收到快递单号", "请把快递号发过来", "刚刚下单没有 单号", "差快递单号", "可以告诉是运单号", "是寄件知道运单号在", "运单号查不出来", "查询寄件单号", "昨天发出单号", "物流单号", "寄件单号再给发下", "快递忘单号", "昨天中午寄快递到现在没有订单号", "能查到快递单号", "订单号就是物流号", "昨天预约快递邮寄快递拿走没出单号", "寄出去快递没有物流号才能找到物流号", "快递单号", "运单号时候显示", "订单号查运单号", "拿走快递也给过钱一直不给发物流号", "查寄出快递单号", "迅速查询快递单号", "快递还没发单号", "不知道单号", "昨天寄件单号发下", "帮查快递单号", "寄快递单号是", "麻烦帮查昨天快递订单号", "今天寄快递查单号", "前段时间寄件快递单号在看", "发个快递单号都不给", "在中通网上买红枣查不到单号", "现在中通运单号是数字", "能不能直接给中通快递单号", "寄件后没有单号", "广州寄上海钱", "快递单号是行数字", "怎么查询寄件单号", "可以帮查单号", "上面不是运单号", "快递寄出单子没给", "时候能知道单号", "想查今天寄快递单号", "快递单号有位数", "今天要寄快递编号码是", "快递单号请发过来", "今天寄快递没有拿到单号想询问单号", "寄过快递单号查", "订单号能查询", "寄出快递单号在查", "查订单号", "想查运单号", "订单号是不是物流单号", "想问快递拿去单号想没看到", "可以帮查询下今天寄出快递单号", "麻烦帮查运单号", "物流单号是", "时候能看到运单号", "寄出快递快递单号在找", "查询订单号", "收快递查没有货单号", "单号没发给", "单号时候出来", "已有快递员上门取件 可是还没给快递单号", "运单号在位置", "快递单号发给", "运单号", "快递取走没有单号", "昨天寄快递单号发给", "问昨天寄出去快递还没给发单号", "快递单号时候有", "不是网上预约寄件 是打客服电话 现在查找单号", "找单号", "寄个件没给单子", "单号", "寄快递单号时候能知道", "快递号忘", "昨天寄单只有单号", "寄出快递却不知道运单号", "前发货要编号帮发过来好", "要运单号", "没有单号快递员没给单号", "运单号格式", "快递没有单号", "能查到寄出去快递单号", "中午有快递小哥来收件还没给单号", "查快递单号真假", "寄快递单号查", "没有订单号", "帮查运单号谢谢", "快递单号是位数", "快递单号在看", "才能知道快递单号", "记得发快递单号给[facepalm]", "寄出快递查快递单号", "提取运单号", "中通快递单号是位数", "有单号", "单号是中通快递", "想查询寄件单号", "快递单号是不是由数字跟字母组成", "快递单号弄错然后寄回去快递收不到物流消息", "快递单号查", "能帮差个单号", "把刚才单号发来", "快递上门取件没给运单号", "快递单号时候可以更新", "寄东西但是没有给订单号", "查询物流单号", "单号位数", "寄快递运单号是不是快递员上门取件后才能查", "查订单号能查到", "没有快递回执单给", "申通快递单号查询", "昨天寄但没给单子想查下单号", "能帮查询昨天寄出快递物流单号", "订单号有误", "查快递单号", "买红薯在看快递单号", "查询快递单号", "单号掉", "查下快递号", "需要查具体单号", "想取件不知道快递单号", "运单号就是快递号", "收件过后没有给单号", "为什么快递取件后没运单号", "现在还没给单号", "怎么查询我的快件单号", "快递单号发过来没有", "可以说昨天发货货单号", "快递拿走还没有单号", "看快递号", "寄完之后要单号", "运单号丢", "快递单号就是订单号码", "订单号位数", "查询在网上下单单号", "能查单号", "为什么查不到寄出去快递单号", "快递员没有跟单子", "想查单号哦", "运单号是"], "question": "怎么查询快递单号", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么查询快递单号"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我要联系快运？", "中通有快运吗？"], "question": "中通快运", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通快运"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["上海到山东要几天", "寄到北京要几天"], "question": "时效查询", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "时效查询"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["已经付钱了，我的订单在哪查看？", "积分兑换的商品如何看发货情况"], "question": "积分兑换的商品如何查看发货情况", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "积分兑换的商品如何查看发货情况"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我要退货积分商城购买的物品", "我已经付款的商品什么时候发货？", "在积分商城购买的物品什么时候发货"], "question": "积分商城购买的物品发/退货", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "积分商城购买的物品发/退货"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["五星好评", "评价", "在哪里可以评价", "查看評價打分"], "question": "怎么评价", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么评价"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["兑换的免费商品什么时候发货"], "question": "0元兑换的商品什么时候发货", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "0元兑换的商品什么时候发货"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["通过哪些渠道联系中通客服", "只能在这里咨询在线客服吗？", "都有哪些客服联系方式", "客服咨询渠道有哪些？", "你们在哪些地方有客服可以咨询"], "question": "客服咨询渠道有哪些", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "客服咨询渠道有哪些"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["遇到台风应改没关系吧", "昨天暴雨 今天也该送到了吧"], "question": "什么情况会影响快递员送快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "什么情况会影响快递员送快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我的快件坏了丢失了吗"], "question": "一般快件是在什么情况下视为丢失呢", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "一般快件是在什么情况下视为丢失呢"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["查询快递付费方式", "查询快递是到付还是现付", "帮查下单是不是到付", "这单是到付的吗？", "是不是到付件", "订单是不是到付", "是不是货到付款", "怎么查询快递是到付还是现付", "要咨询快件是到付", "看看是不是货到付款", "能不能查件是不是到付还是寄付"], "question": "查询快递的付费方式", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查询快递的付费方式"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我捡到一个快递", "我捡到一个快递件", "我是捡到你家快递了"], "question": "捡到的快件怎么处理", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "捡到的快件怎么处理"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可以设置头像吗", "微信里面能换头像吗"], "question": "微信是否可以设置头像", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "微信是否可以设置头像"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递管家下载不了"], "question": "快递管家软件如何安装", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递管家软件如何安装"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": [], "question": "请问可以使用哪些语言咨询", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "亲亲，这边是处理国内快件问题的，建议您使用中文来咨询快件问题。"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["咨询是否可以货到付款", "代收货款", "能代收货款吗？", "支持代收货款", "货到付款需要手续费", "代收货款价格是收取", "能代收货款", "负责代收货款", "寄件可以货到付款", "待收货款收费", "能不能发代收货款件", "能不能代收货款", "货到付款可以", "中通可以代收货款", "可以货到付款", "代收货款意思", "货到付款手续费是", "想代收货款要做", "可以代收货款"], "question": "可以代收货款吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "可以代收货款吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["退积分"], "question": "我要退积分", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "我要退积分"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递员多收了我两块钱，说是包装费？"], "question": "投诉乱收费", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "投诉乱收费"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["下单失败", "东西给出去之后为什么订单号被取消", "为什么订单总是被取消", "是发件人下单寄件然后寄件地址写错可以更改", "取消快递取消", "为何下单失败？", "可以在线取消预约", "为什么下单被取消", "预约揽件现在想取消", "下单成功取消", "已经给取消订单", "在支付宝下单上门取件取消", "订单被取消", "取消发快递", "下单寄件现在看看取消", "没有下单成功", "预约寄快递没到取消订单", "在支付宝预约取件 取消", "下错单取消", "订单取消", "可以在手机上取消", "是显示没有下单成功", "取消预约下单", "下单还没寄快递取消", "订单被取消又运单号", "取消寄件预约", "客户要求取消订单", "为什么快递订单被取消", "预约寄件可以取消", "不想寄取消", "网上下单地址错取消订单", "订单写错取消", "现在要寄件都无法下单", "单号没人取件想取消预约寄件", "预约下单不了", "预约寄件为什么被取消", "取消订单", "快递为取消下单", "取消预约订单", "没有取消订单为什么订单状态显示取消", "没人取件想取消预约寄件", "在线订单想取消", "取消预约订单/::)", "取消预约快递", "取消快递", "咨询下单失败原因", "怎么回事下单失败？", "刚才预约寄快件点错取消", "为什么取消已经下单寄件", "单子在取消", "取消寄件订单", "想取消预约取件", "怎么老下单失败？", "取消上门取件订单", "要取消寄件订单", "取消快件在", "微信下单想取消", "下单下错取消", "发订单给取消", "不能下单", "预约收件取消", "客服帮取消下预约", "订单已取消是取消", "订单取消是为什么", "不寄取消", "取消预约取件", "为什么要取消订单", "取消下单", "取消预约快递员上门取件", "取消预约", "为什么预约寄件都被取消", "帮取消订单", "想取消寄件", "取消预约寄件", "还没取件现在不想寄所以想取消", "为什么下订单取消", "取消寄件", "为什么寄件被取消而没有通知", "为什么订单被取消", "预约快件没取去退回", "请帮取消今天寄件订单", "能把取消订单", "打算取消订单"], "question": "为什么下单失败", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "为什么下单失败"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我想加盟网点", "怎么合作寄件", "怎么与网点加盟合作"], "question": "如何加盟", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何加盟"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["你们快递拒收是几天退回？", "那如果退回要多久?", "拒收快件后多久退回？"], "question": "拒收后多久可以退回", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "拒收后多久可以退回"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递送来没接到电话", "换手机号码了，没接到电话", "没接到送快递电话", "送货员给我电话我没接到", "电话欠费了没有接到快递电话", "昨天送件打来手机掉车上没接到电话"], "question": "错过快递派送怎么办", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "错过快递派送怎么办"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["会地址不详", "我的快递地址不详？", "收件地址不详", "就地址不详 联系不上 打电话", "快递不派件居然说地址不详细", "地址不详不会打电话问", "怎么显示地址不详", "我的货怎么是地址不详", "货是地址不详", "叫地址不详", "为什么说地址不详", "显示地址不详", "还说地址不详", "地址不详", "地址不详细", "得包裹说地址不详细", "快递地址不详"], "question": "收件地址不详细", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "收件地址不详细"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["你们派送几次", "你们一般派送几次"], "question": "快递免费派送几次", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递免费派送几次"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么追回拒收的快递？", "拒收退回的快件如何追回？"], "question": "如何追回已退的快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何追回已退的快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么撤销淘宝投诉？", "在淘宝投诉的物流投诉能在这儿撤销吗？", "能否撤销淘宝投诉？"], "question": "怎么撤销淘宝投诉", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么撤销淘宝投诉"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我已经实名认证了，怎么没有积分", "实名积分没有送", "怎么领取积分"], "question": "实名认证积分未到账", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "实名认证积分未到账"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我买的东西是代收货款的，但是钱付了之后发现是假货怎么办", "代收货款的件，签收后发现东西是假的怎么办"], "question": "代收货款后发现是假货怎么办", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "代收货款后发现是假货怎么办"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["麻烦给我上门取件", "我要预约寄件", "我要发快递"], "question": "我要寄件", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "我要寄件"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递二次收费", "为何我去那快递还要收费", "取快递收费", "取快递要收手续费", "取快递额外收费"], "question": "取快递收手续费吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "取快递收手续费吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可以在线付邮费吗", "在线上支付运费需要怎么操作", "怎么线上结账", "那我微信转给你吧，不好意思，怎么付款", "快递费怎么付", "运费怎么支付", "你好我在微信下的单可以在线支付吗", "能在线付款吗", "运费那里付", "下单了，怎么给快递费", "我要支付运费", "请问怎么付邮费", "我这件取走了运费在哪里付", "收货了邮费怎么付", "如何在线上进行运费支付", "中通快递费是怎么给", "那邮费可以在网上付吗", "需要支付多少运费呢", "在哪里支付", "下单寄件可以现金或者支付宝吗", "请问快递费是在线支付吗", "我还没付运费的呢", "快递费怎么收呢", "怎么付钱啊", "怎么给邮费", "网上能付快递费吗", "怎样把快递费发给你", "快递费怎么收取", "那给钱时现金还是微信", "网上寄件没有办法支付运费吗", "能在线支付运费吗", "运费可以在线付吗", "那在哪里付", "我到底该怎样付运费", "上门揽件 怎么付邮费", "快递取走了快递费在哪付", "我还没付运费", "我的预约寄件怎么没有显示运费", "可以手机支付吗", "快递费怎么给你", "我的帐单怎么付", "咨询如何在线支付运费？", "在微信下单寄件付钱怎么付", "线上支付运费的流程是什么", "咨询如何在线支付运", "预约寄件如何付运费", "我还没付快递费", "微信下单后怎么付运费", "在哪里付运费啊", "快递费支付方式"], "question": "如何支付快递费", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何支付快递费"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["这个快递能改预付吗", "更改支付方式"], "question": "更改付费方式", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "更改付费方式"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["什么是电子底单？", "电子面单是啥意"], "question": "什么是电子面单", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "什么是电子面单"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递员一般什么时候上班", "几点营业", "你们那些店都是几点开门的", "最迟几点收件", "你们中通快递几点上班", "查询乡镇代理点地址", "快递开门了吗", "网点在", "快递一般几点上班", "网点在位置", "一般快递员几点下班", "中通快递站点的营业时间", "网点是位置", "您好，早上几点开门", "快递几点开门", "你们几点钟开门", "查派送网点", "线下店营业时间", "上午快递点几点钟上班", "中通快递网点乡镇有", "自提地址在", "请问快递中心几点上班", "网点什么时候有人", "中转网点在", "快递几点上班？", "周围网点", "你们快递员几点下班啊", "快递网点在", "派件网点地址是", "啥时候营业", "网点查询", "我想问一下晚上你们几点关门呢", "你们中通快递都几点关门的", "网点地址在", "中通快递几点开门？"], "question": "网点营业时间", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "网点营业时间"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["打印发票", "发票开具", "可以申请发票", "可以开具发票", "开具发票", "发票获取", "电子发票开具", "不能开电子发票", "开店子发票", "有发票", "电子发票打印", "开发票怎么开？发票怎么申请？", "能申请开发票", "发票在申请", "电子发票获取", "可以开发票给", "增值税专用发票", "想开到付文件发票开", "发票索取", "网点说是电子发票", "能开发票发快递", "想问问开发票事", "开具电子发票", "开电子版发票", "发票在开", "开到付电子发票", "如果要开发票是发票", "申请", "有没有发票", "可以打发票", "发票在打印", "电子发票能自行打印", "有没有增值税发票", "发票是电子发票", "打印电子发票", "需要申请开发票", "能不能开发票", "有快递发票", "不能申请电子发票", "要开电子发票到去开", "不是电子发票", "是否可以开票", "可以开增值税法票", "在打印发票", "中通快递在开电子发票", "打电子发票", "增值税发票有", "发票", "能开电子发票", "可以开增值税发票", "可以开发票不", "寄件需要运费凭证", "打印中通电子发票", "增值锐发票在开", "请提供发票", "不能像顺丰在线申请电子发票", "有开发票", "发票是增值税发票", "发票是增值税法票", "到付没有开发票", "到付 没给发票", "要申请发票", "没有发票", "可以开电子发票", "咨询取电子发票", "开电子发票", "开发票", "申请电子发票", "需要开发票", "寄快递有发票吗？", "件已经寄需要发票", "电子发票拿到", "寄快递可以开发票", "发票时间可以开过来", "要开发票", "开发票票", "为什么没有发票", "寄时候说申请电子发票", "发票申请", "不是说可以申请电子发票", "直接就是电子发票", "当时说是电子发票", "网上申请发票", "电子发票申请", "有发票可以开", "取得发票", "要发票", "电子发票拿", "到付快递可以开电子发票", "发票有", "寄快递有发票", "能提供发票", "可以申请电子发票", "可以申请运费发票"], "question": "如何开发票", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何开发票"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["好像你们一个区域只有一个派送员吧？", "我想问一下，你们的快递员是只负责这一个区域还是多个"], "question": "一个区域有几个快递员", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "一个区域有几个快递员"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["发货慢怎么回事", "为什么中午才发货", "为什么你们还不发货"], "question": "为什么发货慢", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "为什么发货慢"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["请问到付运费怎么算的", "到付和先付快递费一样吗", "这里是您的问题", "运费到付价格一样吗", "寄付和到付", "请问到付是比寄付贵吗", "快递到付运费是不是比较贵", "是根据什么到付就要那么贵", "请问是现付便宜，还是到付便宜", "到付和自己付钱一样么", "到付加收服务费？", "到付和邮寄费用一样吗", "到付运费和先付价钱一样吗", "请问到付跟寄付费用是不是一样多", "到付要收手续费吗", "是不是到付跟寄付是统一价格吗", "到付和寄付区别", "现在中通到付运费和现付运费一样吗", "预付运费和到付一样的钱吗", "我想问快递到付运费是不是比较贵", "哦，现付给到付有运费有什么不同"], "question": "到付件怎么收费", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "到付件怎么收费"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["账号注销，怎么注销", "注销账号怎么注销", "账号注销怎么弄"], "question": "注销账号", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "注销账号"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我的积分怎么无法使用"], "question": "积分兑换失败", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "积分兑换失败"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["中转原的原因是什么啊", "从兰州到靖远中间还要中转吗？"], "question": "为什么需要中转", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "为什么需要中转"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["投诉蜂巢快递柜", "我要投诉快递柜怎么投诉"], "question": "智能快递柜投诉", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "智能快递柜投诉"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["人工客服的服务时间", "你们客服上班时间段是几点到几点", "在线客服服务时间", "人工在线服务时间", "人工客服时间", "你们工作时间是多久呢？", "中通在线客服服务时间", "现在客服下班了吗"], "question": "人工客服服务时间", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "人工客服服务时间"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["身份证号会不会泄露", "信息泄露"], "question": "信息安全问题", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "信息安全问题"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["你们的赔偿时间是多久呢", "网点答应赔偿怎么还没有赔偿？", "赔偿要多久才能都到账"], "question": "赔偿多久可以到账", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "赔偿多久可以到账"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么找不到积分"], "question": "积分在哪儿", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "积分在哪儿"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["什么是专属业务员？", "专属快递员是什么", "哪里有专属业务"], "question": "什么是专属业务员", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "什么是专属业务员"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["定时派送延迟", "淘宝选了定时派送未按时送达怎么投诉", "淘宝定时派送未按时送达怎么处理"], "question": "定时派送超过怎么办", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "定时派送超过怎么办"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可以转寄吗", "这个快递我要转运", "我想转寄快递", "可以帮我转其它快递吗", "这个订单可以转运吗"], "question": "是否可以转寄快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "是否可以转寄快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我昨天积分兑换的优惠卷怎么不好用了"], "question": "积分兑换的优惠卷无法使用了", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "积分兑换的优惠卷无法使用了"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我没买东西", "我最近没买东西，但是刚刚有一个包裹", "好几个不是我的快递，可信息都发过来了"], "question": "没购物怎么会有快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "没购物怎么会有快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["为什么派件员还不来取件", "快递员迟迟不来取件怎么办？", "怎么还不来取货呀"], "question": "业务员长时间未取件如何处理", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "业务员长时间未取件如何处理"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递号我在哪里获取呢？", "小通，小偷通，运单号在哪里可以找到", "我木有运单号，怎么办，小通", "kuaididanhaodiul", "我的运单号，快递员没给我呀", "我没有 运单号", "运单号丢了", "我没有快递单号", "我的单号丢失了，怎么办？", "运单号在哪里找", "木有运单号", "包裹里面的东西不对", "我的快递运单号不知道放在哪里了？", "如何找回运单号呢"], "question": "已经拦截的快递为什么还显示在派送", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "亲亲，若发件人已经联系发件网点拦截快件，我司是会将快件退回，请您放心。"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["在线客服没人吗？", "没有在线客服？", "在线客服怎么老是接入不了？"], "question": "在线客服一直没人回应", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "在线客服一直没人回应"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["上门取件时需要查看快递吗？", "你们上门收件要打开看嘛？", "上门取件会打开包裹吗？"], "question": "上门取件会验货吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "上门取件会验货吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["实名制可以改吗", "实名制可以更改吗"], "question": "实名制可以取消或更改吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "实名制可以取消或更改吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么绑定快递员", "绑定专属业务员怎么绑定", "专属快递员绑定"], "question": "怎么绑定专属业务员", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么绑定专属业务员"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["你们不是有送货上门服务吗", "这件快递自提件是怎么回事，不能送到吗", "对了 现在都不能送货上门了哈", "能送货上门吗", "我需要送货上门", "能不能送货上门", "快递是不是必须送上门？", "代收送货上门不", "能不能直接给我送到我的地方，不用给我打电话", "送货上门吗？", "希望能安排送货上门", "你们中通不送货上门吗", "快递是不是必须送上门", "派送到客户位置的还是需要自取的", "送货上门吗", "请问你们的快递，是送货上门吗"], "question": "快递送货上门吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递送货上门吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可以货到收货方付运费", "到付运费", "意思运费可以到付", "货到对方付运费", "网上预约到付可以", "微信下单可以到付", "能货到付款", "寄快递能不能到付", "支持到付吗？", "可以发到付件", "发快递可以选择到付", "中通快递支持到付方式", "能不能到付", "能寄到付件", "支持到付", "中通快递支持运费到付业务", "可以到付", "就是运费到付可以", "运费到付", "可以到付不", "发件可以到付", "只是运费到付", "能到付吗？", "退货邮可以到付", "现在中通快递不能到付", "可以选择到付", "中通有到付业务", "台湾可以到付款", "运费可以到付", "中通快递运费可不可以到付", "不支持到付", "网上下单可以到付", "可以邮费到付", "中通快递可以到付", "支付到付", "中通可以到付", "下单时候没有让选择付款方式要到付", "快递费可以到付", "代收点可以到付", "寄到付快递", "可以寄到付", "不能到付", "咨询是否支持到付", "邮费到付", "递费到付", "问到付运费是算", "能否到付", "能不能到付邮费", "运费是否支持到付", "支持到付快递费", "预约寄件选到付", "在线寄件支持到付", "快递可以到付吗？", "快递费用可以到付", "上门取件可以到付", "地址可以到付", "快递可以到付", "邮费支持到付", "快递费到付", "邮费可以到付", "寄到龙华件不支持到付"], "question": "是否支持到付", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "是否支持到付"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["你们有限时快递吗", "中通有限时快递吗"], "question": "限时快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "限时快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["在中通优选购物", "在中通优选上买东西没有发货记录", "在优选里下单查订单信息", "在中通优选商城买", "中通优选公众号关注", "在中通优选里面有弹出来一块钱购买小米音响,现在查不到订单", "中通优选和不一家公司", "中通优选是什么", "中通优选官网", "什么是中通优选", "中通优选是"], "question": "中通优选", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通优选"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["物流信息和官网信息怎么不同步？", "物流状态怎么和网站的不一样啊", "物流信息为什么跟快递公司官网的不一致?"], "question": "物流信息和官网信息怎么不同步", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "物流信息和官网信息怎么不同步"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["发不是空运", "是陆运还是空运", "快件不是发空运", "有规定揽收重量贩围", "中通有空运", "有明确规定揽收重量范围", "寄件只要不超过公斤都是用单号寄", "想知道是发空运还是陆运", "包是空运还是陆运", "快递是发空运", "查询派送范围", "快递最高支持多重", "有规定揽收重量范围", "中通揽收标准", "货是空运还是汽车", "最能邮寄多重物品", "快递最可以重量", "最能寄多重东西", "有规定揽收重量范围吗？", "快递最可以多重", "最多能寄多重的东西？", "可以走空运", "为什么不送货  有快递点", "是空运还是汽运"], "question": "查询揽收标准", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查询揽收标准"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么查询积分使用明细"], "question": "如何查询积分明细", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何查询积分明细"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["能不能给我换一个快递员", "请正常派件，换一个快递员配送，谢谢！"], "question": "要求更换快递员送件", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "要求更换快递员送件"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我的货物少件", "包裹少件怎么办？", "我的快递找不对", "你好，我快递不对呀", "你们是不是少发东西给我了？？？", "我有3个快递少了1个", "收到的货少了怎么办", "你好，我的快递是不是你们少发了？东西怎么少了"], "question": "包裹少件或内件不符", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "包裹少件或内件不符"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["查一下运费"], "question": "运费查询", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "运费查询"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["中通国际部电话多少？", "中通有国际部门吗？"], "question": "中通国际业务", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通国际业务"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["为什么有一个代收货款的快递"], "question": "为何会收到代收货款的快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "为何会收到代收货款的快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么拒签退回了还显示签收了呢"], "question": "要求退回的件被签收了怎么办", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "要求退回的件被签收了怎么办"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["运费险失效是什么原因", "运费险显示失效", "当时快递，我就没签收，运费险不成功吗？", "运费险怎么处理", "怎么用运费险", "运费险"], "question": "运费险相关问题", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "运费险相关问题"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["要同时发快递", "同时寄件", "怎么批量寄件", "寄送件", "手机上批量寄快递", "批量寄件", "我就是说我要寄很多东西怎么寄", "想发快递"], "question": "如何批量寄件", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何批量寄件"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["拒签要付运费吗？", "拒签的话退快递费吗", "拒收返回有快递费么"], "question": "拒签要付运费吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "拒签要付运费吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["菜刀能寄吗", "化妆品能寄吗", "防晒喷雾可以寄吗"], "question": "违禁品查询", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "违禁品查询"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我要求寄的是中通的，为何中途，中通给我转邮政呢", "中途转单怎么回事"], "question": "为何中途转单", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "为何中途转单"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我需要截件。", "今天寄的快递可以给我退回来吗？"], "question": "快件发出了想退回怎么办", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快件发出了想退回怎么办"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["为什么周末都在送货", "节假日能不能送", "还没听说有周末不送哦", "中通周末不送货", "节假日不送", "周末不给配送", "节假日正常送货吗？", "好像周末都不送", "节假日放假现在放节假日", "又没有放假干还有节假日", "礼拜六送快递", "公司没上班", "为什么大企业周末不送件不提前说", "周六周日休息不送件", "不能总回礼拜六礼拜天都不送", "咨询节假日是否派件", "周末就不送快递", "节假日有规定", "节假日中通送货", "你们双休日是不是不送货？", "双休日是否派件", "周日是不是不送货", "周末休息都不送快递", "周末就不配送", "济南中通放假", "周末正常上班麻烦前一定送到", "节假日是不送", "周末送为什么不可以", "是双休日不送货", "周六周日居然不派送", "双休日是不是不送货", "周六周日不派送", "节假日正常送货", "你们是双休日不送货吗？", "国庆送不送"], "question": "节假日正常送货吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "节假日正常送货吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我有份快递需要拒签", "我要拒签", "这个件我要拒签的"], "question": "如何拒签快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何拒签快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["大概多长时间我不去签收会把这个邮件退回去"], "question": "快件多久不签收会退回", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快件多久不签收会退回"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么关闭快递消息推送", "快递消息能不能不要给我发消息", "怎么开启派件通知", "怎样设置快递提醒"], "question": "怎么设置派件时通知？", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么设置派件时通知？"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["是想改为五星好评怎么改呢", "我想修改快递员的评价", "在哪里修改快递员评价"], "question": "修改评价", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "修改评价"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["如何关注微信公众号", "怎么关注中通微信公众号？", "微信可以关注吗"], "question": "怎么关注中通微信公众号", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么关注中通微信公众号"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": [], "question": "蓝牙打印", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "亲亲，蓝牙打印可以通过蓝牙搜索附近的蓝牙打印机设备打印您的快递订单哟。"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["退货什么时候才退款？", "退回去，钱能退给我不"], "question": "退换货以及退款问题", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "退换货以及退款问题"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可以留言投诉吗？", "我有建议在哪里反馈", "意见反馈"], "question": "投诉建议", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "投诉建议"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["上门取件,还需要我自己手写快递单吗", "取件还要写单子"], "question": "上门取件是否需要手写快递单", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "上门取件是否需要手写快递单"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我兑换了优惠卷怎么使用", "为什么多收我的费用？", "有个快递多收我费用了"], "question": "积分兑换的优惠卷如何使用", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "积分兑换的优惠卷如何使用"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["贵重物品能保价吗", "报价怎么算", "保价手续费是多少", "最高能保价多少"], "question": "保价怎么收费", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "保价怎么收费"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["请问现在是不是没有积分签到功能了？", "现在每天不能签到了", "会员中心里的签到怎么取消了？", "如何签到"], "question": "会员中心签到", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "会员中心签到"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["能手机号查件", "用电话号码不能查快递", "知道电话姓名不知道单号能查快递", "物流单号被弄掉", "手机号能查询快递单号", "能通过电话号码查询快递单号", "不知道单号只知道收件人姓名电话可以查询单号", "想只有寄件人收件人地址电话能查到快递单号", "用电话号码查快递", "没有运单号查快递", "单号丢能用电话号查询", "发件人也找不到单号", "手机号可以查快递", "有手机号和姓名可以查快递", "查件没有运单号没寄件历史", "单号不在能找到东西", "输入手机号能查快递", "没有单号", "没有单号可以查询快件", "知道电话能查快递不", "用手机号加姓名能不能查快件", "给邮寄过来 不知道单号查询", "没有运单号查", "有手机号查快递单号", "手机号查快递物流", "可以说收件人名字查询快递", "单号不知道用手机号加姓名能不能查快件", "运单号丢能帮查快递", "快递单丢查快递", "能不能查关于相关快递", "没有单号能查到件", "单号忘记能帮查", "没有运单号手机号码可以查得到", "快递发还没到单号快递员也不发给查询", "查件没有单号查", "想查快递物流但不知道快递单号", "退货单号丢", "无运单号手机号码查快递", "没有快递单号查快递", "不知道快递单号能查到件", "运单号掉查询快递", "能不能帮查个快递没有订单号但是是收件人也有寄件人电话", "可以不用单号用手机号码", "不知道订单号查快递", "手机号可以查询快递单号", "快递单号丢但是很着急想查", "手机号能查", "没有运单号可以查件", "没有订单号能查", "没有给运单号能查", "手机号码可以查件", "不能根据电话号码查快递", "可以用手机号码查快递", "问没有单号可以查快递", "用手机号可以查询快递", "手机号查物流", "只有手机号查快递", "没有快递单号查询快递", "没有订单号查询", "可以通过手机号 查有没有快递", "单号不记得查快递在", "没有单号的话查快递", "运单号忘丢", "可以通过收货电话号码查询快递", "没有快递单号快递还没有到查", "不知道单号只知道收货人手机可以查快递", "能通过运单号查询快递费用", "电话号码查快递", "快递单丢能帮查快递寄到", "不知道快递单号情况下查快递", "无号码查询", "没有单子查不", "通过电话号码查询快递", "不知道单号只知道收件电话查", "手机号查询快递单号", "运单号丢查询", "没有单号可以用电话号码查快递", "没有单号能查物流", "没有单号查快递", "不知道快递单号查快递", "快递单号丢能查快递", "无单号   有手机号查询", "没有快递号用手机号查", "能用手机号查快递", "没有运单号想查下快递到", "没有快递号可以查", "可以通过手机号查到快递", "寄出用名字电话号码可以查", "单号忘记查询物流", "不知道快递单号可以查快递", "没有订单号查快递到", "可以通过收件人手机查快递", "手机号码查快递", "寄同城快寄没有单号查询", "奇东西忘记单号能帮查哈", "收件人姓名和手机号不能查询", "手机号 查快递", "有运单号查询快递单号", "无单号查件", "用手机号查快递", "不知道单号查找快递", "单号丢查快递", "看有没寄快递给", "单号搞丢查询快件", "没有单号只知道收件人手机号能查快递", "没有单号要查单号", "就是寄快递时候单子被掉现在想查单号", "收到快递不知道单号想查询快递信息查", "只有收件人手机号可以查快递", "没快递单号查询快递", "用手机号可以查询单号", "快递不给单号查快递", "不知道单号查询快递", "无单号查询快递", "通过寄件人查快递", "查快递没有单号", "电话号码查单号", "通过手机号查快递", "只知道手机号姓名能查快递", "只有电话没有单号", "用手机号码查寄出快递没有查到", "没有运单号查询", "手机号可以查询", "没有快递单号查询", "不知道单号查快递", "忘记单号", "用手机查快递", "没快递单号查快递", "可以用手机号码查订单", "没有单号查", "输入手机号查不到快递", "不知道单号查", "只有手机号码可以查快递", "没有单号可以提供", "忘记订单号", "快递单没查快递", "用手机号查不", "没有快递单号查", "寄个快递但忘单号", "没有单号投诉快递", "没有单号查件", "没有单号可以查询快递", "只有手机号查快递单号", "快递没有运单号", "没有单号如何查询快递", "忘记快递单号", "是发件人存根不见", "没有单号报电话号码可以查到快递", "手机号码可以查快递", "之前寄个件 单号丢 想查运单号", "忘记快递单号还能查询快递", "没有运输单号查快递", "可以用手机号查询快递", "无快递单号查询", "用手机号或者身份证号可以查到快递", "手机可以查快递", "不记得单号查快递", "没有单号 可以查询", "能通过手机号查快件动态", "忘记快递单号查询", "快递单号没有查询", "快递寄出忘记单号 查询单号", "可用手机号查快递", "没有单号查询快递", "想查之前发出快递但是不知道单号", "有发货地址和收货地址还有电话号码能查单号不", "姓名电话查快递", "没有单号有手机号", "不记得快递编号查", "手机号不能查快递", "可以手机号码查快递", "只有手机号码可以查快递单号", "手机号查询单号", "可以手机号查快递", "手机号查快递", "能通过收件人信息查快递", "手机号查运单", "电话中通快递单号查询 也没有查到单号", "发件人姓名电话地址可以查询快件状态", "没有单号能查快递", "麻烦下  单号丢   想知道快递运送状态查", "运单号丢手机号码可以查", "有个件是收件人没有单号能查到", "只有手机号可以查询快递", "想问下 不知道单号 可以查询到", "没快递单号不能查询"], "question": "没有单号可以查询快件吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "没有单号可以查询快件吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["麻烦查询下快递重量", "重量可以查询", "能不能帮查下件重量", "快递重量帮忙查下", "可以查重量", "帮查发货重量和派件重量", "想查询件重量", "可以帮查重量", "想查询快递重量", "能查询件重量", "帮查快件 揽收重量", "能够查询快递重量", "快件重量", "查询快件重量", "请帮查询件重量", "货物重量是", "问下重量能查询", "想查询快件重量", "可以帮忙查重量", "帮查下件信息重量", "能看到快件重量", "包裹揽收重量截图", "想查询包裹重量", "到可以查包裹重量", "要查询包裹重量", "快递重量帮忙查下？", "快件重量查询", "想查询快递发出时重量", "核实揽收重量", "核实下件重量", "包裹商品收到想查询发货快递重量", "麻烦核实件重量", "帮查包裹重量", "重量可以查", "核对件重量哈", "想查询下邮件发出时候原始重量", "能查询下快递重量", "想查包裹重量", "单重量可以查", "查下件重量", "查快件重量", "查询单号快件重量", "查询揽件重量", "麻烦查询下重量", "包裹重量麻烦告知下", "想查询下重量", "帮查件重量", "要查询下快递揽件重量", "查看重量", "可以帮核实快递重量", "包裹重量", "核对重量", "查不到包裹重量", "帮查下件重量", "麻烦查重量", "查询下重量", "帮我查下这个件重量？", "重量可以查到", "件查询重量", "查包裹重量", "可以查询快件重量", "麻烦帮查快递重量", "可以查快递重量", "想查询包裹揽件重量"], "question": "可以查快递重量吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "可以查快递重量吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递一直显示正在揽件怎么回事？", "快递一直显示正在揽件是什么情况？"], "question": "为什么我的快递一直显示正在揽件", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "为什么我的快递一直显示正在揽件"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["上门取件需要收取哪些费用", "你们上门取件要钱吗？"], "question": "上门取件收费吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "上门取件收费吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["面单保存时间是多久呢？\n快递面单的保存多久"], "question": "面单保存时间是多久", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "面单保存时间是多久"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["这怎么查询快递状态啊？", "怎么查快递进度", "差下物流信息吧", "这个快递怎么还没到，我想了解一下快递的进度", "我想知道我的快递到哪里", "我想查询下快递信息", "我想查一下我的快递到哪里了？？", "能帮我看一下，我的快递到哪里了"], "question": "物流信息查询", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "物流信息查询"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我有一个快递，怎么没有物流追踪？", "我滴快递查不到啊，", "客户在问我人家买了几天不发货", "为什么查询不到订单", "为什么发货了还不更新物流信息", "怎么没办法查看物流", "为什么有单号却没有物流信息", "你好，我寄的快递怎么还没信息", "快递单号查不到", "为什么我昨天发的快递单号在网上查不到", "为什么查不到物理消息呢", "我买的东西怎么查询不到", "查不到快递单号", "我的快件发出去两天了 为什么我还是差不多订单", "你好，我，想问一下，快递还没发嘛，我，查不到", "怎么没有跟踪", "快递寄出去四天了怎么还查不得，去当地中通问了也不给个说法", "怎么快递单号查不到记录", "我寄的快递为啥查不到单号", "怎么差不到我的快件", "查快递查不到", "有订单号怎么差找不到信息", "我有五个快递还没有发送信息", "寄得快递怎么查不到", "快递员说查不到快件在哪里", "商品已发货，物流信息没有显示", "你好你好，请问我那个鞋子的的单号怎么没有物流信息", "昨天上午出的单子，现在还没物流信息？", "查不到快递信息是怎么回事", "运单号查不到快递是为啥", "快递好像还没有物流信息耶", "查又查不到", "我的中通快递单号查不到物流信息是怎么回事", "为啥我还是查不到快递", "查快递查不出来是怎么回事", "你好，怎么查不出快递", "你好，我有一个快递怎么查不到呢"], "question": "单号查不到任何记录", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "单号查不到任何记录"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["改地址要收费用吗？", "改地址需要费用吗", "改地址花钱"], "question": "改地址要收费用吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "改地址要收费用吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我是在杭州下单的怎么显示在江苏的业务员取件呢"], "question": "下单地址与取件地址不符", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "下单地址与取件地址不符"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["赔偿金是怎么给的？", "赔偿怎么支付", "你们的赔偿金是转账还是现金"], "question": "赔偿金支付方式", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "赔偿金支付方式"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["人工服务电话总是无人接听", "客服电话打也没人接", "为什么客服电话也不接", "我打客服电话也没人接", "我打了这边中通的电话打不通", "中通的客服电话打不通", "你们客服电话永远打不通", "你们官网的电话也打不通", "打你们客服电话没人接", "打你们客服电没人接", "我打了这边中通的电话，打不进去"], "question": "客服电话打不通", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "客服电话打不通"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["你们分拣的操作工，是非常的野蛮的吗", "件多了你们是不是会暴力分拣？"], "question": "件多了你们是不是会暴力分拣", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "件多了你们是不是会暴力分拣"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我的快递没有更改地址为什么物流信息显示问题件", "我这边显示说问题件"], "question": "快递记录显示是问题件", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递记录显示是问题件"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可以看到图片嘛", "不显示图片吗？", "为啥发不了图片", "发图片能看到吗", "你能收到图片吗？", "你发的图片我看不到", "刚才发的图片上有啊"], "question": "是否可以发图片", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "是否可以发图片"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我自己称的重量怎么跟网点的重量不一样"], "question": "称重为什么不一样", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "称重为什么不一样"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["发货网点也没有底单怎么办？"], "question": "如何查询发货底单", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何查询发货底单"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["可以电话预约取件吗", "能不能电话下单取件"], "question": "可以电话下单寄件吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "可以电话下单寄件吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["中通有官方微博吗？", "如何找到中通官方微博？"], "question": "中通有官方微博吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通有官方微博吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["自提件是否可以更改时间", "没时间取快递，可不可以晚一点去取？"], "question": "更改自提时间", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "更改自提时间"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["咨询中通快递是否投放柜取快递？", "没有给放到指定地点", "放到门卫不行", "就让快递送到指定地点", "麻烦送到指定地点", "为什么不给送到指定地点", "咨询中通快递是否投放柜取快递", "不能直接放蜂巢吗", "为什么包裹会显示自取难道不可以代签", "是否会送到代收点", "要求送到指定位置", "帮放到丰巢柜就可以", "麻烦送到指定地方谢谢", "能让送到指定地方", "派送到指定地点", "快递放到门卫", "可以把东西在送到指定地方", "只要送到门卫室就行", "不能直接放蜂巢", "是的 派送到指定地点", "送到指定位置", "能不能送到指定地点", "可以让别人代签吗？", "会不会送到地址代收点", "帮放到保安门卫", "帮放在门卫岗亭里", "不用上门送到订单指定位置就好", "会派送到指定地方是", "快件放到小区门卫室即可", "可以让代签", "可以不打电话放门卫上", "把快递送到指定地点"], "question": "要求快递送到指定地点", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "要求快递送到指定地点"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我快件为什么物流显示退回？", "这个件是什么情况退回了？"], "question": "快件为什么被退回", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快件为什么被退回"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我自己把东西送到网点寄出去还要网上下单吗", "不用上门取，明早给你送过去这样还需要下单吗"], "question": "送至网点寄件还需下单吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "送至网点寄件还需下单吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么注册会员", "想用另一个手机号注册，怎么注册", "怎么开通会员？"], "question": "怎么开通会员", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么开通会员"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["收件人是", "我想知道收件人姓名。", "收件人姓名", "查收件人姓名", "收件人电话是", "快递收件人姓名", "收货人姓名电话是", "帮查快递收件人姓名", "收货人姓名是", "收货人姓名电话", "请告诉收货人姓名", "收快件人叫", "可以帮看看是名字收件人", "帮忙查收件人是", "可以查收件人", "可以查收件人电话", "收件人电话姓名", "查收货人是", "能查到收件人姓名电话", "查收件人是名字", "收件人名字是", "收件电话收件人是", "快递收件人是", "能查到收件人手机号", "收件人是谁啊？", "收件人姓名是", "可以查收件人姓名吗？", "可以查收件人信息", "看看收件人名字是", "想查询收件人姓名", "能再帮查收件人手机号", "查收货人姓名", "能查收件人电话号码", "查收件人电话", "是收件人电话姓名", "才能查收件人号码", "是收件人名字是", "收件人是名字", "给查收件人姓名"], "question": "查收件人信息", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查收件人信息"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["打电话没人接？", "不接电话？", "快递员的电话打不通？"], "question": "联系不上快递员", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "联系不上快递员"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["在哪注册快递管家", "快递管家账号怎么获得"], "question": "如何获得快递管家账号", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何获得快递管家账号"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快件 加急", "我想请问一下快递能加急吗", "快递能加急"], "question": "快递可以加急吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递可以加急吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么包装"], "question": "如何正确包装", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何正确包装"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["问一下月度账单怎么导出电子版的", "月度账单", "月度账单提醒"], "question": "如何查看月度账单", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何查看月度账单"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["中通总部地址在什么地方？", "中通总部在哪里？", "中通公司地址在哪儿", "中通总部地址在什么地方", "中通公司地址在哪儿？", "中通总部在哪里"], "question": "中通公司地址在哪里", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通公司地址在哪里"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["下单成功没", "就是还没有下单成功是", "是不是我没有下单成功啊", "想看看下单成功", "想知道下单成功没", "已经下单成功", "只显示下单成功", "刚才下单成功", "算不算下单成功", "我下单成功了吧", "下单成功是", "现在没有下单是", "这是下单成功了吧？", "是下单成功", "是否下单成功"], "question": "查看是否下单成功", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查看是否下单成功"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我司能不能和你司签月结协议呢？", "你好，你们能和我司签月结协议呢？"], "question": "如何办理月结服务", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何办理月结服务"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["查询入仓费和服务费由哪些", "入仓费到底是个什么鬼"], "question": "入仓费和服务费", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "入仓费和服务费"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["要是寄收纳盒有箱子可以帮我包装吗？", "是否提供包装？"], "question": "是否提供快递包装", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "是否提供快递包装"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["为什么不能集运", "我联系集运仓了", "要联系集运", "中通的集运 我要怎么联络", "那我要怎么联络集运"], "question": "中通集运", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通集运"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["乡镇代理点也是你们管的吗", "乡镇快递代收点属于网点管理还是中通直接管理", "乡镇代理点是网点吗？"], "question": "乡镇代理点是网点吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "乡镇代理点是网点吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么包裹异常了呢", "我的包裹现实异常了 怎么回事", "包裹异常，怎么了", "包裹异常什么意思呢？"], "question": "包裹异常", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "包裹异常"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我想修改收件人的电话", "能不能修改发件人的信息", "我要更改收件人的信息"], "question": "如何修改寄/收件人信息", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何修改寄/收件人信息"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["积分可以在哪些地方使用"], "question": "积分有什么用", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "积分有什么用"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["现在找不到快件了", "我的快件都不见了怎么办", "我的快递丢失了", "快递丢失", "我的快件丢了", "包裹丢了怎么处理？", "快递在你们中通寄没了", "我拿回来的快递是被拆过的！", "网点说快件找不到了怎么办啊"], "question": "快递丢了怎么处理", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递丢了怎么处理"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["你们是不是送错位置了", "是不是给我送错了", "他是不是给我送错了"], "question": "快件送错地址", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快件送错地址"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["更换专属快递员", "专属快递员如何中止", "专属快递员怎样取消", "专属快递员如何取消", "中止专属快递员", "我要取消专属快递员怎么弄", "怎么取消专属快递员的", "怎么把专属快递员取消", "专属快递员解绑", "可以暂停专属快递员吗", "取消专属快递员初始化", "麻烦你把专属快递员取消掉", "请问怎么解绑专属快递员？", "帮我解绑专属快递员", "麻烦你把专属快递员终止掉", "我要删除专属快递员", "专属快递员怎么取消", "想问下怎么取消掉专属快递员", "专属快递员怎么取消初始化", "如何取消专属快递员", "专属快递员怎么中止绑定", "可以取消专属快递员吗", "怎么取消专属快递员"], "question": "更换或取消专属业务员", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "更换或取消专属业务员"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递可以存放在网点几天", "快递在快递公司了存放几天", "快递可以在网点放几天", "快递暂放网点"], "question": "快递可以存放在网点几点", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 0, "answer": {"type": 0, "list": [{"type": 1, "text": "快递可以存放在网点几点"}, {"type": 1, "text": "阿斯顿发上的"}]}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["做淘宝,和快递合作的运费价格可以到多少", "与网点合作有什么优惠吗", "怎么可以和您合作共赢", "合作运费报价是多少呢？", "与网点合作需要什么材料吗", "你们那边有跟公司合作的吗", "合作运费报价是多少呢", "淘宝上长期合作的话，有没有的谈啊", "我们是卖家怎么跟你们合作", "长期合作运费", "淘宝卖家如何与快递公司合作", "快递合作件有什么要求呢", "我是淘宝店家怎么和你们合作", "怎么跟当地的快递合作", "淘宝店铺合作运费报价是多少？"], "question": "长期合作运费报价是多少", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "长期合作运费报价是多少"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["是否有签收底单", "能不能让提供下签收底单", "可以把签收底单的照片发给我吗？", "请提供签收底单", "签收凭证", "你那边能提供相当于签收底单么", "让我们提供买家签收底单", "有签收底单没", "能提供签收底单嘛", "请你那边提供签收底单呢。", "能不能让快递员提供下签收底单", "请提供快递签收底单", "签收底单", "麻烦提供签收底单", "可以把签收底单的照片发给我吗", "这个单号能提供一下签收底单吗", "提供下签收底单", "查询签收底单", "签收底单提供啊", "请提供一下签收底单", "有签收底单"], "question": "能提供签收底单吗", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "能提供签收底单吗"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["网点地址在哪里", "那个网点地址在哪", "上海浦东中心这个有地址电话吗"], "question": "网点地址查询", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "网点地址查询"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["更改上门取件地址", "我是发件人我下单寄件然后寄件地址写错了可以更改吗？"], "question": "修改上门取件信息", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "修改上门取件信息"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["招聘需求", "你们对操作工的年龄身体有什么要求吗"], "question": "中通招聘", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通招聘"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["是放快递柜的，不能及时去取可以放多久"], "question": "快递可以存放快递柜几天", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递可以存放快递柜几天"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["实名认证怎么弄", "如何实名制"], "question": "实名制怎么弄", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "实名制怎么弄"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["0元兑换的商品为什么没有库存了？", "我为什么兑换不到"], "question": "免费商品为什么无法兑换", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "免费商品为什么无法兑换"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我刚下了单，你们什么时候来取", "六点下单的怎么没人联系我取件的啊", "我早上下单的你们怎么还可以来取"], "question": "下单后上门取件时间", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "下单后上门取件时间"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["面单收费吗？", "面单要钱吗？", "中通面单是否收费"], "question": "面单是否收费", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "面单是否收费"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["自提快件", "我自己去拿", "自提可以吗", "我可以自取快递吗？", "请安排我自提快件", "可以去自取吗", "不用派送我自己去拿", "我想自提快递", "我自己去拿，不用快递员送了"], "question": "如何自提快递", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何自提快递"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么撤销投诉", "点投诉了，怎么撤销", "投诉怎么撤销", "投诉的派件员问题撤回", "请问这个投诉可以撤销吗", "如何撤销投诉"], "question": "撤销投诉", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "撤销投诉"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递网点电话打不通", "提供网点电话一直打不通", "现在网点电话也打不通", "并且网点电话还打不通", "快递网点电话一直打不通", "快递当前网点电话", "网点电话关机打不通", "网点电话占线根本打不通", "当地网点电话打不通", "给网点电话打不通", "我打网点电话，也不接", "官方电话也打不通网点电话也打不通", "网点电话也打不通", "网点电话也打不通诶", "快递员  网点电话都打不通", "网点电话都打不通", "网点电话打不通是情况", "和当地网点打电话不接", "快递网点电话根本打不通", "网点座机打不通手机", "网点电话打不通一直占线", "派件员跟网点电话都打不通", "发件网点电话一直打不通", "网点电话始终打不通", "网点电话不通", "网点电话打不通是什么情况呢", "网点电话一直打不通", "网点从来都打不通电话"], "question": "网点电话打不通", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "网点电话打不通"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["想问运单号寄件人信息", "发件人是商家", "没有发件人联系方式", "麻烦查哈发货地址", "快递从发货", "发件人怎么联系？", "没有寄件人电话", "能否查到快递单号寄件人", "发件人联系不上", "想查询下发件人姓名电话可以", "想查寄件人信息", "查询 寄件人电话", "查询之前寄件地址", "能否告知寄件人身份", "联系不上发件人", "告诉寄件地址", "也不知道寄件人", "可以查到寄件人信息", "通过快递单号可以查到寄件人信息", "没法联系发件人", "可是需要寄件人信息", "寄件人电话是", "发件人是知道", "发件人电话", "能不能查到寄件人电话", "查发件地址", "可以帮查快递寄件人信息", "可以查到发件人", "如果说不想让收件人知道发件地址可以", "查询发件人地址", "通过名字可以查到寄件信息", "查询单号寄件地址", "不清楚发件人信息", "要查询寄件人电话", "要查询发件人电话", "能不能帮查寄件人信息", "发件人联系", "想查寄件人名字", "想查询发货地址", "查询寄件网址", "想查询下发件人信息", "可以通过单号查询发件地址", "想查下寄件人电话", "寄件人能查不", "寄件人查询", "查询寄件人电话", "查询发件地址", "想查询快递信息", "联系不上寄件人", "想问下寄件人和收件人信息", "想查询发件人信息", "寄件人信息可以查到", "没有寄件人信息", "要查询发件人座机", "查不到发件信息", "可以看下寄件人信息", "查询发件人地址电话", "能帮查寄件人姓名", "可以帮查寄件人姓名", "查询寄件人座机", "想问问寄件人是", "能帮忙查看下发件人", "寄件的手机号是多少？", "想查询寄件人信息"], "question": "查寄件人信息", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查寄件人信息"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我投诉你们业务员，你们业务员会不会报复我呢"], "question": "投诉业务员是否会被报复", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "投诉业务员是否会被报复"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["智能寄件与普通寄件区别？", "智能寄件与普通寄件不同之处？", "智能寄件与普通寄件有什么区别？"], "question": "智能寄件与普通寄件区别", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "智能寄件与普通寄件区别"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["能不能帮查单号货物里面是", "查看里面东西", "知道快递里面是不", "想知道里面是", "能查看里面是东西", "想知道快递里面是东西", "能不能知道快递里面是东西", "里面寄是", "我的包裹内件是什么？能查到快递里面是什么吗？", "快件里面是", "快递物品是", "能查询包裹里面是东西", "能查里面是东西", "还想知道快递里面是", "能不能帮看看里面是", "可以知道里面装是", "快快递里面装是", "能知道快递里面是东西", "能查到是东西", "收到快件但是没买东西可以给查是东西", "不知道里面是", "可以帮查快递是空包", "我的快件寄递的是什么物品？", "可以知道快递里是", "可以知道快递里面是", "想知道包裹是东西", "查里面是东西", "能不能给查下里面是东西", "想知道里面是东西", "可以查快递里面是东西", "想问哈里面装是", "想知道收件人是快递是", "能不能给查里面是东西", "查询邮寄物品是", "查快件里面的是东西", "可以跟说里面装是", "是刷单不知道里面给发是", "想查查快递单号有包裹查", "收到快递信息但没买过是", "能看到快递里面都有", "可以知道包裹是东西", "可以核实到包裹里面有", "包裹里面是东西", "快件里面是东西有写"], "question": "包裹里面是什么东西", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "包裹里面是什么东西"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["也从没有人联系我改时间", "我没有改时间啊"], "question": "未经同意更改配送时间", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "未经同意更改配送时间"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["投诉后什么时候给答复？", "投诉后多久给答复？", "投诉后多长时间给答复？"], "question": "投诉回复时效", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "投诉回复时效"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["那里离我家很远，没办法取件啊", "如果超区了我不想自取怎么办", "什么时间变成自提件了", "超出网点配送范围", "我的收货地址超出派送范围", "包裹显示超出配送范围这是怎么回事呀"], "question": "超出网点派送范围", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "超出网点派送范围"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递代收货款的钱多久到账", "寄代收货款的件什么时候能收到钱?", "什么时候能收到货款"], "question": "代收货款什么时候能收到钱", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "代收货款什么时候能收到钱"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["联系不上淘宝卖家", "卖家我联系不上该怎么办", "卖家电话是空号", "联系卖家又不回复", "联系卖家两天都联系不上"], "question": "联系不上卖家", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "联系不上卖家"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["赔偿标准", "赔偿金额计算方式"], "question": "如何申请赔偿", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何申请赔偿"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["在微信下单", "电脑版微信公众号不能下单", "微信下单上门取货", "微信下单不行是", "我在微信下单", "在公众号上可以下单", "公众号下单", "微信公众号下单", "在微信里面选择预约寄件还是", "微信下单"], "question": "微信怎么下单", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "微信怎么下单"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递代收货款一个多星期了还没到账", "什么时候返款"], "question": "代收款未及时返款", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "代收款未及时返款"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["这个件需要支付多少钱的代收货款", "你好我是需要查询这个代收货款多少钱的"], "question": "如何查询代收货款的金额", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何查询代收货款的金额"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["包裹已经破损", "包裹破损怎么办"], "question": "快件破损或内件损坏", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快件破损或内件损坏"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我想投诉卖家", "卖家服务态度不好", "我想投诉这个淘宝卖家", "亲，我投诉淘宝卖家，我不投诉快递", "我想投诉这个淘宝这家", "我要投诉淘宝客服"], "question": "投诉卖家", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "投诉卖家"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["如何开箱验货？", "什么时候验货？"], "question": "签收的时候如何验货", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "签收的时候如何验货"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么看专属业务员"], "question": "怎么联系专属业务员", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "怎么联系专属业务员"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["官网地址是多少", "中通网址", "中通的官网"], "question": "官网地址", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "官网地址"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["投诉电话是多少", "电话投诉"], "question": "客服电话是多少", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "客服电话是多少"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["签单返还是什么意思", "此件签单返还的单号是什么意思"], "question": "签单返还业务", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "签单返还业务"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["中通速递也是你的名称吗", "中通速递是什么意思", "中通速递一样不"], "question": "中通快递、中通速递与中通快运的区别", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "中通快递、中通速递与中通快运的区别"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["掌中通在哪里下载", "快递员怎么下载掌中通"], "question": "如何下载掌中通", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何下载掌中通"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["已签收产品有点问题", "货物收到后有质量问题怎么办", "收到的快件有质量问题如何处理？"], "question": "货物质量有问题", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "货物质量有问题"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["微信无法绑定", "怎麼绑定不了微信", "如何解绑微信"], "question": "如何绑定或解绑微信", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何绑定或解绑微信"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递什么时候可以查", "快件寄出多久可以查询物流信息？"], "question": "寄出的快件什么时候可以查询到物流", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "寄出的快件什么时候可以查询到物流"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["收件地址是码", "能帮查下快递收货地址", "可以查到收件地址", "可以帮查下单收货地址", "查以前发快递收件地址", "帮查下地址是", "能帮查询收件地址", "能查到收件地址", "帮查单号收货地址是", "收件地址能查", "给查收件地址", "收货地是写", "能查询收件地址", "想咨询下收件地址", "件收件地址是", "现在收货地址是", "有收件地址能查", "想查详细收件人地址查", "看收件地址是", "快件收货地址是", "把快递地址核实", "件目的地发往", "件收货地址是", "查询收货地址", "收货地址", "帮查下快递地址", "能查到地址", "现在收货地址到底是", "单号可以麻烦查收件地址在", "请查收件地址是", "能看到收货地址是", "您帮忙查一下这个收货 地址 是哪里不", "问就是件收件地址是", "告诉我收货地址？", "能帮查下收件地址", "可以帮查快递收货地址", "能查到收快递地址", "收件地址是", "收件地址在", "查收件地址？", "收货地址是", "刚刚订单查到收货地址还能发", "查询收获地址在查", "能帮查下快递收件人地址", "查询收件地址", "帮忙查收件地址是", "快递最终地址是"], "question": "查收货地址", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查收货地址"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["历史寄件", "查以前记录", "想查询之前寄件单号", "我想看看以前我寄的快递的记录，可以吗", "想查询历史快件", "下单但查不到", "寄件记录", "查询下单记录", "刚才是不是下个单", "怎样查找以前的快递", "月份寄件能查询", "看上单寄件", "查历史寄件", "查件历史", "查之前发快件", "想查月份计件记录", "查发快递", "寄过快递信息", "在微信里面下单收不到下单信息", "我想查历史快递记录", "能不能查历史快递", "能不能给查查近寄出快递"], "question": "查询历史快递记录", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查询历史快递记录"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["赔偿是给收件人还是给发件人", "赔偿对象是赔偿给发件人还是收件人呢？", "收件人如何申请赔偿", "你不应该理赔发件人？产品已经被拒签退回来了"], "question": "赔偿对象是赔偿给发件人还是收件人呢", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "赔偿对象是赔偿给发件人还是收件人呢"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["陆运吗", "还能空运的？", "你们是空运还是陆运？"], "question": "咨询中通运输方式", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "咨询中通运输方式"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我这个快递现在是不是在退回的路上", "退快递怎么查询快递信息", "能不能查查这个快件啥时候能返回去？"], "question": "查看退货物流信息", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查看退货物流信息"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["中通邮件收发章是什么意思", "签收人:邮件收发章 是什么意思"], "question": "邮件收发章是指什么", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "邮件收发章是指什么"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["网点停了是什么意思", "泰州市戴南镇停发了吗"], "question": "网点状态查询", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "网点状态查询"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["我六点下班，最好下班前取件", "如果要是现在预约取件能过来吗", "我想知道他什么时候来拿", "五点半左右可以拿件吗", "什么时候才来收啊", "我想问，今天弄网上寄件，明天快递员会过来吗", "你们快递员什么时候上门去取快递", "快递员上门来取已经预约要发送的件", "什么时候过来取件", "什么时候可以揽件", "大概什么时候上门取件？", "什么时候可以上门取件？", "我的快递中午再来取", "这里有个快递上午发的单，什么时候可以来收"], "question": "成功下单后什么时间上门收件", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "成功下单后什么时间上门收件"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么申请快递面单？", "我需要快递面单"], "question": "如何申请快递面单", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "如何申请快递面单"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递发空包要多少钱", "帮忙发空包可以吗"], "question": "是否可以发空包", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "是否可以发空包"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快递老在装袋扫描中是什么意思"], "question": "袋装扫码是什么意思", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "袋装扫码是什么意思"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": [], "question": "网点要求客户一起承担寄违禁品的罚款", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "亲亲，抱歉给您带来不便，罚款是属于发件网点承担的，您是不用出任何费用的，请输入“转人工”客服MM为您处理。"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["什么叫错录？", "什么叫错录签收，不是拒签了的？"], "question": "什么是错录签收", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "什么是错录签收"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["寄出快递什么时候可以查询到物流", "为啥寄到温州", "快递分拣错误怎么回事", "为什么这个包裹寄到三亚那边去了"], "question": "快递分拣错误怎么办", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递分拣错误怎么办"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["怎么快递管家登不上呐", "你们的快递管家软件今天是不是挂了"], "question": "快递管家无法登陆", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递管家无法登陆"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["发件网点能乱开证明吗", "派件网点能乱开证明吗"], "question": "开证明", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "开证明"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["物流显示拍照签收", "怎么显示拍照签收"], "question": "拍照签收是什么意思", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "拍照签收是什么意思"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["能查历史寄件", "之前的单号我现在还能查到记录吗？", "我想问问快递记录一般能查到多长时间之前的呢？", "快递记录保存", "快递记录保存多久？"], "question": "快递记录保存多久", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递记录保存多久"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["现在改地址还能送过来", "有个快递送能不能在送次", "时候帮重新派送", "请安排再今天送", "周一派送", "可以改派送时间", "更改派件时间", "不在想要过取快递可以", "能否可以安排再派送", "能修改订单派送时间", "能不能更改派送时间", "可以不可以改收货时间", "能不能明天重新配送", "明天能不能重新派送", "想问能不能明天再安排下派件", "能不能修改配送时间", "想要延长收货时间晚些收货行不行", "快递能不能明天在派送", "下可以改配送时间", "可以更改时间", "可以更改派送时间", "快递能不能明天或者下午送货过来", "现在没有时间拿货能换个时间", "送过来没人收", "能不能让快递员明天再联系配送下", "能给重新安排发货", "快件能不能麻烦帮忙联系下明天再配送", "不在所以能明天送", "快递能不能晚点配送", "快件能不能明天中午再派件今天店里没人", "时候再来派件", "快递可以修改派送时间", "延迟收货", "更改下送货时间", "时候能重新派件", "请过在送快递现在不在深圳", "可以更改派送时间吗？", "可以改时间送过来", "可以延迟派送改为明天再派送", "改收件时间", "收件时间可以改", "份快件能不能明天在派送", "想问能不能修改快递配送时间", "可以延迟派件吗", "时候重新派送", "可以改派送日期", "请改时间再送", "能不能改派送时间", "能帮延长送货时间", "收货时间可以推延", "快件能不能让快递员明天送货", "派送时间能改", "修改送货时间", "不在家请把货星期一送可以谢谢", "申请更改派送时间", "快件时候能重新安排派送", "改明天派送"], "question": "更改派送时间", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "更改派送时间"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["单怎么取消", "取消预约的寄件订单", "我可不可以取消下单，在重新下单"], "question": "取消下单方法", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "取消下单方法"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["这个单号快递到货派送员为什么不打电话联系", "快递送货都不电话联系？"], "question": "快递送货不打电话", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递送货不打电话"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["快件下一站到", "查询快递下地址", "要久才能到下下是", "下一站是送到", "下一站时候发货", "下一站发往哪里", "快递下是发往", "快递下一站地址", "下一站是发往"], "question": "查询快递下一站地址", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "查询快递下一站地址"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}, {"extend_questions": ["手机上没有快递员电话", "想知道快递员电话号码", "手机在家没有信号快递员电话", "业务员电话", "件派件业务员电话", "有没有电话咨询快递员", "有派送电话号码给", "想问下快递员电话是想联系", "有快递员联系电话", "可以帮忙查下单号 快递是快递员送", "快递员电话给下", "需要快递员号码", "可以给配送员电话", "没收到货麻烦给快递师傅电话", "快递员电话阿", "快递员手机号码", "配送员电话号码是", "要快递员手机号码", "通江路快递员手机号", "快递员电话是", "能把快递员电话给", "快递员电话号码没有", "快递员电话给个", "快递员手机号给", "能发配送快递员电话号码过来", "街快递员电话", "能给快递员电话", "在位置发个快递员电话号给", "麻烦给件快递电话", "想知道快递员号码", "附近快递员电话号码", "帮查快递员手机号", "把送单号快递员电话给", "快递员手机号", "快递单快递员电话能给", "想问下送单号快递员电话", "查询快递员手机号", "派件人闫宁电话联系方式", "能不能把快递负责人电话", "请帮查询下快递员手机号", "麻烦问快递员电话号码是", "查询送快递快递员电话", "快递员电话多少", "可以帮查下派件员电话", "快递员手机号是", "派件员电话给", "快递派送员手机号能给", "派件人电话有", "快递员电话号码给", "快递手机号是", "知道快递员手机号", "想快递到达派送站 可以给快递员电话", "有派件员号码", "快递小哥手机号是", "需要投递员电话", "联系快递员", "能帮查询下快递员手机号码", "帮查下快递员号码", "给派件快递员电话有", "没有快递员电话", "查快递员电话号码", "想问下有快递员电话", "有没有派件员电话", "吴丹有电话不", "派件员手机号给", "请告诉快递员手机号码", "想知道快件送货员电话", "快递员电话是多少？", "需要快递员电话", "件员手机号", "快递员电话号码", "发快递员号电话给", "派件员电话", "想问问快递员电话号码", "联系快递小哥", "能告诉快递员电话", "或快递员手机号也可以", "可以把送件人电话号码发给", "派件员号码有没有", "有派件人电话号码", "快递员电话", "请给提供快递员电话", "帮查询派工快递人联系号码", "没有派件人号码  也不知道是派件", "把快递员电话给", "为什么还没有快递员联系", "想要快递电话可以", "查询送货员电话", "需要派件员电话", "快递员电话多少？", "想问下派件员电话号码是", "快递员电话号", "想知道快递员电话", "派送快递员电话", "要快递人员手机号码", "有快递员电话号码", "还没有快递员联系", "想问下快递员电话", "快递员电话方便给", "把快递员手机号发给", "件员电话给", "快递员电话给", "可以查到派件人电话"], "question": "快递员电话是多少", "special_answers": [], "templates": [], "isActiveHook": 0, "suggest": "[]", "dir_id": "0", "type": 1, "answer": {"type": 1, "text": "快递员电话是多少"}, "parent_id": null, "instruction": "", "effect": "2020-09-23 16:15:29", "expire": "2099-12-31 23:59:59", "name": null}], "faqNoResponse": []}, "basicResources": {"voca": [], "agent": {"id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "name": "效果验证", "description": "", "isPublic": 0, "developerAccessToken": "d066d3b8-531a-48b0-a52f-5892890c2585", "createdUserId": "4308361932", "createdUsername": "wlj-test", "lastEditUserId": "4308361932", "lastEditUsername": "wlj-test", "industry": "2e3c886e-3dc7-4b10-b3f7-62746a017995", "type": 1, "status": 0, "online": 1, "delete": 0, "created": 1600848685000, "updated": 1600848685000, "kgProjectId": null, "kgToken": null, "auditLevel": 2, "effect": 0, "language": "zh"}, "webhook": null, "channelManage": {"channelDimension": [], "channelSpecialValue": [], "channelDict": {}}, "thirdEngine": [], "settingAgent": {"_effect": 0, "agentId": "e9f05e30-cab5-4585-bc25-aab2729fe082", "id": "e77890c5-f21c-4b4f-98d5-8233cf86d6f2", "name": "效果验证", "description": "", "config": "{\"confidence\":{\"faqClarifyThreshold\":0.6,\"intentExampleThreshold\":0.9,\"intentClarifyThreshold\":0.6,\"chatThreshold\":0.85,\"faqThreshold\":0.8,\"intentModelThreshold\":0.8}}", "version": 1, "created": 1600848686000, "updated": 1600848686000, "limitConfig": null, "language": "zh"}, "sensitive": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "repositories": [], "words": []}, "dirList": [], "dialogVariable": [], "instructionManage": {"instruction": [], "instructionVariables": []}}, "intent": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "importData": {"EXAMPLE": [{"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "快递员送货不肯进小区", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "00f5abe4-252b-4826-96cd-a348bfdea63b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "如何重新激活用卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "04aa5c67-ff14-4a37-8882-4a0a31f54bf7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "为什么不上门服务呢？", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "04badd60-2cdd-4e85-9cc5-397ec6b68e47", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "哪里可以查到我的借款额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "04bd30d2-7f05-4071-b9b7-a1709123f5af", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "不送货上门", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "04d66b9f-0301-419e-9c95-bfff407d6b5f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "咨询我有多少额度可用", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "065867c5-4b78-4496-861c-4e7b4475b2ab", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的快递送到哪里了", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "065f456a-bffb-4497-88e3-bb9fa5209c80", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查询物流信息", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "083cfd01-1f4b-42ee-bcef-58086f5236a1", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "这个信用卡分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "08ee2f74-5791-4fe8-b79c-45f9f0383f08", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查一下进度", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "09ffb8f9-c864-48d1-96cc-194160e43a62", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "运费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "0a621d09-63c8-49c4-911d-003963a6a1b2", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "还可以贷多少钱", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "0c435d67-d76c-43e9-b9f0-82a8527511e5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "<PERSON>huoshang<PERSON>", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "0da43461-bd0e-4cef-a7fc-13da600ac82b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我把卡丢家里了能帮我查一下卡号", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "0e036aee-2cd1-44ca-9514-10a81ac9b52f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "物流", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "0e13a265-f29e-4734-ab38-235aec6c1eea", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "快递快2个月了还没收到", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "10f342db-60c7-4c63-98bd-7dd9563dee77", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我查一下这个快递到哪了", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1193a088-88fc-41b1-8542-27fe3f6523f4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要的卡片怎么激活", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "12fd552e-8808-4011-bbc7-1f2f7314ff52", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "rengongs", "intent_id": "sys_intent_7", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1343517c-8927-48b5-b96b-23bfcfde065b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡新的信用卡就是住宅申请分期怎么申请", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "13a203e0-5c8b-4218-a624-0cc9dc983e4e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整额度提升额度可以吗", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "13aa8d45-6110-441b-ad2d-937b0720f152", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "为什么要自己去取？ 不是派送的吗", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "13e1740a-894d-481f-9371-d474952fef15", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整额度两千失败了", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "158a530b-7d1c-4e18-8f05-f9ec86359a93", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "可以帮忙查一下快递到哪儿了吗", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "15f418b1-a4ca-41fa-84f3-a9431768f8bd", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "需要转人工服务", "intent_id": "sys_intent_7", "md5": null, "updated": "2020-09-23 16:27:57", "id": "160b505e-68a7-4c1a-8d6e-39b97a4c2012", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "麻烦调整临时额度吗", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1772cf62-041c-46ec-8445-b267a9f46a44", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "手机银行app可用的贷款额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "17d4b2d8-eb4c-43f4-8297-c01f865ad4bb", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的运单676767676767，为什么不给我送到家", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1a57c8c3-a450-4151-b7ab-15c29044ce19", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整额度高十万块钱", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1b6b63ff-94ef-4853-bf24-e1756ae2b89f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要开通信用卡在国外的卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1d697add-aab5-47c5-b871-7f799c60a333", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "请问我的件什么时候送过来", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1d7f8738-f6e4-40f1-b6d1-0a72320c7e8e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "寄快递怎么收费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1d848b83-5061-49e3-9309-96018783c8c3", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查云非，在哪里查询呢", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1e1941f3-3a8d-4f11-b595-8c90045cd8fc", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "怎么查询自己的银行卡号？", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1e805e74-131f-4985-9665-8f966dd72fab", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "cha一下物流信息吧", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "1f82283d-1be7-41a8-84d1-004b229a4a43", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整网上支付额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2119e6fe-3b65-4f32-8e2b-6b65af7618f3", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想激活一张信用卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "22723f4c-2744-4928-ae0b-8947d252cc33", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "可以调整临时额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "227c4768-d261-4566-aeab-caa6a3be14ad", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期怎么开通", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "250fc331-6c2e-4cb9-ae64-4a9aec47aa0c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "结束吧", "intent_id": "7caa6cae-5796-4bae-9d0f-673f420ba8cb", "md5": null, "updated": "2020-09-23 16:27:57", "id": "25239309-d6a0-47e2-b57a-328003616669", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "催一下快递，谢谢", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "263e47bc-bf04-466d-ada0-0f3d436ac88a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "今年激活用卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "268c30ba-a559-4021-b558-71b774fcbba5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的信用卡怎么分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "27c37a96-17ae-4e48-90d6-1d82a4d4235f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "好的", "intent_id": "sys_intent_4", "md5": null, "updated": "2020-09-23 16:27:57", "id": "27c7dfec-5cbc-4cbc-a1bd-854077c3c38e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "显示快递点代收，为什么不送货", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "27d1919e-7fc8-44bc-9f5f-ecd0a4392810", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "没额度调整", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2868703c-8a74-4f20-9398-17ee93428aa4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "cc", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2abeaf23-e0fa-40d9-9088-1502e44548eb", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查询卡号", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2b3d8bdc-a290-4b92-aab8-f72f14e50541", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "申请卡激活", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2cc3030d-a0b7-4ddb-9c12-e1279bcc2df5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "能安排一下快递送货上门的服务吗", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2ea3e2a9-a9d5-4faf-9d23-77f77dd05267", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "催收快递，运单号888888888888", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2f301d7f-317a-4863-bc55-c65cb961d8e6", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "多次反馈不按地址派送！", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "2f647018-5511-44aa-9101-eb0d5913a924", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "怎么查我的额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "33a54e5c-cdc7-4c40-81b8-4be34fb6154c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "寄件费用收费规则", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "346f498f-8364-40a0-b58c-274a54dfa771", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查下额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "34defbb5-1f17-451c-8fba-b389af259f6e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "好的，就到这里", "intent_id": "7caa6cae-5796-4bae-9d0f-673f420ba8cb", "md5": null, "updated": "2020-09-23 16:27:57", "id": "36c9753c-2282-4d76-904f-72aaa7e39601", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡取多少分期吧", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "3904d54d-e3b2-4f45-81c9-a5eedd21527e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "这个快递单查一下是什么情况呢", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "3a33a48d-da06-43a4-b223-283ea0bd5121", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想让快递员送货上门，但是他不愿意？", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "3afae5ee-5844-432b-a9cd-dd199fc597ce", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "新卡调整固定额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "3c11f4ca-8dba-477c-ab6e-0f479260e853", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的快递为什么是显示是自提件", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "3c8ec054-3469-44d5-910d-2914208ce52d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想办一下分期的怎么透支信用卡透支余额可能", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "3ee525c3-89a8-4951-97c7-11126b730227", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的卡测评卡可以开卡吗", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "3ff8b4ed-b037-4076-b521-a4681b36c9f1", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我查下我还可以借多少钱", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "40d195e5-640e-426f-94c4-4752d16b4b8c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "42e50cb6-5f37-42fa-a98a-841038b2f44b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想激活我的信用卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "44c4f9ff-d4f1-4a8b-82c1-d2b6f441b04a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的额度可以给我看下吗", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "47a117d2-b571-4931-a542-c9e609d327df", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "等等，我还想查一下运费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "47b4df9d-240a-40ca-b802-200d89216612", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "差运费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "48a33d8e-cfb7-49b2-be8e-3a4df248509f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我忘记了我的卡的卡号能不能麻烦你们帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "48a4d0ae-3542-4236-bacd-087981202f70", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我不记得我的银行卡卡号了你们能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "492b1325-ecab-4ac1-98e5-80c8936b9f6e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "请问调整固定额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "4a5685b8-7f67-49d9-8ff8-5b4f0751e575", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "贷款额度如何查找", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "4bb1d404-f7f4-4adc-851a-8aebcb0c3b75", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期人工服务", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "4df07c56-d916-4829-8447-ad3095e61b2a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "账号付款忘记了卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "4ec5da77-9014-4d67-8914-6bdff3c2ee0c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的快递怎么不送来叫我怎么去拿啊", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "4ffd1e03-87cd-4279-9eb0-af65b3a6619a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "临时额度调整能调吗", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "506ca157-aa90-4f22-8bfd-c28e91252095", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "你临时额度调整", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "50a55999-056f-4fe7-b085-f9b9e5807422", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "在外面转账需要银行卡卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "512d507d-5048-4271-9752-d396da0ed25c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "请叫快递员上门服务，谢谢", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "51d3986c-bfbc-4b5d-a856-87c6f116f5b4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查询运费的窗口在哪里", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "52a4d5c8-5d29-4ab7-a9b2-662cc6cebe94", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "结束", "intent_id": "7caa6cae-5796-4bae-9d0f-673f420ba8cb", "md5": null, "updated": "2020-09-23 16:27:57", "id": "52abc515-318f-4d3f-b9f8-f3669e168011", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "激活用卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "52c89152-6678-40e0-a015-dd3c3048cec5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要激活我的卡片", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "530026e3-4489-4aea-8534-b4d66955a47b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "银行调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "56ce4501-5df0-4456-b3be-c86750b0ea5f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "转播人工", "intent_id": "sys_intent_7", "md5": null, "updated": "2020-09-23 16:27:57", "id": "57442a73-b233-46d1-872b-9c9093900af7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我没带卡现在需要卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5820bf43-682e-4b02-a5e3-9248e3466a6c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整额度多少额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5a9011c2-b56e-4ee7-b569-aa284188ad17", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的信用卡现在要开卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5c36b1dc-379a-40ce-9049-24785e89d9f2", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我这边忘记了银行卡卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5c5652ac-233d-4964-920a-0ec8de331305", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "这个贷款最高可以借多少", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5c7e5cf2-551e-4e49-be86-a7922760dd2b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的的信用卡信用卡给我本期可以办分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5cd2d5d9-8e80-4a64-b61a-aec2ad486a15", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "中通快递不送到货地点吗？", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5d44d26f-7632-43ec-a603-8df4fb316967", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "语音调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5efa2be1-4bb4-4809-bd48-d01d9b8ec4df", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "人工服务现在方便吗", "intent_id": "sys_intent_7", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5f351afb-7afb-4a15-b282-d8bf84ce1711", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "催快递", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "5fe67f5e-c63e-4e5a-9c76-b13dd5898ec9", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "请帮我查一下物流信息", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "602d1b9a-be34-436c-bfb9-f657df669b33", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查办帮调整临时额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "60a09b94-1f3c-4c1d-afe7-069ab648e56a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想问运费怎么计算", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6543fce4-5489-42ff-9828-ede8bfb498ef", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我需要办一张分期另外信用卡", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "663d797c-ddb6-4b2e-8fda-3063a89fd683", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "再帮催一下快递", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "671c43ef-33ca-45c7-9f5a-30e0944b5bec", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "快递收费标准", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "67490a7c-9978-4870-9c59-87aedaa63cbf", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "人工", "intent_id": "sys_intent_7", "md5": null, "updated": "2020-09-23 16:27:57", "id": "679c3082-a1ac-4e3e-870c-edc79f2bfc04", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "怎么查物流", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "68b7ce55-2c22-46d7-81a7-52837cba43f0", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的快递到哪里了，我的运单号是777777777777", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "69d2c059-03d0-4b9b-9309-586c970c9c38", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "怎么查询银行卡号", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6b6cc517-505b-48a0-9b38-f6509f65de20", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "忘记了银行卡号怎么查询", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6ce281ae-6ecc-4d94-a8f6-9ae9a7c95765", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "你们家快递运费怎么计算呢", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6d034afe-51cd-42af-90b7-629db37fc597", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的快递能快点到吗", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6d0c48d1-33f7-4ef8-a2a5-f947b561f6f5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我能借多少", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6d17de3c-a51f-447f-bf1f-bc2f016df040", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡你分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6d4e98ce-408b-4018-8b27-cddfab4d5143", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "激活激活用卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6d506a73-ab3c-4520-b2fb-5913e8151536", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期的", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "6def760d-509a-4907-be66-cde7ce677c6b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "如何能够调整提升固定额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7001772b-319c-4bb1-b17a-3e73bf596ad2", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "ca<PERSON>un<PERSON>i'", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "70609fc9-560d-4b4a-9b8e-09a2b1e30e17", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "能提供我一下，查询物流信息的链接吗", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "735524e6-0d52-4831-95ce-c24be9cbbec6", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期还款", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "74a66381-96d6-4b5f-bcc3-07693e98e820", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "用卡信用卡开卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "75830c43-180e-413d-9b30-119c84593f29", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "这快递到哪里了，能帮我查一下进度，催一下吗", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "76b91234-f413-4c2f-8f14-7a139e5b1953", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期办理", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7a21f866-910d-4389-9864-4a897b01b951", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "浦发银行如何查询卡号", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7a901c6c-c6ed-42c9-bd9f-5b49d0140580", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要查借款额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7b2fdaf1-f97f-48fe-9118-76f227f27902", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "每年帮忙调整额度行吗", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7bc149b0-1646-47ca-8a22-8b7d20555190", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我查询借记卡卡片有效期", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7c3c006e-153c-44fc-8404-2ff237140b68", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查询我的快递到哪里了，谢谢谢！", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7c9f4b58-f540-4166-a289-de9adabb6494", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "贷款额度有多少", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7d65d320-943f-410d-92ca-24ce39041cb0", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "激活卡什么", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7e9b9a33-02aa-439f-aa90-c6ce82e47907", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我催催快递", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7ebaaad9-3fa8-456a-8eb9-bd9ad8f4a0ec", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查一下快递到哪里了", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "7fc05b57-56f4-4d47-865d-5a009c550988", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "最低信用卡分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "80049fda-bd2f-4935-be6c-2192f21f814c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期人工人工客服", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "811245af-1021-4cd1-9376-fb01479e4df8", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "物流费一般多少钱", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "813fa975-08e4-40ac-9392-b023c1bf41e6", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查信用卡办理分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "81f16aa4-38a3-4501-860d-a7bb01de7f2b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想要查一下欠款的额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8279e3dd-6576-43a1-81ae-a0240a0c942a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的额度在哪儿看呢", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8409b200-2a56-4d2d-be81-e7c8e9a324e3", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "激活卡需要激活", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "84f570fc-b9fc-4ce9-bca4-234a9374de8d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的额度怎么查", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "854191ab-7270-419f-9bf4-9af4bf7e7055", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡消费分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "85bd8913-6c59-41ea-a6be-64d99e707db7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8698f979-ff38-4518-b80e-08283daad8e9", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "分期要交信用卡", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "88a25a62-b22c-4972-b49b-74df6f8714a7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "账户调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "89336d1e-d2e6-46ec-99fd-1ae7d4cfc1a5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查云费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "89610093-3771-4a7d-86b4-c794e8552faa", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我现在要我的银行卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "899d1a59-8076-4da1-b5f0-5d903a5d4174", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "快递怎么还没来啊", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "89c07231-f672-445f-8df9-9cb32c2cae5d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "取消额度调整", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8a33792b-a688-44ba-a27f-245616440aa1", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查运费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8a461b4f-30a0-4a97-90d4-a039cff3439e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "功能想要一张信用卡为什么分期要怎么办", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8b6aec2d-3f8f-481b-94e0-444a7213ae80", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡额度想办分期为什么不能分期吗", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8d69be41-dd42-4a07-815f-b57e9c0c3783", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要查运费！！！！！", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8d935781-8b94-47ae-a09c-373092c0f6e3", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要查运费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "8fdc6784-35ca-49b5-8d36-bdd48824be77", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡都分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9211114f-bfdb-45f8-947d-2df721ba3c54", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想问问我之前开的银行卡的卡号是多少", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "92208247-96c0-4b78-b4ae-5dd9705fe4a5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "激活卡激活卡号", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "93295945-013e-4b63-9c89-09e525d3ba21", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "上哪查我贷款的额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "96861174-aaaa-4f13-b0ad-bd5583b960db", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "麻烦转一下人工", "intent_id": "sys_intent_7", "md5": null, "updated": "2020-09-23 16:27:57", "id": "973825fd-3cbd-48ec-8f2e-cadcb941f9b4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我看下额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "974ca1fc-26d4-4bab-abac-9748c8809849", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整额度哪儿", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "97a4ba78-8602-4c7c-b199-c8490723a902", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "为什不上门服务呢", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "97eb0e5c-6e50-4a3f-bacb-d009849f893c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "从上海到北京，首重50？！！抢钱", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "985188d3-7183-4788-a202-ab4e8d5d667a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "这个订单运输正常吗", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9a067a58-7426-45ef-810a-0898f816130c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查下我的快递，我的运单号是777777777777", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9b0a8d03-7946-44f6-96d4-2db62a8bc640", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "就不能把我的快递送到家吗", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9c051aa2-2393-41ea-8bc3-09284d576453", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的运单号是676767676767，我的物流到哪里了", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9cd86ce7-9f40-4b4d-a6c2-581246d7e729", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我查个快递", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9da039de-122b-4b88-bdb3-f5170e749435", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我这边急需银行卡卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9dcdf54a-1574-4e65-8448-d185009f00a8", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的运单号是123456789123，催收快递", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "9e87299a-8492-44ef-a3b9-73ef2f3ecc5e", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "新卡调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a119087c-c8a0-4053-a78e-490fb4c8c7af", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "运费查询", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a12c43b5-99df-4304-9d75-8abeb191bb99", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "临时额度可以调整", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a1701507-8c26-442b-b183-6875eeed4909", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要短时间内开通用卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a244b19e-cb12-41d7-bc44-4f4c79f025af", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查物流", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a26c5099-9dd2-40d0-94cf-1bad7151e889", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "银行卡处理帮我激活一下", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a2f6ba6e-96f9-40f7-90f9-641ebbadbc6d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "那临时临时固定的固额临时额度可以调整吗", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a2f94b78-5b4a-4838-ad73-5af2b2a5e4f1", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a301f8e4-5a98-4f6b-b3a9-ad3b54146830", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想问一下拿到这个新卡怎么激活了吗", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a3ab1efa-da3a-4016-a5a8-e59ce6e8b914", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "额度调整不了可以考核多少几个月", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "a4ee6316-d982-402c-8202-5514efec1825", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想问问我之前开的银行卡的卡号是多少啊", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ab8f918e-670c-4b7e-a2bc-67875e1fecda", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "有多少贷款额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ac818835-b25a-4ebe-8ae4-7d6153b4b756", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的快递怎么不送到家", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "acd00f69-a80a-461f-a554-9716039c029d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "那么这个调整临时额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "af3100e1-a3a7-44e0-a209-6e885b816984", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "bs", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b02d9b45-449c-4b28-a0dc-af9345206df3", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "寄快递怎么算", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b08d4ce9-d7f0-4914-a334-ea5f274a7161", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想激活卡的话要怎么弄", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b0905dd9-be6a-4293-847a-b5086ea85979", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我这边急需银行卡卡号能帮我查一下吗", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b49c34c0-1c66-494d-b882-f1f954e56814", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的银行卡号忘了怎么查询", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b4a97486-d883-4ae7-984a-b910c772e1fe", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我查一下这张卡激活", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b50e1635-01f2-4137-a2d0-2e890b5faeb3", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "催收快递了", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b693eabd-310d-4851-a07b-35f3f5ad8de4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "两万分期信用卡人工客服", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b7182fe7-ad73-43aa-8eb4-1cdeed9f24af", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我需要用银行卡卡号付账能帮我查一下卡号吗", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b71ba422-9a81-407c-b460-e8143ff045c4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "运费多少钱", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b71ed2f7-f999-4882-93a9-cc887b26eccb", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "百分贷额度在哪咨询", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b73103bd-b948-4bf1-8aaf-152d7b5cbc84", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "中通快递运费如何查询", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b7daf050-3db1-43dd-8d8f-8b1c11500b64", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "账号付款忘记了卡号能帮我查一下吗", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b831d66c-5ce6-4090-8130-6dadf573b997", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "授信额度在手机银行哪看", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b8b0dd8c-01a5-41f0-bdbb-1b0edc24754a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "卡片有效期最长多久", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "b9073fb3-817c-46c5-9ecb-e07dc4fe8c02", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "怎样查询名下的银行卡号？", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ba11ab4d-0c49-446f-a09d-ec203436bd35", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "能不能激活", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "bc37dabd-73e4-44d7-b7aa-5c3e65934c22", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "中通快递收费标准", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "bd4fd1c8-39f5-402f-810e-0897ddbe35c0", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡现金分期办理", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "bda50bd0-834b-4bac-9d28-7dd3da45d011", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "能帮我转一下人工服务吗", "intent_id": "e1375bf5-4daa-4fca-ba97-41a7a69f28a8", "md5": null, "updated": "2020-09-23 16:27:57", "id": "bf6e8b14-3bf3-4bf0-b78c-3f931c9b773f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "激活卡片", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "bfaf98d5-8ffc-45b5-88e9-e7ececa61214", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查一下运费", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c05155ab-36b6-4a5b-b247-2c5c7014b946", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "帮我查快递签收地点", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c058687a-a80d-411b-aee2-509678d740ac", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "收到信用卡分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c11fec80-a2e0-4fab-a1b1-50a7b0ad8d54", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "好的，再见", "intent_id": "7caa6cae-5796-4bae-9d0f-673f420ba8cb", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c1869a1f-7382-4378-8219-faf46fde9fd8", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整额度调整额度调整额度调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c27ea003-84e5-425d-8b55-7d256f03706d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我需要填银行卡卡号能帮我查一下吗", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c2c7c5d5-0669-42fb-af58-b0c28e101223", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我需要填银行卡卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c41af8f5-c019-44cb-b94d-872e42c8a932", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡分期激活", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c4454c48-cfad-4bd9-8969-69d06ebeee13", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查一下快递", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c49d11bd-31ff-4d6e-a915-d9f1763fc64d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整额度历史调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c4b1cd3d-cc05-4fb0-a54a-57fce604e508", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "寄件多少钱一公斤", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c53f9e7f-3cc7-43dc-ad9a-3dd03213f09a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查下我的快递，我的运单号是123456789123", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c6b0e1a7-5026-473f-a43d-88f2d9b85af2", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "怎么查询自己的银行卡号", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c6dbff33-1e0e-459e-bae5-4a32761887c7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "物流信息查询，我的运单号是676767676767", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "c8412f5c-d52e-43ea-b7e7-d0760c9f47da", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的快递具体到哪里", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ca8626f4-76f2-4701-9e8a-00e2250d8176", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡现金分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "cadd99cb-6f5c-40d7-9c79-a217358ea73f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我卡的收到不知道怎么激活", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "cb09e15e-e108-4817-8c47-c49e4d185fb7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "cyf", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "cb88f0e0-fc74-411e-b1a7-de6ad3d5f572", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "忘记银行卡号怎么查询", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "cb9379f1-2c4b-4983-8614-e1f2bfba3807", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "怎么办信用卡办理分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "cce0f897-a29f-4976-ab21-38ecd420f818", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的百分贷能借出来多少钱", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ce0bfae7-34b7-486d-82ab-0c3a00ff7a7c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "永久额度调整可以查吗", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ceeead95-d2e0-46bf-84c5-449733a23d2d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "你们中通快递不送到货地点吗？", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d0452171-467a-41dd-85e6-cdfc7463b1be", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用卡想办分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d2883c52-0d5f-4c14-aafd-2dffc7ffce3c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想知道百分贷可以借多少", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d2b993ee-b436-4fc8-a94b-a476f513b510", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "你好我忘记了我的储蓄卡卡号能帮我查一下", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d2c350c1-7b2a-4d60-afb8-e81f9ac868a4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "什么时候配送", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d3c07d78-9118-4824-a0ed-bdd2cd9c3de5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想问一下我这个卡到底是怎么激活呀", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d4930676-43dd-4b04-824d-3f63d77b9d8b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "中通快递支持上门送货服务吗", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d4d2008b-b915-4b38-9e7a-c8f42607f727", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "卡片过了有效期还能用", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d50bb2f5-5214-4d82-927d-e6a2c78fcf71", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "你们中通快递送单都是不送上门的吗？", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d5819a6c-a4a2-4cff-a39c-02a89c5dccb2", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的订单海是123456789123，我要催快递", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d64deccd-7a93-4f9f-9266-3402b2a2ad6f", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "想问一下信用卡我要开卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d8f2d8cc-3982-44f0-9c24-112c51603d3a", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "忘了银行卡号怎么查询", "intent_id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d9ea806c-2c4b-425e-8bc6-b29b3bd73c16", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我有什么方法可以查到我的额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "d9fcff95-bed4-4a5b-83d8-b40229824ae6", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调额调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "dadb23cf-44fd-4b21-afed-4cd582739300", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "支持上门服务？？", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "db55425c-8743-4f75-9921-c5ee9a61a5f5", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "你们可以帮我查询我的借款额度吗", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "dc33c8d1-c350-44d9-9c93-1530fe3069f0", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "快递催收", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "de895b4f-abb5-4c28-9492-77eb30a9e7e9", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "调整信用度调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "defa26bb-00a7-4878-b882-bcba6a916552", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "能不能给我查下快递", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "e100c824-24ed-4eb0-8335-002793aee379", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查一查我想要办理信用卡分期", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "e41a22df-35a0-4c86-9968-199c9b3ce923", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "取消固定调整额度", "intent_id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "md5": null, "updated": "2020-09-23 16:27:57", "id": "e43dacf9-6f65-4d77-8bfe-498b4ca6d10b", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的运单号是676767676767，我要催下我的快递", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "e4b57629-ed21-436a-b9d1-bd54aee5e703", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "信用我要把信用卡分期怎么申请", "intent_id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "e8495b1a-dfd5-4750-9073-d14f9c83f148", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "转接人工", "intent_id": "sys_intent_7", "md5": null, "updated": "2020-09-23 16:27:57", "id": "eb578cc5-72f9-4f1f-8e8d-8ea27bccc02d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "开一下卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ec08dcfc-9d25-4ddb-b558-fd478b310cae", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "请问那个快递怎么还没送来", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ed7b2c69-f7a3-4c70-9c7e-ff43cd27fe17", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查询物流信息，但是不知道在哪里查", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ee227fcd-4317-4876-9603-2a8aefe0da71", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查下物流信息", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ee87065d-a29f-4bef-8f83-cefedefcf4c7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "结果激活卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "ef19a888-8b90-42e5-bc35-77478ebc6625", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我支付激活卡片", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f007c607-4245-41cb-aac9-2d128f9b8bc7", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "能提供一下查运费的途径吗", "intent_id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f0b740db-5df3-445f-a459-92bdcd660ef4", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "银行卡给我激活", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f1cdd92b-5f5e-4e2c-9558-7e02f465e4e0", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "开卡手机号卡", "intent_id": "621d9f25-a6dd-4009-a373-37b0da99a003", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f1ee0e8a-6067-4cc5-b06c-eb998dc3b335", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "多次反馈不按地址派送", "intent_id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f1f8645d-ad77-4efa-afb8-011a0181f14d", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我想查一下我欠款的额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f5681fff-9336-4ea2-94bc-5c65780ba978", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "查询跨地", "intent_id": "341edf36-f99b-4799-a1cc-d977c92448c5", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f59130ff-e432-4f88-a750-daf1e1b49884", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "在哪里可以查到我还可以借多少钱？", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f6c164a8-d2cf-4f47-8c44-49f41a32d990", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我要催收快递", "intent_id": "c5e23910-787f-4044-8904-36f25a30924c", "md5": null, "updated": "2020-09-23 16:27:57", "id": "f844f358-5ae4-433d-bd98-53764069672c", "version": 0}, {"created": "2020-09-23 16:27:57", "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "value": "我的贷款可以通过哪里来查询额度", "intent_id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "md5": null, "updated": "2020-09-23 16:27:57", "id": "fe1c93f7-72f7-44cc-a8c5-350b9d8cda79", "version": 0}], "USER_INTENT": [{"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "办理分期", "updated": **********, "id": "5c3ffd3d-9af1-4249-bbcf-04bc4600ca8c", "created_user_id": null, "version": null, "alias": "", "description": "账单", "last_edit_user_id": null, "name": "Stages_by_stages", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "办理开卡", "updated": **********, "id": "621d9f25-a6dd-4009-a373-37b0da99a003", "created_user_id": null, "version": null, "alias": "", "description": "激活", "last_edit_user_id": null, "name": "Handle_card_opening", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "不送货上门", "updated": **********, "id": "7e741c57-fc4c-4a8f-b461-3e8210f51981", "created_user_id": null, "version": null, "alias": "", "description": "", "last_edit_user_id": null, "name": "no_delivery", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "查询贷款额度", "updated": **********, "id": "6660793c-6579-49ee-a161-2e46d22fcb4d", "created_user_id": null, "version": null, "alias": "", "description": "额度", "last_edit_user_id": null, "name": "query_loan_limit", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "查运费", "updated": **********, "id": "938c2e0d-c618-4d5d-8e2b-6a6b8b7b64a1", "created_user_id": null, "version": null, "alias": "", "description": "", "last_edit_user_id": null, "name": "check_the_freight", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "催收快递", "updated": **********, "id": "c5e23910-787f-4044-8904-36f25a30924c", "created_user_id": null, "version": null, "alias": "", "description": "", "last_edit_user_id": null, "name": "rush_delivery", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "调整额度", "updated": **********, "id": "eb5a3703-1b19-4f08-ba55-c80a413e98c0", "created_user_id": null, "version": null, "alias": "", "description": "", "last_edit_user_id": null, "name": "Adjustment_limit", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "结束", "updated": **********, "id": "7caa6cae-5796-4bae-9d0f-673f420ba8cb", "created_user_id": null, "version": null, "alias": "", "description": "", "last_edit_user_id": null, "name": "jiesu", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "全局判断转人工", "updated": **********, "id": "e1375bf5-4daa-4fca-ba97-41a7a69f28a8", "created_user_id": null, "version": null, "alias": "", "description": "", "last_edit_user_id": null, "name": "quanjupanduan", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "物流信息查询", "updated": **********, "id": "341edf36-f99b-4799-a1cc-d977c92448c5", "created_user_id": null, "version": null, "alias": "", "description": "", "last_edit_user_id": null, "name": "logistics_informatio", "last_edit_user_name": null}, {"created": **********, "agent_id": "e9f05e30-cab5-4585-bc25-aab2729fe082", "created_user_name": null, "name_zh": "账户查询", "updated": **********, "id": "45b5ac51-58b2-4a55-919d-c05afdd0b4b5", "created_user_id": null, "version": null, "alias": "", "description": "卡号", "last_edit_user_id": null, "name": "Account_query", "last_edit_user_name": null}], "SYS_INTENT": [{"id": "sys_intent_9"}, {"id": "sys_intent_6"}, {"id": "sys_intent_7"}, {"id": "sys_intent_12"}, {"id": "sys_intent_13"}, {"id": "sys_intent_8"}, {"id": "sys_intent_14"}, {"id": "sys_intent_10"}, {"id": "sys_intent_4"}, {"id": "sys_intent_11"}, {"id": "sys_intent_5"}], "TEMPLATE": [], "NO_RESP": []}}, "version": "v62-upgrade", "entity": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "entity": []}}}