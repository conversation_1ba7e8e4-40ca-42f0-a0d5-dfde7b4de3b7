# -*- coding: utf-8 -*-
"""
@author:da<PERSON><PERSON><PERSON><PERSON>
专业版端到端语义理解准确率效果测评
"""
import numpy as np
import requests
import json
import time
import openpyxl
import re
import argparse

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('userName', help='租户名')
parser.add_argument('agentId', help='AI客服id')
parser.add_argument('token', help='AI客服token')
parser.add_argument('host', help='域名')
parser.add_argument('typeHost', help='offline or online')
parser.add_argument('file_name', help='测试集表格名')
parser.add_argument('sheet_name', help='测试集sheet名')
parser.add_argument('compare_words_value_threshold', help='文本相似度阈值')

# 解析命令行参数
args = parser.parse_args()

base_info = {
    args.userName: {
        "agentId": args.agentId,
        "token": args.token
    }
}

offline_url = "{}/core/v5/stream/query".format(args.host)  # 流式
online_url = "{}/online/core/v5/stream/query".format(args.host)  # 流式

if args.typeHost == 'offline':
    env_array = [offline_url]
elif args.typeHost == 'online':
    env_array = [online_url]
else:
    env_array = [offline_url, online_url]

# 控制qps
sleep_time = 5

# 文本相似度阈值
compare_words_value_threshold = args.compare_words_value_threshold


def main():
    """
    主函数
    :return:
    """
    # 选择要测试的租户
    user_name = args.userName  # eb4.0
    print("====== 开始测试租户【{}】下的【语义理解准确率】corequery ======".format(user_name))

    # 读取测试表格数据
    file_name = "./{}".format(args.file_name)

    print("运行的测试集文件{}".format(file_name))

    # 表格中sheet名
    sheet_names = [
        args.sheet_name
    ]

    # 读取并解析存储query的表格数据
    tables = parse_excel(file_name, sheet_names)

    for host_url in env_array:
        for sheet_name in sheet_names:
            print("开始测试表格：【{}】sheet：【{}】的每条query是否有正常回复".format(file_name, sheet_name))
            # session query
            all_result_array = query_control(
                host_url,
                tables[sheet_name],
                user_name=user_name
            )

            # 生成html表格
            html_content = generate_html_table(
                all_result_array,
                user_name=user_name,
                agent_id=base_info[user_name]["agentId"],
                file_name=file_name,
                sheet_name=sheet_name,
                host_url=host_url
            )
            # 将HTML代码写入文件
            try:
                if "online" in host_url:
                    file_name = f"{sheet_name}-online-report.html"
                    with open(file_name, "w", encoding='utf-8') as f:
                        f.write(html_content)
                else:
                    file_name = f"{sheet_name}-offline-report.html"
                    with open(file_name, "w", encoding='utf-8') as f:
                        f.write(html_content)
            except Exception as e:
                print(e)
                print("保存html文件失败，html源代码如下：")
                print(html_content)
            time.sleep(5)

    print(str("====== 结束测试租户【{}】下【语义理解准确率】corequery ======".format(user_name)))


def my_request_post(session, host_url, params, json_data, headers):
    """
    自定义封装请求(requests)函数
    """
    i = 0
    retry_num = 10
    res = None
    while i < retry_num:
        try:
            res = session.post(url=host_url, params=params, json=json_data, headers=headers, stream=True, timeout=180)
            break
        except Exception as e:
            print(e)
            i += 1
            print("请求失败，重试第{}次".format(i))
            time.sleep(5)
    return res


def start_query(session, host_url, user_name):
    """
    启动query，获取sessionId
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'Token': token
    }
    params = {
        "Token": token
    }
    json_data = {
        "channel": "_sys_web",
        "queryText": "",
        "agentId": agentId,
        "collect": True,
        "variables": {}
    }
    http_data = b''
    http_post = my_request_post(session, host_url, params, json_data, headers)
    for chunk in http_post.iter_content(chunk_size=1024):
        if chunk:
            http_data += chunk
    http_response = http_data.decode('utf-8')
    sessionId = None
    try:
        res_text = json.loads(re.findall(r'data:(.*)', http_response)[0])
        sessionId = res_text['sessionId']
    except Exception as e:
        print(e)
        print("获取sessionId失败，原始的response的json数据为：{}".format(http_response))
    return sessionId


def core_query(session, access_token, session_id, host_url, session_array,
               query_array, answer_array, user_name):
    """
    核心corequery
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'Token': token
    }
    params = {
        "Token": token
    }

    # 存储当前session所有query及answer
    session_result_array = []
    # 一个session有一个或多个query
    for i in range(len(query_array)):
        session_num = session_array[i]
        query = query_array[i]
        if query is None:
            break
        excepted_answer = answer_array[i]

        json_data = {
            "channel": "_sys_web",
            "queryText": query,
            "collect": True,
            "sessionId": session_id,
            "agentId": agentId,
            "variables": {}
        }
        # 缓存返回的数据
        http_data = b''
        # 重试次数
        retry = 0
        retry_num = 3
        while retry < retry_num:
            try:
                http_post = my_request_post(session, host_url, params, json_data, headers)
                for chunk in http_post.iter_content(chunk_size=1024):
                    if chunk:
                        http_data += chunk
                break
            except Exception as e:
                print(e)
                retry += 1
                time.sleep(1)

        http_response = http_data.decode('utf-8')
        all_stream_chunk_response_json = re.findall(r'data:(.*)', http_response)
        actual_answer = ""
        reply_source = ""
        for chunk_json_str in all_stream_chunk_response_json:
            try:
                chunk_json = json.loads(chunk_json_str)
                chunk_reply = chunk_json.get('answer')[0].get("reply", None)
                # 拼接流式trunk的answer
                if chunk_reply is not None:
                    actual_answer += chunk_reply.get("text", "")

                # 获取回复来源
                if ("" == reply_source) and (chunk_reply is not None):
                    reply_source = chunk_reply.get("replySource", "")
            except Exception as e:
                print(e)
                print("获取reply_answer_text失败，原始的response的json数据为：{}".format(chunk_json_str))

        # 对答案进行打分
        # 获取两个词向量的相似度
        retry_num = 0
        compare_words_value = 0.0
        while retry_num < 10:
            try:
                compare_words_value = getEmbeddingsBetweenTwoWords(access_token, excepted_answer, actual_answer)
                break
            except Exception as e:
                print(e)
                retry_num += 1
                print(f"打分-尝试重新获取compare_words_value {retry_num}次")

        # 组装query的相关信息
        print("\n============================================================")
        session_result_dict = {
            "session_id": session_id,
            "session_num": session_num,
            "query": query,
            "excepted_answer": excepted_answer,
            "actual_answer": actual_answer,
            "compare_words_value": compare_words_value,
            "reply_source": reply_source
        }
        print(session_result_dict)

        time.sleep(sleep_time)
        session_result_array.append(session_result_dict)

    return session_result_array


def parse_excel(excel_name, sheet_names):
    """
    解析excel表格数据，存储为字典+数组
    :param excel_name: excel文件名
    :param sheet_names: sheet名
    :return: tables对象，key为sheet名，value为表格数据
    """
    workbook = openpyxl.load_workbook(excel_name)
    tables = {}
    # 通过工作表名称选择当前活动的工作表
    for sheet_name in sheet_names:
        sheet = workbook[sheet_name]
        table = {}
        for row_index in range(1, sheet.max_row + 1):
            # 拿到表头数据名
            if row_index == 1:
                for col_index in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=1, column=col_index)
                    table[cell.value] = []
                continue
            # 存储第个表头下的所有数据，以表头为key，数据为value数组
            for col_index in range(1, sheet.max_column + 1):
                cell = sheet.cell(row=row_index, column=col_index)
                table[sheet.cell(row=1, column=col_index).value].append(cell.value)

        tables[sheet_name] = table
    return tables


def query_control(host_url, table, user_name):
    """
    根据表格内容，控制query流程，统计每个session中的query的情况，再进行query测试
    :param table: excel名
    :param user_name: 租户名
    :return: all_cost_times 所有query的应答时间
    """
    all_result_array = []
    row_length = len(table['session'])
    # 访问文心的 token，用于调用接口，获取两个词向量的相似度
    access_token = getAccessToken()

    cur_row_index = 0
    while (cur_row_index < row_length):
        # 统计当前session序号中有多少个query，并把query存入数组中
        last_row_index = cur_row_index
        # 当前session序号
        cur_session_num = table['session'][cur_row_index]
        # 越界检查，下一行的session序号
        if cur_row_index + 1 >= row_length:
            # 如果最后一行的session只有1个query
            cur_session_query_num = 1
            # 用于外围循环判断，防止死循环
            cur_row_index += 1
        else:
            # 下一行的session序号
            cur_row_index += 1
            next_session_num = table['session'][cur_row_index]
            # 如果下一个session序号与当前session序号相同，则认为当前session中query个数为1
            while cur_session_num == next_session_num and cur_row_index < row_length:
                cur_row_index += 1
                # 越界检查，最后一个session中有多个query时
                if cur_row_index >= row_length:
                    break
                next_session_num = table['session'][cur_row_index]
            # 当前session中query个数
            cur_session_query_num = cur_row_index - last_row_index

        session_array = []
        query_array = []
        expected_answer_array = []
        for i in range(0, cur_session_query_num):
            session_array.append(table['session'][last_row_index + i])
            query_array.append(table['query'][last_row_index + i])
            expected_answer_array.append(table['expected_answer'][last_row_index + i])

        # 每个session开始
        session = requests.Session()
        # 发送一个空query
        session_id = start_query(session, host_url, user_name)
        session_result_array = []
        if session_id is None:
            # 如果start_query失败，则跳过当前session，继续下一个session
            print("发送空query获取sessionId失败")
            # 保留源query，但实际answer为空
            for i in range(cur_session_query_num):
                print("\n============================================================")
                fake_session_result = {
                    "session_id": session_id,
                    "session_num": session_array[i],
                    "query": query_array[i],
                    "excepted_answer": query_array[i],
                    "actual_answer": "",
                    "compare_words_value": 0,
                    "reply_source": ""
                }
                print(fake_session_result)
                session_result_array.append(fake_session_result)
        else:
            # 获取sessionId之后，再发送后续的query
            session_result_array = core_query(
                session,
                access_token,
                session_id,
                host_url,
                session_array,
                query_array,
                expected_answer_array,
                user_name=user_name
            )

        # 每个session结束后，保存结果
        all_result_array.append(session_result_array)
        # 关闭session
        try:
            session.close()
        except Exception as e:
            print(e)
            print("session close error")
        # 等待1秒
        time.sleep(5)

    return all_result_array


def getAccessToken():
    """
    通过ak、sk获取文心token
    :return: access_token
    """
    url = "https://aip.baidubce.com/oauth/2.0/token?" \
          "grant_type=client_credentials&client_id=hnX8luym9a5HWgLzTqCYXfAg&client_secret" \
          "=gVwfBpGWIp37fkKxkiZcYj4IbGqhIuMg"

    response = requests.request("GET", url, headers="", data="")
    response = response.json()
    return response['access_token']


def getEmbeddingsBetweenTwoWords(access_token, first, second):
    """
    获取两个query之间的向量
    :param access_token: api token
    :param first: 第一个query
    :param second: 第二个query
    :return:
    """
    url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/embedding-v1?" \
          "access_token={}".format(access_token)

    payload = json.dumps({
        "input": [
            first,
            second
        ]
    })
    headers = {
        'Content-Type': 'application/json'
    }
    json_data = requests.request("POST", url, headers=headers, data=payload)
    json_data = json_data.json()

    # print("获取向量结果{}".format(json_data))

    # 提取向量 A 和 B
    A = np.array(json_data["data"][0]["embedding"])
    B = np.array(json_data["data"][1]["embedding"])

    # 计算向量 A 和 B 的点积
    dot_product = np.dot(A, B)

    # 计算向量 A 和 B 的模
    magnitude_A = np.linalg.norm(A)
    magnitude_B = np.linalg.norm(B)

    # 计算夹角的余弦值
    cos_theta = dot_product / (magnitude_A * magnitude_B)

    # 计算夹角（弧度）
    theta_radians = np.arccos(cos_theta)

    # 将夹角转换为角度
    theta_degrees = np.degrees(theta_radians)

    # 打印结果
    # print("向量 A 和 B 的cos_theta ：", cos_theta)
    return cos_theta


def generate_html_table(all_result_array, user_name, agent_id, file_name, sheet_name, host_url):
    """
    生成htlm表格
    :return:
    """
    html = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\">" \
           "<title>客悦专业版端到端【语义理解准确率】回复可用度效果报告</title></head>"
    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0'><tr>\
                    <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
                    <strong>客悦专业版端到端【语义理解准确率】回复可用度效果报告，租户名：【{user_name}】，AI客服id：【{agent_id}】<br>\
                    测试集excel名【{file_name}】，sheet名【{sheet_name}】，域名：【{host_url}】<br>\
                    机器计算预期答案与实际答案相似度阈值为【{compare_words_value_threshold}】/strong><br>\
                点击右侧按钮后，可以在 [全部显示]/[只显示打分为0的内容] 两种方式中进行切换\
                <button id='toggleButton' style='color: blue'>切换显示</button></td></tr>"

    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0' id='detail'>\
                    <thead><tr bgcolor='yellow'>\
                    <td width='auto' align='center'><strong>session_id</strong></td>\
                    <td width='auto' align='center'><strong>query</strong></td>\
                    <td width='auto' align='center'><strong>预期回复</strong></td>\
                    <td width='auto' align='center'><strong>实际回复</strong></td>\
                    <td width='auto' align='center'><strong>答案相似度</strong></td>\
                    <td width='auto' align='center'><strong>打分</strong></td>\
                    <td width='auto' align='center'><strong>回复来源</strong></td>\
                    </tr></thead>"

    html += "<tbody>"
    total_1_score = 0
    total_score = 0
    for session in all_result_array:
        session_size = len(session)
        for index in range(0, session_size):
            query_info = session[index]

            # 高亮低相似度的query
            if query_info.get('compare_words_value') >= float(compare_words_value_threshold):
                html += "<tr>"
            else:
                html += "<tr style='color:red;font-weight:bold'>"

            # 展示session_id
            col1_text = "第【{}】个session，session_id:【{}】".format(
                session[0].get('session_num'),
                query_info.get('session_id')
            )
            html += f"<td>{col1_text}</td>"
            # 展示用户query
            html += f"<td>{query_info.get('query')}</td>"
            # 展示预期回复
            html += f"<td>{query_info.get('excepted_answer')}</td>"
            # 展示实际回复
            html += f"<td>{query_info.get('actual_answer')}</td>"
            # 展示答案相似度
            html += "<td>{:,.4f}</td>".format(query_info.get('compare_words_value'))
            # 展示打分
            if query_info.get('compare_words_value') >= float(compare_words_value_threshold):
                html += f"<td>1</td>"
                total_1_score += 1
            else:
                html += f"<td>0</td>"
            total_score += 1
            # 展示回复来源
            html += f"<td>回复来源：<br>{query_info.get('reply_source')}</td>"

            html += "</tr>"

    html += f"<tr><td colspan='8' align='center'>总计：{total_score}条query，回复可用度（得1分的数量/总数）：{total_1_score} / " \
            f"{total_score} = {round((total_1_score / total_score), 2) * 100}%</td></tr>"

    html += "</tbody></table>"
    # 添加一个按钮，点击后可以显示/隐藏所有识别错误的行，增加对应的js代码
    html += """<script>  
            let isFiltered = false;  

            function toggleTableRows() {  
                const rows = document.querySelectorAll("#detail tbody tr");  
                rows.forEach(row => {  
                    if (isFiltered) {  
                        // 如果当前是在“隐藏”模式，显示所有行  
                        row.style.display = "";  
                    } else {  
                        // 如果当前是在“显示”模式，只显示带样式的行  
                        if (row.getAttribute("style") === "color:red;font-weight:bold") {  
                            row.style.display = "";  
                        } else {  
                            row.style.display = "none";  
                        }  
                    }  
                });  
                isFiltered = !isFiltered; // 切换状态  
            }  

            document.getElementById("toggleButton").addEventListener("click", toggleTableRows);  
        </script>"""
    html += "</body></html>"
    return html


if __name__ == '__main__':
    main()
