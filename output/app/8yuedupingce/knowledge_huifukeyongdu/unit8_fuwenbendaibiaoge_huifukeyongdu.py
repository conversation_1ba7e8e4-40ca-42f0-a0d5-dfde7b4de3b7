# -*- coding: utf-8 -*-
"""
@author:da<PERSON><PERSON><PERSON><PERSON>
专业版端到端知识库-富文本带表格回复可用度效果测评
"""
import os
import requests
import json
import time
import openpyxl
import re
import argparse

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('userName', help='租户名')
parser.add_argument('agentId', help='AI客服id')
parser.add_argument('token', help='AI客服token')
parser.add_argument('host', help='域名')
parser.add_argument('typeHost', help='offline or online')
parser.add_argument('file_name', help='测试集表格名')
parser.add_argument('sheet_name', help='测试集sheet名')
parser.add_argument('api_key', help='千帆大模型打分的api_key')
parser.add_argument('secret_key', help='千帆大模型打分的secret_key')

# 解析命令行参数
args = parser.parse_args()

base_info = {
    args.userName: {
        "agentId": args.agentId,
        "token": args.token
    }
}

offline_url = "{}/core/v5/stream/query".format(args.host)  # 流式
online_url = "{}/online/core/v5/stream/query".format(args.host)  # 流式

if args.typeHost == 'offline':
    env_array = [offline_url]
elif args.typeHost == 'online':
    env_array = [online_url]
else:
    env_array = [offline_url, online_url]

api_key = args.api_key
secret_key = args.secret_key


class RequestLargeModelObj:
    """
    声明请求大模型打分的类
    """
    api_key = None
    secret_key = None
    access_token = None

    def __init__(self):
        # 初始化 api_key
        self.api_key = api_key
        # 初始化 secret_key
        self.secret_key = secret_key
        # 初始化 access_token
        self.get_access_token()

    def get_access_token(self):
        """
        更新 Access Token
        """
        url = f"https://aip.baidubce.com/oauth/2.0/token?client_id={self.api_key}&"\
              f"client_secret={self.secret_key}&grant_type=client_credentials"

        payload = json.dumps("")
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        result = response.json()

        if 'access_token' in result:
            self.access_token = result['access_token']
            print(f"Successfully fetched access token：{self.access_token}")
        else:
            print(f"Failed to fetch access token")

    def get_large_model_analysis_result(self, query, expected_answer, actual_answer):
        """
        根据prompt，请求大模型，获得大模型的回答
        :param query: 用户query
        :param expected_answer: 期望答案
        :param actual_answer: 实际答案
        :return: 大模型打分的结果、打分（0，1，2）、打分原因
        """
        prompt = "你是专业的评测人员，请根据问题和参考答案对模型输出进行准确性进行打分，分档0、1、2，" \
                 "具体的打分标准请参照分档描述和注意事项，此外你还需要提供打分和对应的评分依据，你的回答请按照\n" \
                 "【得分】xxx\n" \
                 "【原因】xxx\n" \
                 "的格式输出，注意【得分】xxx和【原因】xxx之间要进行换行，像上面已经加入的换行符一样。" \
                 "下面给出分档描述、问题、参考答案、模型输出以及注意事项\n" \
                 "【分档描述】\n" \
                 "0: 模型输出与参考答案语义和相关数据信息完全不符合\n" \
                 "1: 模型输出与参考答案语义和相关数据信息部分符合，但是允许存在不完整、冗余或者部分错误的情况\n" \
                 "2: 模型输出与参考答案语义和相关数据信息完全符合\n" \
                 "【注意事项】\n" \
                 "1.如果模型输出有关于来源的描述，例如如果模型中有'答案由一言生成'、'来源xxx'等内容，请将内容进行忽略。下面是真实的场景：\n" \
                 "【用户问题】{}\n" \
                 "【参考答案】{}\n" \
                 "【模型输出】{}\n".format(query, expected_answer, actual_answer)
        url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro" \
              "?access_token={}".format(self.access_token)
        payload = json.dumps({
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        })
        headers = {
            'Content-Type': 'application/json',
            'Cookie': 'BAIDUID=DD6A3F09483D75037865C5797A29D70B:FG=1; BAIDUID=DD6A3F09483D75037865C5797A29D70B:FG=1'
        }
        try:
            response = requests.request("POST", url, headers=headers, data=payload)
            print("\n============================================================")
            print(response.text)
            if response.status_code == 200:
                res_text = response.json()["result"]
                # 正则匹配提取打分和原因
                default_pattern = "【得分】([012])\\n【原因】(.*)"
                score = re.findall(default_pattern, res_text)[0][0].strip()
                reason = re.findall(default_pattern, res_text)[0][1].strip()
                return res_text, score, reason
            else:
                print('请求大模型打分的响应状态码不对，实际为：{}\n'.format(response.status_code))
        except Exception as e:
            print('请求大模型打分异常：{}'.format(e))
            return None, None, None


# 实例化大模型打分的类
large_model_obj = RequestLargeModelObj()


def main():
    """
    主函数
    :return:
    """
    # 选择要测试的租户
    user_name = args.userName  # eb4.0
    print("====== 开始测试租户【{}】下的【知识库】corequery ======".format(user_name))

    # 读取测试表格数据
    file_name = "./{}".format(args.file_name)

    print("运行的测试集文件{}".format(file_name))

    # 表格中sheet名
    sheet_names = [
        args.sheet_name
    ]

    # 读取并解析存储query的表格数据
    tables = parse_excel(file_name, sheet_names)

    for host_url in env_array:
        for sheet_name in sheet_names:
            print("开始测试表格：【{}】sheet：【{}】的每条query是否有正常回复".format(file_name, sheet_name))
            # session query
            all_result_array = query_control(
                host_url,
                tables[sheet_name],
                user_name=user_name
            )

            # 生成html表格
            html_content = generate_html_table(
                all_result_array,
                user_name=user_name,
                agent_id=base_info[user_name]["agentId"],
                file_name=file_name,
                sheet_name=sheet_name,
                host_url=host_url
            )
            # 将HTML代码写入文件
            try:
                if "online" in host_url:
                    file_name = "{}-online-report.html".format(sheet_name)
                    with open(file_name, "w", encoding='utf-8') as f:
                        f.write(html_content)
                else:
                    file_name = "{}-offline-report.html".format(sheet_name)
                    with open(file_name, "w", encoding='utf-8') as f:
                        f.write(html_content)
            except Exception as e:
                print(e)
                print("保存html文件失败，html源代码如下：")
                print(html_content)
            time.sleep(1)

    print(str("====== 结束测试租户【{}】下【知识库】corequery ======".format(user_name)))


def my_request_post(session, host_url, params, json_data, headers):
    """
    自定义封装请求(requests)函数
    """
    i = 0
    retry_num = 10
    res = None
    while i < retry_num:
        try:
            res = session.post(url=host_url, params=params, json=json_data, headers=headers, stream=True, timeout=180)
            break
        except Exception as e:
            print(e)
            i += 1
            print("请求失败，重试第{}次".format(i))
            time.sleep(1)
    return res


def start_query(session, host_url, user_name):
    """
    启动query，获取sessionId
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'Token': token
    }
    params = {
        "Token": token
    }
    json_data = {
        "channel": "_sys_web",
        "queryText": "",
        "agentId": agentId,
        "collect": True,
        "variables": {}
    }
    http_data = b''
    http_post = my_request_post(session, host_url, params, json_data, headers)
    for chunk in http_post.iter_content(chunk_size=1024):
        if chunk:
            http_data += chunk
    http_response = http_data.decode('utf-8')
    sessionId = None
    try:
        res_text = json.loads(re.findall(r'data:(.*)', http_response)[0])
        sessionId = res_text['sessionId']
    except Exception as e:
        print(e)
        print("获取sessionId失败，原始的response的json数据为：{}".format(http_response))
    return sessionId


def core_query(session, session_id, host_url, session_array,
               query_array, answer_array, image_array, user_name):
    """
    核心corequery
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'Token': token
    }
    params = {
        "Token": token
    }

    # 存储当前session所有query及answer
    session_result_array = []
    # 一个session有一个或多个query
    for i in range(len(query_array)):
        session_num = session_array[i]
        query = query_array[i]
        if query is None:
            break
        expected_answer = answer_array[i]
        expected_image = image_array[i]
        json_data = {
            "channel": "_sys_web",
            "queryText": query,
            "collect": True,
            "sessionId": session_id,
            "agentId": agentId,
            "variables": {}
        }
        # 缓存返回的数据
        http_data = b''
        # 重试次数
        retry = 0
        retry_num = 3
        while retry < retry_num:
            try:
                http_post = my_request_post(session, host_url, params, json_data, headers)
                for chunk in http_post.iter_content(chunk_size=1024):
                    if chunk:
                        http_data += chunk
                break
            except Exception as e:
                print(e)
                retry += 1
                time.sleep(1)

        http_response = http_data.decode('utf-8')
        all_stream_chunk_response_json = re.findall(r'data:(.*)', http_response)
        actual_answer = ""
        reply_source = ""
        for chunk_json_str in all_stream_chunk_response_json:
            try:
                chunk_json = json.loads(chunk_json_str)
                chunk_reply = chunk_json.get('answer')[0].get("reply", None)
                # 拼接流式trunk的answer
                if chunk_reply is not None:
                    if "/api/v2/file" in chunk_reply['text']:
                        actual_answer += chunk_reply['text'].replace("/api/v2/file", "{}/api/v2/file".format(args.host))
                    else:
                        actual_answer += chunk_reply.get("text", "")

                # 获取回复来源
                if ("" == reply_source) and (chunk_reply is not None):
                    reply_source = chunk_reply.get("replySource", "")
            except Exception as e:
                print(e)
                print("获取reply_answer_text失败，原始的response的json数据为：{}".format(chunk_json_str))

        # 对答案进行打分
        # 获取两个词向量的相似度
        retry_num = 0
        res_text, score, reason = None, 0, None
        while retry_num < 10:
            try:
                res_text, score, reason = large_model_obj.get_large_model_analysis_result(
                    query, expected_answer, actual_answer)
                break
            except Exception as e:
                print(e)
                retry_num += 1
                print(f"打分-尝试重新获取compare_words_value {retry_num}次")

        # 组装query的相关信息
        session_result_dict = {
            "session_id": session_id,
            "session_num": session_num,
            "query": query,
            "expected_answer": expected_answer,
            "expected_image": expected_image,
            "actual_answer": actual_answer,
            "score": score,
            "reason": reason,
            "reply_source": reply_source
        }
        print(session_result_dict)

        time.sleep(1)
        session_result_array.append(session_result_dict)

    return session_result_array


def parse_excel(excel_name, sheet_names):
    """
    解析excel表格数据，存储为字典+数组
    :param excel_name: excel文件名
    :param sheet_names: sheet名
    :return: tables对象，key为sheet名，value为表格数据
    """
    workbook = openpyxl.load_workbook(excel_name)
    tables = {}
    # 通过工作表名称选择当前活动的工作表
    for sheet_name in sheet_names:
        sheet = workbook[sheet_name]
        table = {}
        for row_index in range(1, sheet.max_row + 1):
            # 拿到表头数据名
            if row_index == 1:
                for col_index in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=1, column=col_index)
                    table[cell.value] = []
                continue
            # 存储第个表头下的所有数据，以表头为key，数据为value数组
            for col_index in range(1, sheet.max_column + 1):
                cell = sheet.cell(row=row_index, column=col_index)
                table[sheet.cell(row=1, column=col_index).value].append(cell.value)

        tables[sheet_name] = table
    return tables


def query_control(host_url, table, user_name):
    """
    根据表格内容，控制query流程，统计每个session中的query的情况，再进行query测试
    :param table: excel名
    :param user_name: 租户名
    :return: all_cost_times 所有query的应答时间
    """
    all_result_array = []
    row_length = len(table['session'])

    cur_row_index = 0
    while (cur_row_index < row_length):
        # 统计当前session序号中有多少个query，并把query存入数组中
        last_row_index = cur_row_index
        # 当前session序号
        cur_session_num = table['session'][cur_row_index]
        # 越界检查，下一行的session序号
        if cur_row_index + 1 >= row_length:
            # 如果最后一行的session只有1个query
            cur_session_query_num = 1
            # 用于外围循环判断，防止死循环
            cur_row_index += 1
        else:
            # 下一行的session序号
            cur_row_index += 1
            next_session_num = table['session'][cur_row_index]
            # 如果下一个session序号与当前session序号相同，则认为当前session中query个数为1
            while cur_session_num == next_session_num and cur_row_index < row_length:
                cur_row_index += 1
                # 越界检查，最后一个session中有多个query时
                if cur_row_index >= row_length:
                    break
                next_session_num = table['session'][cur_row_index]
            # 当前session中query个数
            cur_session_query_num = cur_row_index - last_row_index

        session_array = []
        query_array = []
        expected_answer_array = []
        expected_image_array = []
        for i in range(0, cur_session_query_num):
            session_array.append(table['session'][last_row_index + i])
            query_array.append(table['query'][last_row_index + i])
            expected_answer_array.append(table['expected_answer'][last_row_index + i])
            expected_image_array.append(table['expected_image'][last_row_index + i])

        # 每个session开始
        session = requests.Session()
        # 发送一个空query
        session_id = start_query(session, host_url, user_name)
        session_result_array = []
        if session_id is None:
            # 如果start_query失败，则跳过当前session，继续下一个session
            print("发送空query获取sessionId失败")
            # 保留源query，但实际answer为空
            for i in range(cur_session_query_num):
                print("\n============================================================")
                fake_session_result = {
                    "session_id": session_id,
                    "session_num": session_array[i],
                    "query": query_array[i],
                    "expected_answer": query_array[i],
                    "actual_answer": "",
                    "compare_words_value": 0,
                    "reply_source": ""
                }
                print(fake_session_result)
                session_result_array.append(fake_session_result)
        else:
            # 获取sessionId之后，再发送后续的query
            session_result_array = core_query(session, session_id, host_url, session_array, query_array,
                                              expected_answer_array, expected_image_array, user_name=user_name)

        # 每个session结束后，保存结果
        all_result_array.append(session_result_array)
        # 关闭session
        try:
            session.close()
        except Exception as e:
            print(e)
            print("session close error")
        # 等待1秒
        time.sleep(1)

    return all_result_array


def generate_html_table(all_result_array, user_name, agent_id, file_name, sheet_name, host_url):
    """
    生成htlm表格
    :return:
    """
    html = "<!DOCTYPE html><html lang=\"en\">"
    html += "<head>" \
            "<meta charset=\"UTF-8\">" \
            "<script src=\"https://cdn.jsdelivr.net/npm/marked/marked.min.js\"></script>" \
            "<title>客悦专业版端到端【知识库】回复可用度效果报告</title>" \
            "</head>"
    html += f"<div style='text-align: center'>\
            <strong>端到端【知识库-富文本带表格】回复可用度效果报告，租户名：【{user_name}】，AI客服agent id：【{agent_id}】<br>\
            测试集excel名【{file_name}】，sheet名【{sheet_name}】，域名+接口：【{host_url}】<br>\
            高亮低分的query，0分的红色，1分的橙色。\
            点击右侧按钮后，可以在 [全部显示]/[只显示打分为0、1的内容] 两种方式中进行切换\
            <button id='toggleButton' style='color: blue'>切换显示</button>" \
            "</div>"
    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0' id='detail'>\
                    <thead><tr bgcolor='yellow'>\
                    <td width='200' align='center'><strong>session_id</strong></td>\
                    <td width='200' align='center'><strong>query</strong></td>\
                    <td width='200' align='center'><strong>预期回复</strong></td>\
                    <td width='400' align='center'><strong>预期来源图片</strong></td>\
                    <td width='300' align='center'><strong>实际回复</strong></td>\
                    <td width='100' align='center'><strong>大模型打分结果</strong></td>\
                    <td width='auto' align='center'><strong>大模型打分原因</strong></td>\
                    <td width='100' align='center'><strong>回复来源</strong></td>\
                    </tr></thead>"
    html += "<tbody>"

    # 统计各类得分数量
    total_2_score = 0
    total_1_score = 0
    total_score = 0
    for session in all_result_array:
        session_size = len(session)
        for index in range(0, session_size):
            query_info = session[index]

            # 高亮低分的query，0分的红色，1分的橙色
            if query_info.get('score') is None or query_info.get('score') == '0':
                html += "<tr style='color:red;font-weight:bold'>"
            elif query_info.get('score') == '1':
                html += "<tr style='color:orange;font-weight:bold'>"
                total_1_score += 1
            else:
                total_2_score += 1

            # 展示session_id
            col1_text = f"第【{session[0].get('session_num')}】个session，session_id:【{query_info.get('session_id')}】"
            html += f"<td>{col1_text}</td>"
            # 展示用户query
            html += f"<td>{query_info.get('query')}</td>"
            # 展示预期回复
            html += f"<td>{query_info.get('expected_answer')}</td>"
            # 展示预期回复
            if query_info.get('expected_image') is None or query_info.get('expected_image') == '':
                html += "<td></td>"
            else:
                image_path = "./images/" + query_info.get('expected_image')
                html += f"<td><img src='{image_path}' "\
                        "style='width:100%; height:100%; object-fit:contain; bject-position: center;'/></td>"
            # 展示实际回复
            html += f"<td>{query_info.get('actual_answer')}</td>"
            # 展示大模打分
            html += f"<td>{query_info.get('score')}</td>"
            # 展示大模型打分原因
            html += f"<td>{query_info.get('reason')}</td>"
            # 展示回复来源
            html += f"<td>回复来源：<br>{query_info.get('reply_source')}</td>"
            html += "</tr>"

            # 总query数+1
            total_score += 1

    html += f"<tr><td colspan='8' align='center'>总计：{total_score}条query，得1分数量：{total_1_score}，得2分数量：{total_2_score}" \
            f"，回复可用度（（得1分+得2分的数量）/总数）：{total_1_score + total_2_score} / " \
            f"{total_score} = {round(((total_1_score + total_2_score) / total_score), 2) * 100}%" \
            f"，高可用度（得2分的数量/总数）{total_2_score}/" \
            f"{total_score}={round((total_2_score) / total_score, 2) * 100}%</td></tr>"

    html += "</tbody></table>"
    # 添加一个按钮，点击后可以显示/隐藏所有识别错误的行，增加对应的js代码
    html += """<script>  
            let isFiltered = false;  

            function toggleTableRows() {  
                const rows = document.querySelectorAll("#detail tbody tr");  
                rows.forEach(row => {  
                    if (isFiltered) {  
                        // 如果当前是在“隐藏”模式，显示所有行  
                        row.style.display = "";  
                    } else {  
                        // 如果当前是在“显示”模式，只显示带样式的行  
                        if ((row.getAttribute("style") === "color:red;font-weight:bold") || (row.getAttribute("style") === "color:orange;font-weight:bold")) {    
                            row.style.display = "";  
                        } else {  
                            row.style.display = "none";  
                        }  
                    }  
                });  
                isFiltered = !isFiltered; // 切换状态  
            }  

            document.getElementById("toggleButton").addEventListener("click", toggleTableRows);  
        </script>"""
    # 增加markdown格式的解析显示优化
    html += """<script>  
            window.onload = function() {  
                const options = {  
                    breaks: true, // 支持换行  
                };  
            
                // 只选择 <td> 元素中的 Markdown 内容进行解析  
                document.querySelectorAll('td').forEach(element => {  
                    // 检查内容是否包含 Markdown 格式（如 **, *, #, - 等）  
                    if (/[#*\-]/.test(element.innerText)) {  
                        const htmlContent = marked.parse(element.innerText, options);  
                        element.innerHTML = htmlContent;  
                    }  
                });  
            };  
            </script>
            """
    html += "</body></html>"
    return html


if __name__ == '__main__':
    main()