"""
构造数据，调试代码使用
"""

import numpy as np

all_query_times = [9131.571054458618, 11161.931037902832, 6658.456087112427, 9853.26623916626, 10173.504114151001, 8031.473875045776,
                   10000.61821937561, 7185.431003570557, 21061.485052108765, 9152.092933654785, 10177.581071853638, 7145.150899887085,
                   8347.035884857178, 5937.237024307251, 7111.783981323242, 8215.553998947144, 7083.520174026489, 7833.189010620117,
                   6200.690984725952, 7102.106809616089, 7420.699834823608, 9695.273876190186, 6427.893877029419, 7149.580001831055,
                   10868.63374710083, 6193.103790283203, 7524.3237018585205, 9966.731071472168, 8076.514005661011, 7528.381824493408]
session_info = [
    {'session_num': 1, 'session_id': 'f13f9198-1918-4b5d-976d-999470064eb2', 'cost_times': [1131.571054458618, 11161.931037902832, 6658.456087112427],
     'query_array': ['今天好冷呀', '有衣服吗？', '推荐下冲锋衣吧'], 'answer_texts': [
        '',
        '当然有啦！我们这儿有户外冲锋衣，价格区间从0-1000元不等。你能不能告诉我，你希望购买的冲锋衣大概在哪个价格区间呢？比如0-200元、200-400元这样的。还有，你主要是在什么场景下穿冲锋衣呢？是城市通勤还是山野徒步？这样我可以更好地帮你推荐哦！',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：男士山地徒步防水夹克 | MT 500\n商品价格：799.9\n商品简介：这款防水夹克由我们热爱山地徒步运动的团队设计完成，让您在多风多雨的地方也能获得充分保护。这款防水夹克结合了一种轻便透气的面料和一种结实的面料，让您不惧雨水的威胁。[https://www.decathlon.com.cn/product-detail?dsm_code=339506&model_code=8739514&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%A3%AB%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20%7C%20MT%20500&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=339506&model_code=8739514&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%A3%AB%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20%7C%20MT%20500&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D)\n\n[2] 商品名称：男式山地徒步轻盈防水夹克 MH150\n\n商品价格：349.9\n商品简介：在勃朗峰山脚下，我们的徒步团队设计了这款防水冲锋衣，让你在山区行走时更加安全。生态设计的冲锋衣，由再生的聚酯纤维制成，方便徒步活动时穿着。[https://www.decathlon.com.cn/product-detail?dsm_code=309604&model_code=8796055&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%BC%8F%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E8%BD%BB%E7%9B%88%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20MH150&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=309604&model_code=8796055&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%BC%8F%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E8%BD%BB%E7%9B%88%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20MH150&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！'],
     'query_start_times': ['2024-08-02 14:11:02.392', '2024-08-02 14:11:12.530', '2024-08-02 14:11:24.696'],
     'query_end_times': ['2024-08-02 14:11:11.524', '2024-08-02 14:11:23.692', '2024-08-02 14:11:31.355']},
    {'session_num': 2, 'session_id': '8945cac4-60a4-420a-a872-5bd878d74d4d', 'cost_times': [9853.26623916626, 10173.504114151001, 8031.473875045776],
     'query_array': ['今天走路好累', '有自行车吗', '我想要公路款式的女式自行车，价格在2000左右的'], 'answer_texts': [
        '哎呀，听起来你今天确实需要放松一下呢！😌 是不是考虑来点户外运动，换个心情？比如骑行，感受下风驰电掣的快感~ 我们有好多炫酷的自行车哦！🚴\u200d♂️💨 你想不想了解一下呢？',
        '当然有啦！我们这里有各种款式的自行车呢，比如旅行、公路、山地、女士款式。你对哪种款式比较感兴趣呢？还有，你希望自行车的价格在哪个区间呢？比如0-3900、3900-7800这样的。',
        '非常抱歉，商品库中暂时没有符合您需要的商品～'],
     'query_start_times': ['2024-08-02 14:11:37.364', '2024-08-02 14:11:48.222', '2024-08-02 14:11:59.400'],
     'query_end_times': ['2024-08-02 14:11:47.217', '2024-08-02 14:11:58.396', '2024-08-02 14:12:07.432']},
    {'session_num': 3, 'session_id': 'd38f18b2-6564-4d8e-b477-36c5234da9f4', 'cost_times': [10000.61821937561, 7185.431003570557, 21061.485052108765],
     'query_array': ['今天下雪了，可以堆雪人玩啦~', '有手套吗？', '我想要两双儿童手套，价格在200元以内的'], 'answer_texts': [
        '哇，下雪天堆雪人真的超级有趣呢！❄️你是不是也喜欢户外运动呀？我们这儿有好多适合户外玩的装备，比如冲锋衣，保暖又防风，堆雪人的时候穿正好！你有没有兴趣看看呢？😉',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：冬款骑行运动手套 920\n商品价格：199.9\n商品简介：我们团队设计了这款手套，用于在0°C左右的温度时骑行时穿戴.冬季骑行手套采用符合人体工程学的龙虾式设计，旨在提供最佳的御寒保护。[https://www.decathlon.com.cn/product-detail?dsm_code=305607&model_code=8530335&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20920&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=305607&model_code=8530335&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20920&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D)\n\n[2] 商品名称：冬款骑行运动手套 900\n商品价格：149.9\n商品简介：我们团队设计了这款手套，用于在0°C左右的温度时骑行时穿戴.采用SOFTERMIC 衬里的冬季骑行手套。 手掌处设计有Technogel 内衬，增加舒适性。指腹面料可以不用脱下手套直接操控手机.[https://www.decathlon.com.cn/product-detail?dsm_code=305623&model_code=8530331&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20900&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=305623&model_code=8530331&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20900&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！',
        '哇，您真是位细心的家长呢！👏 我们这儿有几款非常适合小朋友的骑行手套，不仅保暖舒适，而且价格都在200元以内哦！您看看这些怎么样？\n\n[1] 商品名称：儿童骑行手套 800\n商品价格：99.9元/双\n商品简介：专为小朋友设计的骑行手套，保暖又舒适，特别适合小手佩戴。👶\n[购买链接](https://www.example.com/product-detail?model_code=8530330)\n\n[2] 商品名称：儿童保暖手套 700\n商品价格：89.9元/双\n商品简介：这款手套保暖效果极佳，柔软舒适，孩子们一定会喜欢的！🎈\n[购买链接](https://www.example.com/product-detail?model_code=8530329)\n\n如果您对这两款手套感兴趣，或者还有其他需求，都可以随时告诉我哦！💬 我会尽力为您提供帮助的！'],
     'query_start_times': ['2024-08-02 14:12:13.442', '2024-08-02 14:12:24.447', '2024-08-02 14:12:32.637'],
     'query_end_times': ['2024-08-02 14:12:23.443', '2024-08-02 14:12:31.633', '2024-08-02 14:12:53.699']},
    {'session_num': 4, 'session_id': 'b5f93eec-3a7b-4985-8078-3e5ddb6c5a4d', 'cost_times': [9152.092933654785, 10177.581071853638, 7145.150899887085],
     'query_array': ['今天爬山带的水喝没了，中途好渴', '有水壶吗', '推荐个山地的水壶吧'], 'answer_texts': [
        '哎呀，听起来你的爬山之旅有点小插曲呢！😅 下次爬山前可得记得多准备点水哦！不过说到爬山，我们这儿有不少适合户外运动的装备，比如超轻便的水壶，让你随时补充水分，不再半路口渴～🥤 怎么样，想不想看看？',
        '当然有啦！我们这儿的水壶款式还挺多的，有公路、儿童、山地、城市好几种呢。对了，你更偏好哪种出水方式呀？直饮式还是按压式？这样我可以更好地给你推荐哦！🚰',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：800毫升不锈钢自行车水壶\n商品价格：99.9\n商品简介：专为需要坚固水壶的越野自行车手而设计。同样适合日常使用。800毫升骑行水壶，可轻松打开，兼容水壶架，可更长时间保持液体原味。[https://www.decathlon.com.cn/product-detail?dsm_code=330919&model_code=8645460&category_id=6495398b21672f0001c10585&product_name=800%E6%AF%AB%E5%8D%87%E4%B8%8D%E9%94%88%E9%92%A2%E8%87%AA%E8%A1%8C%E8%BD%A6%E6%B0%B4%E5%A3%B6&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=330919&model_code=8645460&category_id=6495398b21672f0001c10585&product_name=800%E6%AF%AB%E5%8D%87%E4%B8%8D%E9%94%88%E9%92%A2%E8%87%AA%E8%A1%8C%E8%BD%A6%E6%B0%B4%E5%A3%B6&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！'],
     'query_start_times': ['2024-08-02 14:12:59.710', '2024-08-02 14:13:09.867', '2024-08-02 14:13:21.049'],
     'query_end_times': ['2024-08-02 14:13:08.862', '2024-08-02 14:13:20.045', '2024-08-02 14:13:28.194']},
    {'session_num': 5, 'session_id': '73efbd21-a1de-4ba6-bafa-54c820d683dd', 'cost_times': [8347.035884857178, 5937.237024307251, 7111.783981323242],
     'query_array': ['我的自行车被偷了', '有什么自行车？', '女士，3000块左右的自行车有推荐的吗？'], 'answer_texts': [
        '哎呀，听到你的自行车被偷了，真的好心疼💔！不过别担心，我这里有好多超棒的自行车，可以帮你重新找到骑行的快乐！要不要看看我们这儿的自行车，选一辆心仪的代步小伙伴呢？🚴\u200d♂️💨',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：RC 120 FB DISC CN 浅灰色\n商品价格：2999.9\n商品简介：我们设计出这款自行车，针对入门以及定期进行公路骑行的爱好者，采用2x8速设计，可探索多种路况。舒适的几何形状、铝制车架、铝碳前叉和碟刹刹车，因此自行车可适用于任何路径。[https://www.decathlon.com.cn/product-detail?dsm_code=313255&model_code=8773950&category_id=644a1c540c5c730001d1e01c&product_name=RC%20120%20FB%20DISC%20CN%20%E6%B5%85%E7%81%B0%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%22644a1c540c5c730001d1e01c%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=313255&model_code=8773950&category_id=644a1c540c5c730001d1e01c&product_name=RC%20120%20FB%20DISC%20CN%20%E6%B5%85%E7%81%B0%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%22644a1c540c5c730001d1e01c%22%7D)\n\n[2] 商品名称：迪卡侬RC120官方碟刹铝架碳纤维前叉公路自行车碟轻量赛车男OVB1\n商品价格：2999.9\n商品简介：为公路自行车的入门级爱好者而设计。舒适的车辆几何、铝制车架、铝碳前叉和碟刹刹车，因此自行车可适用于各种路况。因年份批次不同，变速器，刹车及车辆涂装会有所不同。[https://www.decathlon.com.cn/product-detail?dsm_code=313167&model_code=&item_code=](https://www.decathlon.com.cn/product-detail?dsm_code=313167&model_code=&item_code=)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！',
        '非常抱歉，商品库中暂时没有符合您需要的商品～'],
     'query_start_times': ['2024-08-02 14:13:34.200', '2024-08-02 14:13:43.550', '2024-08-02 14:13:50.489'],
     'query_end_times': ['2024-08-02 14:13:42.547', '2024-08-02 14:13:49.487', '2024-08-02 14:13:57.601']},
    {'session_num': 6, 'session_id': '2c6af428-c4ee-43b7-92e7-3a59c283fc87', 'cost_times': [8215.553998947144, 7083.520174026489, 7833.189010620117],
     'query_array': ['我儿子今天在学校和同学打架把衣服撕破了', '有小学生冲锋衣吗', '200-400元的，主要是城市通勤场合'], 'answer_texts': [
        '哎呀，听起来像是场“激烈”的较量呢！💪不过别担心，我这里有好多适合小朋友的户外冲锋衣，既耐穿又时尚，保证下次“战斗”时更加帅气！要不要我给推荐几款？😉',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：男士山地徒步防水夹克 | MT 500\n商品价格：799.9\n商品简介：这款防水夹克由我们热爱山地徒步运动的团队设计完成，让您在多风多雨的地方也能获得充分保护。这款防水夹克结合了一种轻便透气的面料和一种结实的面料，让您不惧雨水的威胁。[https://www.decathlon.com.cn/product-detail?dsm_code=339506&model_code=8739514&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%A3%AB%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20%7C%20MT%20500&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=339506&model_code=8739514&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%A3%AB%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20%7C%20MT%20500&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D)\n\n[2] 商品名称：男式山地徒步轻盈防水夹克 MH150\n\n商品价格：349.9\n商品简介：在勃朗峰山脚下，我们的徒步团队设计了这款防水冲锋衣，让你在山区行走时更加安全。生态设计的冲锋衣，由再生的聚酯纤维制成，方便徒步活动时穿着。[https://www.decathlon.com.cn/product-detail?dsm_code=309604&model_code=8796055&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%BC%8F%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E8%BD%BB%E7%9B%88%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20MH150&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=309604&model_code=8796055&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%BC%8F%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E8%BD%BB%E7%9B%88%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20MH150&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：男式山地徒步轻盈防水夹克 MH150\n\n商品价格：349.9\n商品简介：在勃朗峰山脚下，我们的徒步团队设计了这款防水冲锋衣，让你在山区行走时更加安全。生态设计的冲锋衣，由再生的聚酯纤维制成，方便徒步活动时穿着。[https://www.decathlon.com.cn/product-detail?dsm_code=309604&model_code=8796055&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%BC%8F%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E8%BD%BB%E7%9B%88%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20MH150&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=309604&model_code=8796055&category_id=65f3b669e750cb0001a04076&product_name=%E7%94%B7%E5%BC%8F%E5%B1%B1%E5%9C%B0%E5%BE%92%E6%AD%A5%E8%BD%BB%E7%9B%88%E9%98%B2%E6%B0%B4%E5%A4%B9%E5%85%8B%20MH150&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！'],
     'query_start_times': ['2024-08-02 14:14:03.610', '2024-08-02 14:14:12.829', '2024-08-02 14:14:20.919'],
     'query_end_times': ['2024-08-02 14:14:11.826', '2024-08-02 14:14:19.913', '2024-08-02 14:14:28.752']},
    {'session_num': 7, 'session_id': '77bea9ec-8ec1-4e89-ac2b-38efd25fbe2f', 'cost_times': [6200.690984725952, 7102.106809616089, 7420.699834823608],
     'query_array': ['现在便宜的水壶一言难尽', '你有啥样的水壶', '推荐个直饮式的100元以内的水壶呢'], 'answer_texts': [
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：骑行水壶FASTFLOW 中号 650毫升\n商品价格：39.9\n商品简介：这款水壶由我们的骑行爱好者团队开发，适合想在骑行时补充水分的高要求骑行者。SureSnap®壶嘴可控制水流量，即使打开壶嘴，也不会漏液。不会再有水滴到车架上了。[https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D)\n\n[2] 商品名称：骑行水壶FASTFLOW 中号 650毫升\n商品价格：39.9\n商品简介：这款水壶由我们的骑行爱好者团队开发，适合想在骑行时补充水分的高要求骑行者。SureSnap®壶嘴可控制水流量，即使打开壶嘴，也不会漏液。不会再有水滴到车架上了。[https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：骑行水壶FASTFLOW 中号 650毫升\n商品价格：39.9\n商品简介：这款水壶由我们的骑行爱好者团队开发，适合想在骑行时补充水分的高要求骑行者。SureSnap®壶嘴可控制水流量，即使打开壶嘴，也不会漏液。不会再有水滴到车架上了。[https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D)\n\n[2] 商品名称：骑行水壶FASTFLOW 中号 650毫升\n商品价格：39.9\n商品简介：这款水壶由我们的骑行爱好者团队开发，适合想在骑行时补充水分的高要求骑行者。SureSnap®壶嘴可控制水流量，即使打开壶嘴，也不会漏液。不会再有水滴到车架上了。[https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=304308&model_code=8797501&category_id=6495398b21672f0001c10585&product_name=%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6FASTFLOW%20%E4%B8%AD%E5%8F%B7%20650%E6%AF%AB%E5%8D%87%20%E9%80%8F%E6%98%8E%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：ALLROAD LIGHT 600ML 轻量化基础款骑行水壶-白色\n商品价格：14.9\n商品简介：骑行时喝水。600 毫升水壶，中等容量。 可存放常温水或饮料。[https://www.decathlon.com.cn/product-detail?dsm_code=304235&model_code=8518742&category_id=6495398b21672f0001c10585&product_name=ALLROAD%20LIGHT%20600ML%20%E8%BD%BB%E9%87%8F%E5%8C%96%E5%9F%BA%E7%A1%80%E6%AC%BE%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6-%E7%99%BD%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=304235&model_code=8518742&category_id=6495398b21672f0001c10585&product_name=ALLROAD%20LIGHT%20600ML%20%E8%BD%BB%E9%87%8F%E5%8C%96%E5%9F%BA%E7%A1%80%E6%AC%BE%E9%AA%91%E8%A1%8C%E6%B0%B4%E5%A3%B6-%E7%99%BD%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%22sports_zh%22%3A%5B%22%E8%BF%90%E5%8A%A8%E5%85%AC%E8%B7%AF%E8%87%AA%E8%A1%8C%E8%BD%A6%22%5D,%22sale_price%22%3A%5B14.9,99.9%5D%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%226495398b21672f0001c10585%22%7D)\n\n[2] 商品名称：500 380 毫升儿童水壶\n商品价格：29.9\n商品简介：我们设计了这款水壶，适合儿童手的大小. 380毫升容量，易于携带!(水壶架另售)这款水壶采用软咬嘴设计，适合儿童. 设计用于轻松滑入儿童自行车车架或车把. 有两种颜色可选.[https://www.decathlon.com.cn/product-detail?dsm_code=100763&model_code=%E5%84%BF%E7%AB%A5%E6%B0%B4%E5%A3%B6](https://www.decathlon.com.cn/product-detail?dsm_code=100763&model_code=%E5%84%BF%E7%AB%A5%E6%B0%B4%E5%A3%B6)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！'],
     'query_start_times': ['2024-08-02 14:14:34.759', '2024-08-02 14:14:41.965', '2024-08-02 14:14:50.074'],
     'query_end_times': ['2024-08-02 14:14:40.960', '2024-08-02 14:14:49.068', '2024-08-02 14:14:57.494']},
    {'session_num': 8, 'session_id': 'e0a4ece6-bcc9-4df6-93da-8b662d97b65a', 'cost_times': [9695.273876190186, 6427.893877029419, 7149.580001831055],
     'query_array': ['冬天洗菜手真遭罪', '你的手套是什么样式的', '不超过300元的能用手套推荐一下，谢谢'], 'answer_texts': [
        '哎呀，冬天洗菜确实是个挑战呢！🥶手都冻得红彤彤的，看着都心疼。😢 那你有没有想过试试一些保暖又方便的手套呢？我们这里有好多适合冬天用的手套哦，说不定能帮你解决这个问题呢！🤔',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：冬款骑行运动手套 920\n商品价格：199.9\n商品简介：我们团队设计了这款手套，用于在0°C左右的温度时骑行时穿戴.冬季骑行手套采用符合人体工程学的龙虾式设计，旨在提供最佳的御寒保护。[https://www.decathlon.com.cn/product-detail?dsm_code=305607&model_code=8530335&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20920&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=305607&model_code=8530335&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20920&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D)\n\n[2] 商品名称：冬款骑行运动手套 900\n商品价格：149.9\n商品简介：我们团队设计了这款手套，用于在0°C左右的温度时骑行时穿戴.采用SOFTERMIC 衬里的冬季骑行手套。 手掌处设计有Technogel 内衬，增加舒适性。指腹面料可以不用脱下手套直接操控手机.[https://www.decathlon.com.cn/product-detail?dsm_code=305623&model_code=8530331&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20900&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=305623&model_code=8530331&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20900&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：冬款骑行运动手套 920\n商品价格：199.9\n商品简介：我们团队设计了这款手套，用于在0°C左右的温度时骑行时穿戴.冬季骑行手套采用符合人体工程学的龙虾式设计，旨在提供最佳的御寒保护。[https://www.decathlon.com.cn/product-detail?dsm_code=305607&model_code=8530335&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20920&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=305607&model_code=8530335&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20920&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D)\n\n[2] 商品名称：冬款骑行运动手套 900\n商品价格：149.9\n商品简介：我们团队设计了这款手套，用于在0°C左右的温度时骑行时穿戴.采用SOFTERMIC 衬里的冬季骑行手套。 手掌处设计有Technogel 内衬，增加舒适性。指腹面料可以不用脱下手套直接操控手机.[https://www.decathlon.com.cn/product-detail?dsm_code=305623&model_code=8530331&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20900&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=305623&model_code=8530331&category_id=644a1c540c5c730001d1e028&product_name=%E5%86%AC%E6%AC%BE%E9%AA%91%E8%A1%8C%E8%BF%90%E5%8A%A8%E6%89%8B%E5%A5%97%20900&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%22644a1c540c5c730001d1e028%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！'],
     'query_start_times': ['2024-08-02 14:15:03.504', '2024-08-02 14:15:14.205', '2024-08-02 14:15:21.636'],
     'query_end_times': ['2024-08-02 14:15:13.199', '2024-08-02 14:15:20.633', '2024-08-02 14:15:28.785']},
    {'session_num': 9, 'session_id': '16753b39-7a20-452b-81fd-af967736fe39', 'cost_times': [10868.63374710083, 6193.103790283203, 7524.3237018585205],
     'query_array': ['骑小电驴快是快，就是充电要钱', '有山地自行车吗', '5000元以内的'], 'answer_texts': [
        '哈哈，看来你是个环保又经济的出行达人呢！骑小电驴确实很方便，但充电成本也是个头疼的问题🤔。说到出行，我们这儿有不少适合户外运动的装备，比如骑行手套、水壶之类的，都能让你的出行更加舒适和便捷哦！你有没有兴趣了解一下呢？🚴\u200d♂️💧',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：XC 900 RR 山地车\n商品价格：11999.9\n商品简介：山地车竞速、训练。采用轻量碳纤维车架，搭配ROCKSHOX REBA RL 100mm 前叉，MAVIC Crossmax车轮和SRAM GX EAGLE 1X12动力传动系统，可助您快速冲向终点线。[https://www.decathlon.com.cn/product-detail?dsm_code=303549&model_code=8644263&category_id=644a1c540c5c730001d1e01c&product_name=XC%20900%2029%22%20RR%20%E5%B1%B1%E5%9C%B0%E8%BD%A6&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%22644a1c540c5c730001d1e01c%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=303549&model_code=8644263&category_id=644a1c540c5c730001d1e01c&product_name=XC%20900%2029%22%20RR%20%E5%B1%B1%E5%9C%B0%E8%BD%A6&listFilter=%7B%22sort%22%3A%22PRICE_DESC%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%22644a1c540c5c730001d1e01c%22%7D)\n\n[2] 商品名称：XC920S碳纤维前碳后铝山地越野竞赛自行车\n商品价格：19999.9\n商品简介：这款山地自行车是为定期训练和参加越野山地车马拉松赛事而设计的。助您快人一步，超越自己。全避震碳/铝合金29“车架，助您再创佳绩。120 mm的避震能助您在马拉松和长距离比赛中取得更优异的成绩。[https://www.decathlon.com.cn/product-detail?dsm_code=334011&model_code=8647806&item_code=](https://www.decathlon.com.cn/product-detail?dsm_code=334011&model_code=8647806&item_code=)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：山地自行车ST900 黑红色\n商品价格：3499.9\n商品简介：ST900硬尾山地车，品质卓越，专为爱在长途、全地形探险中保持舒适骑行的专业骑手而设计。这款山地车可让您在各种地形上开启舒适感十足的骑行冒险。[https://www.decathlon.com.cn/product-detail?dsm_code=310214&model_code=8559445&category_id=644b56b540d0d90001d5da0a&product_name=%E5%B1%B1%E5%9C%B0%E8%87%AA%E8%A1%8C%E8%BD%A6ST900%20%E9%BB%91%E7%BA%A2%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%22644b56b540d0d90001d5da0a%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=310214&model_code=8559445&category_id=644b56b540d0d90001d5da0a&product_name=%E5%B1%B1%E5%9C%B0%E8%87%AA%E8%A1%8C%E8%BD%A6ST900%20%E9%BB%91%E7%BA%A2%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%5D,%22category_id%22%3A%22644b56b540d0d90001d5da0a%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！'],
     'query_start_times': ['2024-08-02 14:15:34.790', '2024-08-02 14:15:46.662', '2024-08-02 14:15:53.861'],
     'query_end_times': ['2024-08-02 14:15:45.659', '2024-08-02 14:15:52.855', '2024-08-02 14:16:01.385']},
    {'session_num': 10, 'session_id': 'a59287fa-72fb-4b88-99b8-316a8ad6b8f1', 'cost_times': [9966.731071472168, 8076.514005661011, 7528.381824493408],
     'query_array': ['现在的衣服太贵了，动不动就好几大百', '有什么衣服', '不超过300元的山野冲锋衣'], 'answer_texts': [
        '哎呀，你说得对！现在的衣服价格确实让人头疼🤯。不过，别担心，我这里有好多性价比超高的户外运动装备，说不定能帮你省下一笔呢！你对户外运动感兴趣吗？比如骑行、徒步之类的？🚴\u200d♂️🏞️',
        '哈哈，说到衣服，我这里正好有一款超棒的冲锋衣！🧣它不仅防风防水，而且设计时尚，穿上它，你就是户外最靓的崽！怎么样，想不想了解一下？😉',
        '根据您的需求，我为您推荐如下商品：\n        \n[1] 商品名称：NH500 男式自然徒步经典夹克 黑色\n商品价格：249.9\n商品简介：这款防水夹克通过降雨量达到 5 米的暴雨测试。设有 4 个密封口袋，搭配透气的衬里。我们的设计师以环保方式设计了这款 NH500 防水夹克，适合雨天在平原、森林或海岸边潮湿天气里进行常规徒步时穿着。[https://www.decathlon.com.cn/product-detail?dsm_code=352053&model_code=8862206&category_id=65f3b669e750cb0001a04076&product_name=NH500%20%E7%94%B7%E5%BC%8F%E8%87%AA%E7%84%B6%E5%BE%92%E6%AD%A5%E7%BB%8F%E5%85%B8%E5%A4%B9%E5%85%8B%20%E9%BB%91%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D](https://www.decathlon.com.cn/product-detail?dsm_code=352053&model_code=8862206&category_id=65f3b669e750cb0001a04076&product_name=NH500%20%E7%94%B7%E5%BC%8F%E8%87%AA%E7%84%B6%E5%BE%92%E6%AD%A5%E7%BB%8F%E5%85%B8%E5%A4%B9%E5%85%8B%20%E9%BB%91%E8%89%B2&listFilter=%7B%22sort%22%3A%22DEFAULT_SORT%22,%22selectFilter%22%3A%7B%7D,%22expandData%22%3A%5B%22sports_zh%22%5D,%22category_id%22%3A%2265f3b669e750cb0001a04076%22%7D)\n\n如果您想了解商品具体情况，可点击链接，如果您还有其他问题或需要进一步的帮助，请随时与我联系。祝您购物愉快！'],
     'query_start_times': ['2024-08-02 14:16:07.393', '2024-08-02 14:16:18.366', '2024-08-02 14:16:27.445'],
     'query_end_times': ['2024-08-02 14:16:17.360', '2024-08-02 14:16:26.442', '2024-08-02 14:16:34.974']}]

chitchat_query_time = []
intent_clarify_query_time = []
product_recommend_query_time = []
valid_all_query_time = []
for i in range(0, len(all_query_times)):
    time = all_query_times[i]
    if time == -1:
        continue
    else:
        if i % 3 == 0:
            chitchat_query_time.append(time)
        elif i % 3 == 1:
            intent_clarify_query_time.append(time)
        else:
            product_recommend_query_time.append(time)

        valid_all_query_time.append(time)

print(all_query_times)
print(chitchat_query_time)
print(intent_clarify_query_time)
print(product_recommend_query_time)

# P90指标
P90_time_chitchat = np.percentile(chitchat_query_time, 90)
print("闲聊场景P90：【{}】ms".format(P90_time_chitchat))
P90_time_intent_clarify = np.percentile(intent_clarify_query_time, 90)
print("澄清场景P90：【{}】ms".format(P90_time_intent_clarify))
P90_time_product_recommend = np.percentile(product_recommend_query_time, 90)
print("推荐场景P90：【{}】ms".format(P90_time_product_recommend))
P90_time_all = np.percentile(valid_all_query_time, 90)
print("所有场景P90：【{}】ms".format(P90_time_all))

# P99指标
P99_time_chitchat = np.percentile(chitchat_query_time, 99)
print("闲聊场景P99：【{}】ms".format(P99_time_chitchat))
P99_time_intent_clarify = np.percentile(intent_clarify_query_time, 99)
print("澄清场景P99：【{}】ms".format(P99_time_intent_clarify))
P99_time_product_recommend = np.percentile(product_recommend_query_time, 99)
print("推荐场景P99：【{}】ms".format(P99_time_product_recommend))
P99_time_all = np.percentile(valid_all_query_time, 99)
print("所有场景P99：【{}】ms".format(P99_time_all))

# 平均应答时间
avg_time_chitchat = np.mean(chitchat_query_time)
print("闲聊场景平均应答时间：【{}】ms".format(avg_time_chitchat))
avg_time_intent_clarify = np.mean(intent_clarify_query_time)
print("澄清场景平均应答时间：【{}】ms".format(avg_time_intent_clarify))
avg_time_product_recommend = np.mean(product_recommend_query_time)
print("推荐场景平均应答时间：【{}】ms".format(avg_time_product_recommend))
avg_time_all = np.mean(valid_all_query_time)
print("所有场景平均应答时间：【{}】ms".format(avg_time_all))

print("""<style type="text/css">
.tg  {border-collapse:collapse;border-spacing:0;}
.tg td{border-color:black;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;
  overflow:hidden;padding:10px 5px;word-break:normal;}
.tg th{border-color:black;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;
  font-weight:normal;overflow:hidden;padding:10px 5px;word-break:normal;}
.tg .tg-uqo3{background-color:#efefef;text-align:center;vertical-align:top}
.tg .tg-7fle{background-color:#efefef;font-weight:bold;text-align:center;vertical-align:top}
.tg .tg-baqh{text-align:center;vertical-align:top}
.tg .tg-mxrt{color:#fe0000;font-weight:bold;text-align:center;vertical-align:top}
</style>
""")


def generate_html_table(query_url, file_name, sheet_name, user_name, agent_id,
                        model_type, scene, fast_scene_query_time, sessions_info):
    """
    生成html表格，展示会话记录数据
    :param query_url: 域名，区分是 keyue-test.cloud.baidu.com 还是 keyue.cloud.baidu.com
    :param file_name: 测试使用的excel文件名
    :param sheet_name: sheet名
    :param user_name: 测试使用的租户名
    :param agent_id: 测试使用的agent_id
    :param model_type: 测试使用的模型类型
    :param scene: 测试使用的场景，如：商品导购场景
    :param fast_scene_query_time: 快捷场景的query时延
    :param sessions_info: 所有session的信息（sessionId、首字时延、query起始时刻、首字响应时刻、query、首字响应答案）
    :param all_query_times: 所有query的应答时间
    :return:
    """

    # 打印公共信息
    html = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><title>客悦时延效果报告</title></head>"
    html += f"<table border='2' bordercolor='black' cellspacing='0' cellpadding='0' style='table-layout:fixed;word-break:break-all'><tr>\
            <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
            <strong>客悦【{scene}】场景首字响应时延报告，user_name【{user_name}】，agent_id【{agent_id}】<br>\
            模型版本【{model_type}】，数据表excel名【{file_name}】，sheet名【{sheet_name}】，query_url【{query_url}】\
            </strong></td></tr>"

    if scene == "商品导购场景":
        # 输出指标结果
        # 指标第一行：表头
        html += """<tr><td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;font-weight:normal;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">
        </td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">
query总数(条)</td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">
P90(ms)</td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">
P99(ms)</td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">
平均(ms)</td></tr>"""

        # 指标第二行：
        # 第一列：闲聊
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>闲聊</strong>"
        html += '</td>'
        # 第二列：闲聊 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('chitchat_query_num')}</strong>"
        html += '</td>'
        # 第三列：闲聊 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_chitchat')}</strong>"
        html += '</td>'
        # 第四列：闲聊 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_chitchat')}</strong>"
        html += '</td>'
        # 第五列：闲聊 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_chitchat')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 指标第三行：澄清
        # 第一列：澄清
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>澄清</strong>"
        html += '</td>'
        # 第二列：澄清 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('intent_clarify_query_num')}</strong>"
        html += '</td>'
        # 第三列：澄清 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_intent_clarify')}</strong>"
        html += '</td>'
        # 第四列：澄清 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_intent_clarify')}</strong>"
        html += '</td>'
        # 第五列：澄清 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_intent_clarify')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 指标第四行：推荐
        # 第一列：推荐
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>推荐</strong>"
        html += '</td>'
        # 第二列：推荐 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('product_recommend_query_num')}</strong>"
        html += '</td>'
        # 第三列：推荐 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_product_recommend')}</strong>"
        html += '</td>'
        # 第四列：推荐 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_product_recommend')}</strong>"
        html += '</td>'
        # 第五列：推荐 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_product_recommend')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 指标第五行：全部
        # 第一列：全部
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>全部</strong>"
        html += '</td>'
        # 第二列：全部 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('all_query_num')}</strong>"
        html += '</td>'
        # 第三列：全部 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_all')}</strong>"
        html += '</td>'
        # 第四列：全部 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_all')}</strong>"
        html += '</td>'
        # 第五列：全部 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_all')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 遍历所有session，打印query详情
        html += "<tr bgcolor='yellow'>\
            <td width='auto' align='center'><strong>测试场景</strong></td>\
            <td width='auto' align='center' colspan='6'><strong>query以及结果（标红表示响应时长超过3s，蓝底表示回复内容为空）</strong></td></tr>"
        for row in sessions_info:
            for i in range(0, len(row.get('cost_times'))):
                html += "<tr>"
                query = row.get('query_array')[i]
                cost_time = row.get('cost_times')[i]
                first_answer_text = row.get('answer_texts')[i]
                query_start_time = row.get('query_start_times')[i]
                query_end_time = row.get('query_end_times')[i]

                # 展示sessionId的列只展示一次
                if i == 0:
                    col1_text = "第【{}】个session，session_id:【{}】".format(row.get('session_num'), row.get('session_id'))
                    html += f"<td rowspan='{len(row.get('cost_times'))}'>{col1_text}</td>"

                col2_text = "第【{}】轮 === query:【{}】，首字响应结果：【{}】\
                            <br>query起始时刻:【{}】\
                            <br>query首字响应时刻:【{}】\
                            <br><strong>首字响应时长：【{}】ms</strong>" \
                    .format(i + 1, query, first_answer_text, query_start_time, query_end_time, round(cost_time, 2))

                # 首字响应时长超过3s，标红字；首字响应结果为空，标黄底
                if cost_time > 3000.0 and first_answer_text == '':
                    html += f"<td style='color:red' bgcolor='aquamarine' colspan='5'>{col2_text}</td>"
                elif cost_time <= 3000.0 and first_answer_text == '':
                    html += f"<td style='color:black' bgcolor='aquamarine' colspan='5'>{col2_text}</td>"
                elif cost_time > 3000.0 and first_answer_text != '':
                    html += f"<td style='color:red' colspan='5'>{col2_text}</td>"
                else:
                    html += f"<td style='color:black' colspan='5'>{col2_text}</td>"
                html += "</tr>"
    html += "</table>"
    html += "</body></html>"
    return html
abc

def time_calculate_production_recommend(all_query_times):
    """
    计算快捷场景-商品导购的时延
    :param all_query_times: 所有query的应答时间
    :return:
    """

    """
    关于快捷场景，以商品导购场景为例，说明如下：
    每个session有3轮query，每轮query分别触发：闲聊、澄清、推荐
    在daogouchangjing.xlsx文件中，总共准备了10个session
    在计算时延时，会统计所有query的平均时延、P90、P99指标
    然后会分别统计闲聊、澄清、推荐的平均时延、P90、P99指标

    注：前提是所有query最后都能返回结果，且结果不为空
    """

    # 整理出有效的 闲聊、澄清、推荐、全部 的基础响应时延
    chitchat_query_time = []
    intent_clarify_query_time = []
    product_recommend_query_time = []
    valid_all_query_time = []
    for i in range(0, len(all_query_times)):
        time = all_query_times[i]
        if time == -1:
            continue
        else:
            if i % 3 == 0:
                chitchat_query_time.append(time)
            elif i % 3 == 1:
                intent_clarify_query_time.append(time)
            else:
                product_recommend_query_time.append(time)

            valid_all_query_time.append(time)

    # P90指标
    P90_time_chitchat = round(np.percentile(chitchat_query_time, 90), 2)
    print("闲聊场景P90：【{}】ms".format(P90_time_chitchat))
    P90_time_intent_clarify = round(np.percentile(intent_clarify_query_time, 90), 2)
    print("澄清场景P90：【{}】ms".format(P90_time_intent_clarify))
    P90_time_product_recommend = round(np.percentile(product_recommend_query_time, 90), 2)
    print("推荐场景P90：【{}】ms".format(P90_time_product_recommend))
    P90_time_all = round(np.percentile(valid_all_query_time, 90), 2)
    print("所有场景P90：【{}】ms".format(P90_time_all))

    # P99指标
    P99_time_chitchat = round(np.percentile(chitchat_query_time, 99), 2)
    print("闲聊场景P99：【{}】ms".format(P99_time_chitchat))
    P99_time_intent_clarify = round(np.percentile(intent_clarify_query_time, 99), 2)
    print("澄清场景P99：【{}】ms".format(P99_time_intent_clarify))
    P99_time_product_recommend = round(np.percentile(product_recommend_query_time, 99), 2)
    print("推荐场景P99：【{}】ms".format(P99_time_product_recommend))
    P99_time_all = round(np.percentile(valid_all_query_time, 99), 2)
    print("所有场景P99：【{}】ms".format(P99_time_all))

    # 平均应答时间
    avg_time_chitchat = round(np.mean(chitchat_query_time), 2)
    print("闲聊场景平均应答时间：【{}】ms".format(avg_time_chitchat))
    avg_time_intent_clarify = round(np.mean(intent_clarify_query_time), 2)
    print("澄清场景平均应答时间：【{}】ms".format(avg_time_intent_clarify))
    avg_time_product_recommend = round(np.mean(product_recommend_query_time), 2)
    print("推荐场景平均应答时间：【{}】ms".format(avg_time_product_recommend))
    avg_time_all = round(np.mean(valid_all_query_time), 2)
    print("所有场景平均应答时间：【{}】ms".format(avg_time_all))

    product_recommend_query_time = {
        "P90_time_chitchat": P90_time_chitchat,
        "P90_time_intent_clarify": P90_time_intent_clarify,
        "P90_time_product_recommend": P90_time_product_recommend,
        "P90_time_all": P90_time_all,
        "P99_time_chitchat": P99_time_chitchat,
        "P99_time_intent_clarify": P99_time_intent_clarify,
        "P99_time_product_recommend": P99_time_product_recommend,
        "P99_time_all": P99_time_all,
        "avg_time_chitchat": avg_time_chitchat,
        "avg_time_intent_clarify": avg_time_intent_clarify,
        "avg_time_product_recommend": avg_time_product_recommend,
        "avg_time_all": avg_time_all,
        "chitchat_query_num": len(chitchat_query_time),
        "intent_clarify_query_num": len(intent_clarify_query_time),
        "product_recommend_query_num": len(product_recommend_query_time),
        "all_query_num": len(valid_all_query_time)
    }

    return product_recommend_query_time


product_recommend_query_time = time_calculate_production_recommend(all_query_times)

print(product_recommend_query_time)
html = generate_html_table(
    query_url="asdfasdf",
    file_name="asdfasdf",
    sheet_name="sheet_name",
    user_name="adsfasdf",
    agent_id="asdfasdf",
    model_type="e",
    scene="商品导购场景",
    fast_scene_query_time=product_recommend_query_time,
    sessions_info=session_info)
# 将HTML代码写入文件
try:
    with open(f"test-report.html", "w", encoding='utf-8') as f:
        f.write(html)
except Exception as e:
    print(e)
