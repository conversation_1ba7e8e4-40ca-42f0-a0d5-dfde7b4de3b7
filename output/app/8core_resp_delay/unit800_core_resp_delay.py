# coding:utf8
'''
@author:dangchujia
'''
from datetime import datetime

import requests
import time
import openpyxl
import argparse
import json
import re
import numpy as np

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('userName', help='租户名称')  # 租户名称
parser.add_argument('agentId', help='agent id')  # agent id
parser.add_argument('token', help='agent发布后的 token')  # agent发布后的 token
parser.add_argument('host', help='域名：keyue-test or keyue')  # keyue-test or keyue
parser.add_argument('typeHost', help='offline or online')  # offline or online
parser.add_argument('env', help='production or test')  # 生产环境 or 测试环境
parser.add_argument('qps', help='每秒发送多少个query')  # 每秒发送多少个query
parser.add_argument('modelType', help='大模型eb3.5 or 大模型eb4.0')  # 大模型eb3.5 or 大模型eb4.0
parser.add_argument('engine', help='对话引擎：faq、docQa、task_based、tableQa')  # 测试引擎名称
parser.add_argument('taskBasedScene', help='多轮的场景：电力服务、车辆小管家；表格问答的场景')  # 任务式会话测试场景，或者表格问答测试场景

# 解析命令行参数
args = parser.parse_args()

# 两个租户下各自的【新-时延效果测试-勿动】信息
base_info = {
    args.userName: {
        "agentId": args.agentId,
        "token": args.token
    }
}

offline_url = "https://{}/core/v5/stream/query".format(args.host)  # 流式
online_url = "https://{}/online/core/v5/stream/query".format(args.host)  # 流式

if args.typeHost == "offline":
    env_array = [offline_url]
elif args.typeHost == "online":
    env_array = [online_url]
else:
    env_array = [offline_url, online_url]

# qps：每秒发送多少个query，设置为1，表示每秒发送一个query，设置为2表示每秒发送2个query
qps = int(args.qps)
# 控制qps
sleep_time = 1 / qps


def session_query(query_array, user_name, query_url, engine):
    """
    测试文档问答，每调用一次，表示一次session
    :param query_array: 一个session的query数组
    :param user_name: 租户名称
    :param query_url: query接口请求地址
    :param engine: 对话引擎
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'uid': '**********',
        'username': 'dcj36513',
        'accountType': 'PASSPORT',
        'Token': token,
        'Cookie': 'bceAccountName=PASSPORT:**********;\
        bce-ctl-client-cookies="BDUSS,bce-passport-stoken,\
        bce-device-cuid,bce-device-token,BAIDUID"; \
        bce-ctl-sessionmfa-cookie=bce-session; \
                  bce-login-display-name=dcj36513; \
                  bce-userbind-source=PASSPORT%3BUUAP; \
                  bce-auth-type=PASSPORT; \
                  bce-login-type=PASSPORT; loginUserId=**********; \
                  Hm_lvt_2e94ac2e1bcde2839e431fe263a8762e=**********; \
                  BDUSS=U1QnJaYzdzVE5MVzlPSzVNbHJMYWtFOFZSWXJZa3JMd3lqaD\
                  BrZUVvV1AxdU5sSVFBQUFBJCQAAAAAAQAAAAEAAABN\
                  -CyCZGNqMzY1MTMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
                  AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9JvGWPSbxlUE; \
                  BDUSS_BFESS=U1QnJaYzdzVE5MVzlPSzVNbHJMYWtFOFZSWXJZa3\
                  JMd3lqaDBrZUVvV1AxdU5sSVFBQUFBJCQAAAAAAQAAAAEAAABN\
                  -CyCZGNqMzY1MTMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
                  AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9JvGWPSbxlUE; \
                  bce-passport-stoken=23b50d75b85a0ed37d47e0488d8bfebf86\
                  9a5be992e132c3d9da784ca8f3c2d1; bce-sessionid=0014472e4\
                  ff8e7e411bb494a9632f2c81b1; bce-user-info=2024-02-02T09:46:57Z\
                  |79b4e04ced710ba4923d0e2438d150f8; bce-session=\
                  501b6fe9d6654c7bba8a5817506423ba4f56c6ac126e4\
                  a8f8fb9a490229cb46a|da754f4d1428a62e84b47d660e546f17;\
                   bce-login-expire-time="2024-02-02T02:16:57Z|9f93f5e27\
                   1e4dc178a06aa96e3da4611"; BAIDUID=D1F8B9E6B09E7B6A3BC\
                   829D817B986E7:FG=1; BAIDUID_BFESS=D1F8B9E6B09E7B6A3BC\
                   829D817B986E7:FG=1; Hm_lpvt_2e94ac2e1bcde2839e431fe2\
                   63a8762e=1706838435; Hm_lvt_2ffc4ed803ded123a8731727\
                   1c849548=1706498435,1706838465; Hm_lpvt_2ffc4ed803de\
                   d123a87317271c849548=1706838465; JSESSIONID=node0k09\
                   mel35wgi31ng1kov2huk3n72.node0'
    }
    params = {
        "Token": token
    }
    # 随机生成一个sessionId
    sessionId = args.engine + str(time.time())

    # 存储一轮会话中各query的应答时间
    cost_times = []
    # 存储一轮会话中的响应答案text
    answer_texts = []
    # query的起始时间
    query_start_times = []
    # query的收到第一个Stream的时间
    query_end_times = []
    for query in query_array:
        time.sleep(sleep_time)
        if "tableQa" == engine:
            json_data = {
                "queryText": query,
                "sessionId": sessionId,
                "agentId": agentId
            }
        else:
            json_data = {
                "queryText": query,
                "sessionId": sessionId,
                "agentId": agentId,
                "engines": [
                    engine,
                ],
                "variables": {}
            }
        # 如果为online环境，则添加channel
        if query_url.find("online") > 0:
            json_data["channel"] = "_sys_web"

        # 当前query起始时间
        start_time = time.time()
        query_start_times.append(datetime.fromtimestamp(start_time).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])
        # 当前query结束时间
        end_time = start_time

        # 重试次数
        retry = 0
        retry_num = 3
        http_post = None
        while retry < retry_num:
            try:
                http_post = requests.post(url=query_url, params=params, json=json_data, headers=headers,
                                          stream=True, timeout=180)
                break
            except requests.ConnectionError as e:
                print(e)
                retry += 1
                time.sleep(1)

        # 请求失败，重试次数已达上限
        if http_post is None:
            print("query失败，重试次数已达上限")
            continue

        # 请求成功，开始解析应答流式数据
        # 拼接首字应答流式数据
        answer_text = ''
        for line in http_post.iter_lines():
            if line:
                # 将二进制数据流转为字符串
                line = line.decode('utf-8')
                if line.startswith('data:'):
                    # 获取到第一个Stream时，记录时间，退出
                    end_time = time.time()
                    query_end_times.append(datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])

                    # decode('utf-8')时可能会报错：'utf-8' codec can't decode byte 0x81 in position 0: invalid start byte
                    try:
                        res_contents = re.findall(r'data:(.*)', line)
                        for content in res_contents:
                            reply = json.loads(content)['answer'][0]['reply']
                            if reply is not None:
                                answer_text += reply.get('text')
                                break
                    except Exception as e:
                        print(e)

                    # 第一个Stream的应答为空就再进行一下一个Stream
                    if answer_text != '':
                        answer_texts.append(answer_text)
                        break

        # 直接未收到回复
        if answer_text == '':
            print("query失败，未收到回复")
            answer_texts.append('')

        # print("query开始时间：", start_time)
        # print("query收到第一个Stream时间：", end_time))

        # 请求有问题，标记本条query的应答时间为-1，表示无效，不计入平均值
        if end_time == start_time:
            cost_time = -1
            query_end_times.append(datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])
        else:
            # 计算当前query的应答时间，单位ms
            cost_time = (end_time - start_time) * 1000  # ms

        # 存储当前query的应答时间，单位ms
        cost_times.append(cost_time)

    return sessionId, cost_times, answer_texts, query_start_times, query_end_times


def parse_excel(excel_name, sheet_names):
    """
    解析excel表格数据，存储为字典+数组
    :param excel_name: excel文件名
    :param sheet_names: sheet名
    :return: tables对象，key为sheet名，value为表格数据
    """
    workbook = openpyxl.load_workbook(excel_name)
    tables = {}
    # 通过工作表名称选择当前活动的工作表
    for sheet_name in sheet_names:
        sheet = workbook[sheet_name]
        table = {}
        for row_index in range(1, sheet.max_row + 1):
            # 拿到表头数据名
            if row_index == 1:
                for col_index in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=1, column=col_index)
                    table[cell.value] = []
                continue
            # 存储第个表头下的所有数据，以表头为key，数据为value数组
            for col_index in range(1, sheet.max_column + 1):
                cell = sheet.cell(row=row_index, column=col_index)
                table[sheet.cell(row=1, column=col_index).value].append(cell.value)

        tables[sheet_name] = table
    return tables


def query_control(table, user_name, query_url, engine):
    """
    根据表格内容，控制query流程，统计每个session中的query的情况，再进行query测试
    :param table: excel名
    :param user_name: 租户名
    :param query_url: query接口
    :param engine: 对话引擎
    :return: all_cost_times 所有query的应答时间
    """
    # 存储所有query的应答时间
    all_cost_times = []
    # 存储所有session信息（session_num、）
    sessions_info = []
    row_length = len(table['session'])
    cur_row_index = 0
    while cur_row_index < row_length:
        # 统计当前session序号中有多少个query，并把query存入数组中
        last_row_index = cur_row_index
        # 当前session序号
        cur_session_num = table['session'][cur_row_index]

        # 越界检查，下一行的session序号
        if cur_row_index + 1 >= row_length:
            # 如果最后一行的session只有1个query
            cur_session_query_num = 1
            # 用于外围循环判断，防止死循环
            cur_row_index += 1
        else:
            # 下一行的session序号
            cur_row_index += 1
            next_session_num = table['session'][cur_row_index]
            # 如果下一个session序号与当前session序号相同，则认为当前session中query个数为1
            while cur_session_num == next_session_num and cur_row_index < row_length:
                cur_row_index += 1
                # 越界检查，最后一个session中有多个query时
                if cur_row_index >= row_length:
                    break
                next_session_num = table['session'][cur_row_index]
            # 当前session中query个数
            cur_session_query_num = cur_row_index - last_row_index
        query_array = []
        for i in range(0, cur_session_query_num):
            query_array.append(table['query'][last_row_index + i])

        print("当前session【{}】的query列表为：【{}】".format(cur_session_num, query_array))
        # 开始测试当前session中的query，并统计每个query的应答时间
        session_id, cost_times, answer_texts, query_start_times, query_end_times = session_query(
            query_array,
            user_name=user_name,
            query_url=query_url,
            engine=engine
        )
        print("cost_times：{}，单位：ms".format(cost_times))

        # 存储当前session中所有query的应答时间至全局数组中
        all_cost_times.extend(cost_times)
        # 存储当前session的信息
        sessions_info.append(
            {
                "session_num": cur_session_num,
                "session_id": session_id,
                "cost_times": cost_times,
                "query_array": query_array,
                "answer_texts": answer_texts,
                "query_start_times": query_start_times,
                "query_end_times": query_end_times
            }
        )

        # session间间隔5秒
        time.sleep(5)

    print("all_cost_times：{}，单位：ms".format(all_cost_times))
    return all_cost_times, sessions_info


def generate_html_table(query_url, engine, file_name, sheet_name, user_name, agent_id,
                        model_type, scene, total_query_num, avg_time, P90_time, P99_time, sessions_info):
    """
    生成html表格，展示会话记录数据
    :param query_url: 域名，区分是 keyue-test.cloud.baidu.com 还是 keyue.cloud.baidu.com
    :param engine: 测试使用的对话引擎，如：faq、doc、task
    :param file_name: 测试使用的excel文件名
    :param sheet_name: sheet名
    :param user_name: 测试使用的租户名
    :param agent_id: 测试使用的agent_id
    :param model_type: 测试使用的模型类型
    :param scene: 测试使用的场景，如：文档问答 + offline
    :param total_query_num: 所有query的总数
    :param avg_time: 所有query的平均首字响应时延
    :param P90_time: 所有query的90%分位数
    :param P99_time: 所有query的99%分位数
    :param sessions_info: 所有session的信息（sessionId、首字时延、query起始时刻、首字响应时刻、query、首字响应答案）
    :return:
    """
    html = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><title>客悦时延效果报告</title></head>"
    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0'><tr>\
                <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
                <strong>客悦专业版【{scene}】首字响应时延报告，user_name【{user_name}】，agent_id【{agent_id}】<br>\
                模型版本【{model_type}】，数据表excel名【{file_name}】，sheet名【{sheet_name}】，query_url【{query_url}】<br><br>\
                【{engine}】场景的共【{total_query_num}】条query，平均首字响应时长为【<span style=\"color: red;\">\
                {round(avg_time, 2)}</span>】ms，\
                P90：【<span style=\"color: red;\">{round(P90_time, 2)}</span>】ms，\
                P99：【<span style=\"color: red;\">{round(P99_time, 2)}</span>】ms\
                </strong></td></tr>\
            <tr bgcolor='yellow'>\
                <td width='auto' align='center'><strong>测试场景</strong></td>\
                <td width='auto' align='center'><strong>query以及结果（标红表示响应时长超过3s，黄底表示回复内容为空）</strong></td></tr>"

    for row in sessions_info:
        for i in range(0, len(row.get('cost_times'))):
            html += "<tr>"
            query = row.get('query_array')[i]
            cost_time = row.get('cost_times')[i]
            first_answer_text = row.get('answer_texts')[i]
            query_start_time = row.get('query_start_times')[i]
            query_end_time = row.get('query_end_times')[i]

            # 展示sessionId的列只展示一次
            if i == 0:
                col1_text = "第【{}】个session，session_id:【{}】".format(row.get('session_num'), row.get('session_id'))
                html += f"<td rowspan='{len(row.get('cost_times'))}'>{col1_text}</td>"

            col2_text = "第【{}】轮 === query:【{}】，首字响应结果：【{}】\
                        <br>query起始时刻:【{}】\
                        <br>query首字响应时刻:【{}】\
                        <br><strong>首字响应时长：【{}】ms</strong>" \
                .format(i + 1, query, first_answer_text, query_start_time, query_end_time, round(cost_time, 2))

            # 首字响应时长超过3s，标红字；首字响应结果为空，标黄底
            if cost_time > 3000.0 and first_answer_text == '':
                html += f"<td style='color:red' bgcolor='yellow'>{col2_text}</td>"
            elif cost_time <= 3000.0 and first_answer_text == '':
                html += f"<td style='color:black' bgcolor='yellow'>{col2_text}</td>"
            elif cost_time > 3000.0 and first_answer_text != '':
                html += f"<td style='color:red'>{col2_text}</td>"
            else:
                html += f"<td style='color:black'>{col2_text}</td>"
            html += "</tr>"
    html += "</table></body></html>"
    return html


def main():
    """
    主入口
    :return:
    """
    # 选择要测试的租户
    user_name = args.userName  # eb3.5

    print("====== 开始测试租户【{}】下【{}场景】的【{}】corequery ======".format(user_name, args.typeHost, args.engine))
    file_name = ""
    sheet_names = []
    scene = ""
    if "docQa" == args.engine:
        scene = "文档问答"
        # 读取测试表格数据
        file_name = "./zhengshuju-docQa.xlsx"
        # 表格中sheet名
        sheet_names = [
            "政数局"
        ]
    elif "faq" == args.engine:
        scene = "FAQ"
        # 读取测试表格数据
        file_name = "./car-faq.xlsx"
        sheet_names = [
            "汽车"
        ]
    elif "task_based" == args.engine:
        scene = "任务式对话"
        if args.taskBasedScene == "dianlifuwu":
            # 读取测试表格数据
            file_name = "./dianlifuwu-task_based.xlsx"
            # 表格中sheet名
            sheet_names = [
                "电力查询"
            ]
        elif args.taskBasedScene == "chengliangguanjia":
            # 读取测试表格数据
            file_name = "./chengliangguanjia-task_based.xlsx"
            # 表格中sheet名
            sheet_names = [
                "车辆小管家"
            ]
    elif "tableQa" == args.engine:
        scene = "表格问答"
        if args.taskBasedScene == "wuliu":
            # 读取测试表格数据
            file_name = "./tableQa_resp_delay_wuliu.xlsx"
            # 表格中sheet名
            sheet_names = [
                "物流"
            ]
        elif args.taskBasedScene == "zhuimi":
            # 读取测试表格数据
            file_name = "./tableQa_resp_delay_zhuimi.xlsx"
            # 表格中sheet名
            sheet_names = [
                "追觅"
            ]


    # 读取并解析存储query的表格数据
    tables = parse_excel(file_name, sheet_names)
    print("读取表格数据：【{}】成功。".format(file_name))
    for query_url in env_array:
        for sheet_name in sheet_names:
            print("开始测试表格：【{}】sheet：【{}】的每条query应答时间".format(file_name, sheet_name))
            all_query_times, sessions_info = query_control(
                tables[sheet_name],
                user_name=user_name,
                query_url=query_url,
                engine=args.engine
            )
            # 总耗时
            sum_query_time = 0
            # 有效query数，要排除-1的query，因为-1表示query超时等其他网络问题
            sum_valid_query = 0
            for query_time in all_query_times:
                if query_time == -1:
                    continue
                else:
                    sum_valid_query += 1
                    sum_query_time += query_time
            # 平均应答时间
            avg_query_time = sum_query_time / sum_valid_query

            # P90指标
            print(avg_query_time)
            P90_time = np.percentile(all_query_times, 90)
            print("P90：【{}】ms".format(P90_time))

            # P99指标
            print(avg_query_time)
            P99_time = np.percentile(all_query_times, 99)
            print("P99：【{}】ms".format(P99_time))

            print("表格:【{}】的sheet：【{}】共有【{}】条query，只有【{}】条query请求有效，平均应答时间：【{}】ms".format(
                file_name,
                sheet_name,
                len(all_query_times),
                sum_valid_query,
                avg_query_time)
            )

            # 生成html代码
            html_content = generate_html_table(query_url=query_url,
                                               file_name=file_name,
                                               sheet_name=sheet_name,
                                               user_name=user_name,
                                               agent_id=base_info.get(user_name).get("agentId"),
                                               model_type=args.modelType,
                                               scene=scene,
                                               total_query_num=len(all_query_times),
                                               avg_time=avg_query_time,
                                               P90_time=P90_time,
                                               P99_time=P99_time,
                                               engine=args.engine,
                                               sessions_info=sessions_info)

            # 将HTML代码写入文件
            try:
                with open(f"{args.typeHost}-report.html", "w", encoding='utf-8') as f:
                    f.write(html_content)
            except Exception as e:
                print(e)
            # sheet间间隔10秒
            time.sleep(10)

    print("====== 结束测试租户【{}】下【{}场景】的【{}】corequery ======".format(user_name, args.typeHost, args.engine))


if __name__ == '__main__':
    main()
