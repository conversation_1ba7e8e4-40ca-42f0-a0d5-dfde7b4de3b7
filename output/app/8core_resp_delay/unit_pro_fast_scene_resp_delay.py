# coding:utf8
'''
@author:dangchujia
'''
from datetime import datetime

import requests
import time
import openpyxl
import argparse
import json
import re
import uuid
import numpy as np

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('userName', help='租户名称')  # 租户名称
parser.add_argument('agentId', help='agent id')  # agent id
parser.add_argument('token', help='agent发布后的 token')  # agent发布后的 token
parser.add_argument('host', help='域名：keyue-test or keyue')  # keyue-test or keyue
parser.add_argument('typeHost', help='offline or online')  # offline or online
parser.add_argument('env', help='production or test')  # 生产环境 or 测试环境
parser.add_argument('qps', help='每秒发送多少个query')  # 每秒发送多少个query
parser.add_argument('modelType', help='大模型eb3.5 or 大模型eb4.0')  # 大模型eb3.5 or 大模型eb4.0
parser.add_argument('version', help='客悦专业版的当前版本号')  # 如1.3.x
parser.add_argument('agentScene', help='对话引擎：product_recommend、clue_collect')  # AI场景客服的测试场景，商品导购、线索搜集

# 解析命令行参数
args = parser.parse_args()

# 两个租户下各自的【新-时延效果测试-勿动】信息
base_info = {
    args.userName: {
        "agentId": args.agentId,
        "token": args.token
    }
}

offline_url = "https://{}/core/v5/stream/query".format(args.host)  # 流式
online_url = "https://{}/online/core/v5/stream/query".format(args.host)  # 流式

if args.typeHost == "offline":
    env_array = [offline_url]
elif args.typeHost == "online":
    env_array = [online_url]
else:
    env_array = [offline_url, online_url]

# 客悦专业版的当前版本号
version = args.version

# qps：每秒发送多少个query，设置为1，表示每秒发送一个query，设置为2表示每秒发送2个query
qps = int(args.qps)
# 控制qps
sleep_time = 1 / qps


def get_uuid():
    """
    获取uuid，用于生成唯一的sessionId
    :return:
    """
    return str(uuid.uuid4())


def session_query(query_array, user_name, query_url):
    """
    测试文档问答，每调用一次，表示一次session
    :param query_array: 一个session的query数组
    :param user_name: 租户名称
    :param query_url: query接口请求地址
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'uid': '**********',
        'username': 'dcj36513',
        'accountType': 'PASSPORT',
        'Token': token,
        'Cookie': 'bceAccountName=PASSPORT:**********;\
        bce-ctl-client-cookies="BDUSS,bce-passport-stoken,\
        bce-device-cuid,bce-device-token,BAIDUID"; \
        bce-ctl-sessionmfa-cookie=bce-session; \
                  bce-login-display-name=dcj36513; \
                  bce-userbind-source=PASSPORT%3BUUAP; \
                  bce-auth-type=PASSPORT; \
                  bce-login-type=PASSPORT; loginUserId=**********; \
                  Hm_lvt_2e94ac2e1bcde2839e431fe263a8762e=**********; \
                  BDUSS=U1QnJaYzdzVE5MVzlPSzVNbHJMYWtFOFZSWXJZa3JMd3lqaD\
                  BrZUVvV1AxdU5sSVFBQUFBJCQAAAAAAQAAAAEAAABN\
                  -CyCZGNqMzY1MTMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
                  AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9JvGWPSbxlUE; \
                  BDUSS_BFESS=U1QnJaYzdzVE5MVzlPSzVNbHJMYWtFOFZSWXJZa3\
                  JMd3lqaDBrZUVvV1AxdU5sSVFBQUFBJCQAAAAAAQAAAAEAAABN\
                  -CyCZGNqMzY1MTMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\
                  AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9JvGWPSbxlUE; \
                  bce-passport-stoken=23b50d75b85a0ed37d47e0488d8bfebf86\
                  9a5be992e132c3d9da784ca8f3c2d1; bce-sessionid=0014472e4\
                  ff8e7e411bb494a9632f2c81b1; bce-user-info=2024-02-02T09:46:57Z\
                  |79b4e04ced710ba4923d0e2438d150f8; bce-session=\
                  501b6fe9d6654c7bba8a5817506423ba4f56c6ac126e4\
                  a8f8fb9a490229cb46a|da754f4d1428a62e84b47d660e546f17;\
                   bce-login-expire-time="2024-02-02T02:16:57Z|9f93f5e27\
                   1e4dc178a06aa96e3da4611"; BAIDUID=D1F8B9E6B09E7B6A3BC\
                   829D817B986E7:FG=1; BAIDUID_BFESS=D1F8B9E6B09E7B6A3BC\
                   829D817B986E7:FG=1; Hm_lpvt_2e94ac2e1bcde2839e431fe2\
                   63a8762e=1706838435; Hm_lvt_2ffc4ed803ded123a8731727\
                   1c849548=1706498435,1706838465; Hm_lpvt_2ffc4ed803de\
                   d123a87317271c849548=1706838465; JSESSIONID=node0k09\
                   mel35wgi31ng1kov2huk3n72.node0'
    }
    params = {
        "Token": token
    }
    # 随机生成一个sessionId
    sessionId = get_uuid()

    # 存储一轮会话中各query的应答时间
    cost_times = []
    # 存储一轮会话中的响应答案text
    answer_texts = []
    # query的起始时间
    query_start_times = []
    # query的收到第一个Stream的时间
    query_end_times = []
    for query in query_array:
        time.sleep(sleep_time)
        json_data = {
            "queryText": query,
            "sessionId": sessionId,
            "agentId": agentId,
            "variables": {}
        }
        # 如果为online环境，则添加channel
        if query_url.find("online") > 0:
            json_data["channel"] = "_sys_web"

        # 当前query起始时间
        start_time = time.time()
        query_start_times.append(datetime.fromtimestamp(start_time).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])
        # 当前query结束时间
        end_time = start_time

        # 重试次数
        retry = 0
        retry_num = 3
        http_post = None
        while retry < retry_num:
            try:
                http_post = requests.post(url=query_url, params=params, json=json_data, headers=headers,
                                          stream=True, timeout=60)
                break
            except requests.ConnectionError as e:
                print(e)
                retry += 1
                time.sleep(1)

        # 请求失败，重试次数已达上限
        if http_post is None:
            print("query失败，重试次数已达上限")
            continue

        # 请求成功，开始解析应答流式数据
        for line in http_post.iter_lines():
            if line:
                # 将二进制数据流转为字符串
                line = line.decode('utf-8')
                if line.startswith('data:'):
                    # 获取到第一个Stream时，记录时间，退出
                    end_time = time.time()
                    query_end_times.append(datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])

                    # 拼接首字应答流式数据
                    answer_text = ''
                    # decode('utf-8')时可能会报错：'utf-8' codec can't decode byte 0x81 in position 0: invalid start byte
                    try:
                        res_contents = re.findall(r'data:(.*)', line)
                        for content in res_contents:
                            reply = json.loads(content)['answer'][0]['reply']
                            if reply is not None:
                                answer_text += reply.get('text')
                                break
                    except Exception as e:
                        print(e)

                    # 第一个Stream的应答为空就再进行一下一个Stream
                    if answer_text != '':
                        answer_texts.append(answer_text)
                        break
        # print("query开始时间：", start_time)
        # print("query收到第一个Stream时间：", end_time))

        # 请求有问题，标记本条query的应答时间为-1，表示无效，不计入平均值
        if end_time == start_time:
            cost_time = -1
            query_end_times.append(datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])
        else:
            # 计算当前query的应答时间，单位ms
            cost_time = (end_time - start_time) * 1000  # ms

        # 存储当前query的应答时间，单位ms
        cost_times.append(cost_time)

    return sessionId, cost_times, answer_texts, query_start_times, query_end_times


def parse_excel(excel_name, sheet_names):
    """
    解析excel表格数据，存储为字典+数组
    :param excel_name: excel文件名
    :param sheet_names: sheet名
    :return: tables对象，key为sheet名，value为表格数据
    """
    workbook = openpyxl.load_workbook(excel_name)
    tables = {}
    # 通过工作表名称选择当前活动的工作表
    for sheet_name in sheet_names:
        sheet = workbook[sheet_name]
        table = {}
        for row_index in range(1, sheet.max_row + 1):
            # 拿到表头数据名
            if row_index == 1:
                for col_index in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=1, column=col_index)
                    table[cell.value] = []
                continue
            # 存储第个表头下的所有数据，以表头为key，数据为value数组
            for col_index in range(1, sheet.max_column + 1):
                cell = sheet.cell(row=row_index, column=col_index)
                table[sheet.cell(row=1, column=col_index).value].append(cell.value)

        tables[sheet_name] = table
    return tables


def query_control(table, user_name, query_url):
    """
    根据表格内容，控制query流程，统计每个session中的query的情况，再进行query测试
    :param table: excel名
    :param user_name: 租户名
    :param query_url: query接口
    :return: all_cost_times 所有query的应答时间
    """
    # 存储所有query的应答时间
    all_cost_times = []
    # 存储所有session信息（session_num、）
    sessions_info = []
    row_length = len(table['session'])
    cur_row_index = 0
    while cur_row_index < row_length:
        # 统计当前session序号中有多少个query，并把query存入数组中
        last_row_index = cur_row_index
        # 当前session序号
        cur_session_num = table['session'][cur_row_index]

        # 越界检查，下一行的session序号
        if cur_row_index + 1 >= row_length:
            # 如果最后一行的session只有1个query
            cur_session_query_num = 1
            # 用于外围循环判断，防止死循环
            cur_row_index += 1
        else:
            # 下一行的session序号
            cur_row_index += 1
            next_session_num = table['session'][cur_row_index]
            # 如果下一个session序号与当前session序号相同，则认为当前session中query个数为1
            while cur_session_num == next_session_num and cur_row_index < row_length:
                cur_row_index += 1
                # 越界检查，最后一个session中有多个query时
                if cur_row_index >= row_length:
                    break
                next_session_num = table['session'][cur_row_index]
            # 当前session中query个数
            cur_session_query_num = cur_row_index - last_row_index
        query_array = []
        for i in range(0, cur_session_query_num):
            query_array.append(table['query'][last_row_index + i])

        print("当前session【{}】的query列表为：【{}】".format(cur_session_num, query_array))
        # 开始测试当前session中的query，并统计每个query的应答时间
        session_id, cost_times, answer_texts, query_start_times, query_end_times = session_query(
            query_array,
            user_name=user_name,
            query_url=query_url
        )
        print("cost_times：{}，单位：ms".format(cost_times))

        # 存储当前session中所有query的应答时间至全局数组中
        all_cost_times.extend(cost_times)
        # 存储当前session的信息
        sessions_info.append(
            {
                "session_num": cur_session_num,
                "session_id": session_id,
                "cost_times": cost_times,
                "query_array": query_array,
                "answer_texts": answer_texts,
                "query_start_times": query_start_times,
                "query_end_times": query_end_times
            }
        )

        # session间间隔5秒
        time.sleep(5)

    print("all_cost_times：{}，单位：ms".format(all_cost_times))
    return all_cost_times, sessions_info


def generate_html_table(query_url, file_name, sheet_name, user_name, agent_id,
                        model_type, scene, fast_scene_query_time, sessions_info):
    """
    生成html表格，展示会话记录数据
    :param query_url: 域名，区分是 keyue-test.cloud.baidu.com 还是 keyue.cloud.baidu.com
    :param file_name: 测试使用的excel文件名
    :param sheet_name: sheet名
    :param user_name: 测试使用的租户名
    :param agent_id: 测试使用的agent_id
    :param model_type: 测试使用的模型类型
    :param scene: 测试使用的场景，如：商品导购场景
    :param fast_scene_query_time: 快捷场景的query时延
    :param sessions_info: 所有session的信息（sessionId、首字时延、query起始时刻、首字响应时刻、query、首字响应答案）
    :param all_query_times: 所有query的应答时间
    :return:
    """

    # 打印公共信息
    html = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><title>客悦时延效果报告</title></head>"
    html += f"<table border='2' bordercolor='black' cellspacing='0' cellpadding='0' \
    style='table-layout:fixed;word-break:break-all'><tr>\
            <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
            <strong>客悦专业版{version}【{scene}】场景首字响应时延报告，user_name【{user_name}】，agent_id【{agent_id}】<br>\
            模型版本【{model_type}】，数据表excel名【{file_name}】，sheet名【{sheet_name}】，query_url【{query_url}】\
            </strong></td></tr>"

    if scene == "商品导购场景":
        # 输出指标结果
        # 指标第一行：表头
        html += """<tr><td style="background-color:#efefef;border-color:inherit;border-style:solid;
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;font-weight:normal;
        overflow:hidden;padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">
        </td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;
font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;
text-align:center;vertical-align:top;word-break:normal">
query总数(条)</td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;
font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;
text-align:center;vertical-align:top;word-break:normal">
P90(ms)</td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;
font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;
text-align:center;vertical-align:top;word-break:normal">
P99(ms)</td>
<td style="background-color:#efefef;border-color:inherit;border-style:solid;border-width:1px;
font-family:Arial, sans-serif;font-size:14px;font-weight:bold;overflow:hidden;padding:10px 5px;
text-align:center;vertical-align:top;word-break:normal">
平均(ms)</td></tr>"""

        # 指标第二行：
        # 第一列：闲聊
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>闲聊</strong>"
        html += '</td>'
        # 第二列：闲聊 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('chitchat_query_num')}</strong>"
        html += '</td>'
        # 第三列：闲聊 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_chitchat')}</strong>"
        html += '</td>'
        # 第四列：闲聊 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_chitchat')}</strong>"
        html += '</td>'
        # 第五列：闲聊 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_chitchat')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 指标第三行：澄清
        # 第一列：澄清
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>澄清</strong>"
        html += '</td>'
        # 第二列：澄清 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('intent_clarify_query_num')}</strong>"
        html += '</td>'
        # 第三列：澄清 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_intent_clarify')}</strong>"
        html += '</td>'
        # 第四列：澄清 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_intent_clarify')}</strong>"
        html += '</td>'
        # 第五列：澄清 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_intent_clarify')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 指标第四行：推荐
        # 第一列：推荐
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>推荐</strong>"
        html += '</td>'
        # 第二列：推荐 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('product_recommend_query_num')}</strong>"
        html += '</td>'
        # 第三列：推荐 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_product_recommend')}</strong>"
        html += '</td>'
        # 第四列：推荐 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_product_recommend')}</strong>"
        html += '</td>'
        # 第五列：推荐 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_product_recommend')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 指标第五行：全部
        # 第一列：全部
        html += "<tr>"
        html += '<td style="background-color:#efefef;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>全部</strong>"
        html += '</td>'
        # 第二列：全部 总条数
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('all_query_num')}</strong>"
        html += '</td>'
        # 第三列：全部 P90
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P90_time_all')}</strong>"
        html += '</td>'
        # 第四列：全部 P99
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('P99_time_all')}</strong>"
        html += '</td>'
        # 第五列：全部 平均
        html += '<td style="background-color:#ffffff;border-color:inherit;border-style:solid;\
        border-width:1px;font-family:Arial, sans-serif;font-size:14px;overflow:hidden;\
        padding:10px 5px;text-align:center;vertical-align:top;word-break:normal">'
        html += f"<strong>{fast_scene_query_time.get('avg_time_all')}</strong>"
        html += '</td>'

        html += "</tr>"

        # 遍历所有session，打印query详情
        html += "<tr bgcolor='yellow'>\
            <td width='auto' align='center'><strong>测试场景</strong></td>\
            <td width='auto' align='center' colspan='6'><strong>query以及结果（标红表示响应时长超过3s，蓝底表示回复内容为空）</strong>\
            <br>每个session中，第1轮为「闲聊」，第2轮为「澄清」，第3轮为「推荐」</td></tr>"
        for row in sessions_info:
            for i in range(0, len(row.get('cost_times'))):
                html += "<tr>"
                query = row.get('query_array')[i]
                cost_time = row.get('cost_times')[i]
                first_answer_text = row.get('answer_texts')[i]
                query_start_time = row.get('query_start_times')[i]
                query_end_time = row.get('query_end_times')[i]

                # 展示sessionId的列只展示一次
                if i == 0:
                    col1_text = "第【{}】个session，session_id:【{}】".format(row.get('session_num'), row.get('session_id'))
                    html += f"<td rowspan='{len(row.get('cost_times'))}'>{col1_text}</td>"

                col2_text = "第【{}】轮 === query:【{}】，首字响应结果：【{}】\
                            <br>query起始时刻:【{}】\
                            <br>query首字响应时刻:【{}】\
                            <br><strong>首字响应时长：【{}】ms</strong>" \
                    .format(i + 1, query, first_answer_text, query_start_time, query_end_time, round(cost_time, 2))

                # 首字响应时长超过3s，标红字；首字响应结果为空，标黄底
                if cost_time > 3000.0 and first_answer_text == '':
                    html += f"<td style='color:red' bgcolor='aquamarine' colspan='5'>{col2_text}</td>"
                elif cost_time <= 3000.0 and first_answer_text == '':
                    html += f"<td style='color:black' bgcolor='aquamarine' colspan='5'>{col2_text}</td>"
                elif cost_time > 3000.0 and first_answer_text != '':
                    html += f"<td style='color:red' colspan='5'>{col2_text}</td>"
                else:
                    html += f"<td style='color:black' colspan='5'>{col2_text}</td>"
                html += "</tr>"
    html += "</table>"
    html += "</body></html>"
    return html


def time_calculate_production_recommend(all_query_times):
    """
    计算快捷场景-商品导购的时延
    :param all_query_times: 所有query的应答时间
    :return:
    """

    """
    关于快捷场景，以商品导购场景为例，说明如下：
    每个session有3轮query，每轮query分别触发：闲聊、澄清、推荐
    在daogouchangjing.xlsx文件中，总共准备了10个session
    在计算时延时，会统计所有query的平均时延、P90、P99指标
    然后会分别统计闲聊、澄清、推荐的平均时延、P90、P99指标

    注：前提是所有query最后都能返回结果，且结果不为空
    """

    # 整理出有效的 闲聊、澄清、推荐、全部 的基础响应时延
    chitchat_query_time = []
    intent_clarify_query_time = []
    product_recommend_query_time = []
    valid_all_query_time = []
    for i in range(0, len(all_query_times)):
        time = all_query_times[i]
        if time == -1:
            continue
        else:
            if i % 3 == 0:
                chitchat_query_time.append(time)
            elif i % 3 == 1:
                intent_clarify_query_time.append(time)
            else:
                product_recommend_query_time.append(time)

            valid_all_query_time.append(time)

    # P90指标
    P90_time_chitchat = round(np.percentile(chitchat_query_time, 90), 2)
    print("闲聊场景P90：【{}】ms".format(P90_time_chitchat))
    P90_time_intent_clarify = round(np.percentile(intent_clarify_query_time, 90), 2)
    print("澄清场景P90：【{}】ms".format(P90_time_intent_clarify))
    P90_time_product_recommend = round(np.percentile(product_recommend_query_time, 90), 2)
    print("推荐场景P90：【{}】ms".format(P90_time_product_recommend))
    P90_time_all = round(np.percentile(valid_all_query_time, 90), 2)
    print("所有场景P90：【{}】ms".format(P90_time_all))

    # P99指标
    P99_time_chitchat = round(np.percentile(chitchat_query_time, 99), 2)
    print("闲聊场景P99：【{}】ms".format(P99_time_chitchat))
    P99_time_intent_clarify = round(np.percentile(intent_clarify_query_time, 99), 2)
    print("澄清场景P99：【{}】ms".format(P99_time_intent_clarify))
    P99_time_product_recommend = round(np.percentile(product_recommend_query_time, 99), 2)
    print("推荐场景P99：【{}】ms".format(P99_time_product_recommend))
    P99_time_all = round(np.percentile(valid_all_query_time, 99), 2)
    print("所有场景P99：【{}】ms".format(P99_time_all))

    # 平均应答时间
    avg_time_chitchat = round(np.mean(chitchat_query_time), 2)
    print("闲聊场景平均应答时间：【{}】ms".format(avg_time_chitchat))
    avg_time_intent_clarify = round(np.mean(intent_clarify_query_time), 2)
    print("澄清场景平均应答时间：【{}】ms".format(avg_time_intent_clarify))
    avg_time_product_recommend = round(np.mean(product_recommend_query_time), 2)
    print("推荐场景平均应答时间：【{}】ms".format(avg_time_product_recommend))
    avg_time_all = round(np.mean(valid_all_query_time), 2)
    print("所有场景平均应答时间：【{}】ms".format(avg_time_all))

    product_recommend_query_time = {
        "P90_time_chitchat": P90_time_chitchat,
        "P90_time_intent_clarify": P90_time_intent_clarify,
        "P90_time_product_recommend": P90_time_product_recommend,
        "P90_time_all": P90_time_all,
        "P99_time_chitchat": P99_time_chitchat,
        "P99_time_intent_clarify": P99_time_intent_clarify,
        "P99_time_product_recommend": P99_time_product_recommend,
        "P99_time_all": P99_time_all,
        "avg_time_chitchat": avg_time_chitchat,
        "avg_time_intent_clarify": avg_time_intent_clarify,
        "avg_time_product_recommend": avg_time_product_recommend,
        "avg_time_all": avg_time_all,
        "chitchat_query_num": len(chitchat_query_time),
        "intent_clarify_query_num": len(intent_clarify_query_time),
        "product_recommend_query_num": len(product_recommend_query_time),
        "all_query_num": len(valid_all_query_time)
    }

    return product_recommend_query_time


def main():
    """
    主入口
    :return:
    """
    # 选择要测试的租户
    user_name = args.userName  # eb3.5

    print("====== 开始测试租户【{}】下【{}】的【{}】场景的corequery ======".format(user_name, args.typeHost, args.agentScene))
    file_name = ""
    sheet_names = []
    scene = ""
    if "product_recommend" == args.agentScene:
        scene = "商品导购场景"
        # 读取测试表格数据
        file_name = "./daogouchangjing.xlsx"
        # 表格中sheet名
        sheet_names = [
            "导购场景"
        ]

    # 读取并解析存储query的表格数据
    tables = parse_excel(file_name, sheet_names)
    print("读取表格数据：【{}】成功。".format(file_name))
    for query_url in env_array:
        for sheet_name in sheet_names:
            print("开始测试表格：【{}】sheet：【{}】的每条query应答时间".format(file_name, sheet_name))
            all_query_times, sessions_info = query_control(
                tables[sheet_name],
                user_name=user_name,
                query_url=query_url
            )

            html_content = ""
            if args.agentScene == "product_recommend":
                product_recommend_query_time = time_calculate_production_recommend(all_query_times)

                # 生成html代码
                html_content = generate_html_table(
                    query_url=query_url,
                    file_name=file_name,
                    sheet_name=sheet_name,
                    user_name=user_name,
                    agent_id=base_info.get(user_name).get("agentId"), model_type=args.modelType,
                    scene=scene,
                    fast_scene_query_time=product_recommend_query_time,
                    sessions_info=sessions_info)

            # 将HTML代码写入文件
            try:
                with open(f"{args.typeHost}-report.html", "w", encoding='utf-8') as f:
                    f.write(html_content)
            except Exception as e:
                print(e)
            # sheet间间隔10秒
            time.sleep(10)

    print("====== 结束测试租户【{}】下【{}场景】的【{}】corequery ======".format(user_name, args.typeHost, args.agentScene))


if __name__ == '__main__':
    main()
