#!/usr/bin/env python
# coding=utf-8
########################################################################
#
# Copyright (C) Baidu Ltd. All rights reserved. 
#
########################################################################

'''
Description: 
Autor: <EMAIL>
Date: 2024-02-29 14:16:00
LastEditors: <EMAIL>
LastEditTime: 2024-04-29 17:41:25
'''
import re
import sys
import requests
import json
import time
import copy
import pandas as pd
from tqdm import tqdm

class YiYan(object):
    """YiYan large language models."""

    # eb-4.0
    base_url = 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token='
    
    ##eb-speed
    # base_url = 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/fgbrnzd1_unit?access_token='

    # cs token
    access_token = "24.d177f4b906779166e7be7b8872ad4619.2592000.1718936987.282335-45284004"


    temperature: float = 1e-5
    top_p: float = 0.0
    # temperature: float = 0.8
    # top_p: float = 0.8

    penalty_score: float = 1.0
    streaming: bool = False
    max_retries: int = 3

    def chat_request(self, prompt, retries=5):
        """
        请求一言的接口
        """
        headers = {'Content-Type': 'application/json'}
        # 调用API：
        payload = {
            "temperature": self.temperature,
            "top_p": self.top_p,
            "messages": [{
                "role": "user",
                "content": prompt,
            }],
        }
        # print(prompt)
        url = self.base_url + self.access_token
        # t1 = time.time()
        # response = requests.request("POST", url, headers=headers, json=payload, stream=self.streaming)
        # print(response.text)
        # cnt = 1
        # for line in response.iter_lines():
        #     print(line.decode('UTF-8'))
        #     if cnt == 1:
        #         t2 = time.time()
        #         print('first token time cost: ', t2-t1)
        #     cnt += 1
            
        # t3 = time.time()
        # print('all time cost: ', t3-t1)
        for _ in range(retries):
            try:
                t1 = time.time()
                response = requests.request("POST", url, headers=headers, json=payload, stream=self.streaming)
                t2 = time.time()
                # print('time cost: ', t2-t1)
                # print(response.text)
                result = json.loads(response.text)
                # speed
                # result_res = result["result"] 
                # eb4
                result_res = result["result"]
                result_res = result_res.strip('```json\n').strip('\n```')
                #return result["result"]
                return result_res
            except Exception as err:
                print("Error:", err)
                time.sleep(1)
                continue
        return ''

## 实体抽取
def evaluate_slot_filling(model, golden_file):
    """评估槽位收集"""
    data = pd.read_excel(golden_file)
    prompt = """
你是一位线上客服专家，擅长根据对话内容抽取用户办理业务的所需信息。我将给你当前时间、需要收集的信息和一段用户与客服的对话内容，请你按照下述要求抽取信息。抽取结果的输出格式为：{{"信息名称1": "抽取结果1", "信息名称2": "抽取结果2"}}。

信息抽取要求如下：
1、抽取结果必须按照【需要收集的信息】列出的顺序依次输出，不得遗漏和添加额外信息
2、如果用户的回复和问题不相关，抽取结果填写“不相关”
3、如果用户对已经给出的信息作出否定，抽取结果填写“擦除”
4、对于有候选项的信息，抽取结果填写候选项全称
5、对于有候选项的信息，如果用户回复的信息不在选项内，但和选项内容属于相同类别，此时应该抽取
6、对于时间类信息，准确的时间描述如“后天”、“下周二”等直接抽取，模糊的时间表述如“住5天”、“3天后”、“一小时后”等输出基准时间+偏移量

请根据上述要求，输出下述对话内容的抽取结果。
###当前时间###
{}
###需要收集的信息###
{}
###对话内容###
{}
###输出###
    """
    tp = 0
    fp = 0
    fn = 0
    for i in tqdm(range(len(data))):
        tmp_fp = 0
        tmp_fn = 0
        slot = data['slot'][i]
        dialog = data['dialog'][i]
        cur_time = str(data['time'][i])
        response = eval(data['response'][i])
        tmp_prompt = prompt.strip().format(cur_time, slot, dialog)
        tmp_prompt = tmp_prompt.strip()
        # print(tmp_prompt)
        # print(response)
        ori_pred = model.chat_request(tmp_prompt.strip())
        pred = json.loads(ori_pred.strip().split('\n')[-1])
        for k, v in response.items():
            if v:
                if v == pred.get(k, ''):
                    tp += 1
                else:
                    if pred.get(k, ''):
                        fp += 1
                        tmp_fp += 1
                    fn += 1
                    tmp_fn += 1
            else:
                if pred.get(k, ''):
                    fp += 1
                    tmp_fp += 1
        if tmp_fn > 0 or tmp_fp > 0:
            print(tmp_prompt)
            print('golden: ', response)
            print('pred: ', ori_pred)
            print(tmp_fn, tmp_fp)
            print()
            print()
    p = tp / (fp + tp + 1e-13)
    r = tp / (fn + tp + 1e-13)
    f1 = (2 * p * r) / (p + r + 1e-13)
    print('槽位收集: P: {:.4f}, R:{:.4f}, F1:{:.4f}'.format(p, r, f1))
    print('TP: {}, FP: {}, FN: {}'.format(tp, fp, fn))


def evaluate_intent(model, test_file):
    """评估意图识别效果"""
    cnt_1 = 0
    cnt_2 = 0
    cnt_0 = 0
    for line in tqdm(open(test_file)):
        if not line.strip():
            continue
        item = json.loads(line.strip())[0]
        prompt = item['prompt']
        response = json.loads(item['response'][0][0])[0]
        pred = json.loads(model.chat_request(prompt.strip()))[0]
        if response['意图名称'] == pred['意图名称']:
            if response['是否需要澄清'] == pred['是否需要澄清']:
                cnt_2 += 1
            else:
                cnt_1 += 1
                print(prompt)
                print('gold: ', response)
                print('pred: ', pred)
                print('\n\n----------------------\n\n')
        else:
            cnt_0 += 1
            print(prompt)
            print('gold: ', response)
            print('pred: ', pred)
            print('\n\n----------------------\n\n')
    print('总数量：{}，高可用数量：{}，可用数量：{}，错误数量：{}'.format(cnt_1 + cnt_2 + cnt_0, cnt_2, cnt_1, cnt_0))
    print('高可用率：{}，可接受率：{}'.format(cnt_2 / (cnt_1 + cnt_2 + cnt_0), (cnt_2 + cnt_1) / (cnt_1 + cnt_2 + cnt_0)))

        
def evaluate_other_tasks(model):
    """评估信息澄清、实体引导问等6个任务"""

    input_filename_list = ['模糊澄清测试数据', '意图例句生成测试数据', '实体引导问测试数据', '实体值生成测试数据', '实体值同义词生成测试数据', 'FAQ相似问生成测试数据']
    for tmp_file_name in  input_filename_list:
        print('process: ', tmp_file_name)
        data = pd.read_excel('./test_data/' + tmp_file_name + '.xlsx')
        prompt_list = []
        response_list = []
        pred_list = []
        for i in tqdm(range(len(data))):
            prompt = data['prompt'][i]
            response = data['response'][i]
            pred = model.chat_request(prompt.strip())
            prompt_list.append(prompt)
            response_list.append(response)
            pred_list.append(pred)
        data = pd.DataFrame({"prompt": prompt_list, "response": response_list, "pred": pred_list})
        data.to_excel('./pred_data/' + tmp_file_name + '_pred.xlsx', index=None)

def gen_intent_cla(prompt, ori_file, model):
    """构建生成澄清意图话术的prompt

    Args:
        prompt (str): Prompt模版
        ori_file (file): 原始文件
        model (model): model
    """
    import re
    data = pd.read_excel(ori_file)
    data['cla_sentence'] = ''
    for i in tqdm(range(len(data))):
        is_cla = data['is_clarify'][i]
        query = data['query'][i]
        response = data['response'][i]
        if is_cla == '是':
            response = re.sub(r'^\d+\.\s+', '', response)
            tmp_prompt = prompt.format(query, response)
            cla_sentence = model.chat_request(tmp_prompt.strip())
            data['cla_sentence'][i] = cla_sentence
            print(tmp_prompt)
            print(cla_sentence)
            print('\n\n----------------------\n\n')
    data.to_excel('intent_data_cla.0428.xlsx', index=None)

def evaluate_intent_baisheng(ori_file, model):
    """评估百胜意图

    Args:
        ori_file (file): 原始文件
        model (model): model
    """
    cnt_1 = 0
    cnt_2 = 0
    cnt_0 = 0
    for line in tqdm(open(ori_file, 'r')):
        if not line.strip():
            continue
        item = json.loads(line.strip())[0]
        prompt = item['prompt']
        response = json.loads(item['response'][0][0])[0]
        pred = json.loads(model.chat_request(prompt.strip()))[0]
        # print(response)
        # print(pred)
        if response['意图名称'] == pred['意图名称']:
            if response['是否需要澄清'] == pred['是否需要澄清']:
                cnt_2 += 1
            else:
                cnt_1 += 1
                print(prompt)
                print('gold: ', response)
                print('pred: ', pred)
                print('\n\n----------------------\n\n')
        else:
            cnt_0 += 1
            print(prompt)
            print('gold: ', response)
            print('pred: ', pred)
            print('\n\n----------------------\n\n')
    print('总数量：{}，高可用数量：{}，可用数量：{}，错误数量：{}'.format(cnt_1 + cnt_2 + cnt_0, cnt_2, cnt_1, cnt_0))
    print('高可用率：{}，可接受率：{}'.format(cnt_2 / (cnt_1 + cnt_2 + cnt_0), (cnt_2 + cnt_1) / (cnt_1 + cnt_2 + cnt_0)))

def evaluate_intent_qa(intent_file, data_file, model):
    """评估qa构建的意图数据
    """
    prompt = """
你是一位意图识别专家，擅长根据历史对话识别用户当前Query所表达的的真实意图。我将给你一个意图列表，上面记录着意图的名称、解释和例句信息，请你根据这些信息识别用户意图，并按照如下格式输出结果：[{{"意图名称": "", "是否需要澄清": "", "澄清话术": ""}}]

请你遵循下述要求识别用户意图和输出结果：
1、如果用户的表述准确、无歧义地命中了意图列表中的某个意图，“意图名称”字段填写该意图，“是否需要澄清”字段填“否”，“澄清话术”字段置空
2、如果用户的表述与意图列表中的某个意图相关，但表述比较模糊，“意图名称”字段填写该意图，“是否需要澄清”字段填“是”，“澄清话术”字段填写针对该意图的澄清表述
3、如果用户的表述与意图列表中的意图都无关，但属于业务问题咨询范畴，“意图名称”字段填写“问题咨询”，“是否需要澄清”字段填“否”，“澄清话术”字段置空
4、如果用户的表述与意图列表中的意图都无关，并且不属于业务问题咨询范畴，“意图名称”字段填写“不相关”，“是否需要澄清”字段填“否”，“澄清话术”字段置空

###意图列表###
| 意图名称 | 意图解释 | 意图例句 |
| :--: | :--: | :--: |
{}
| 问题咨询 | 用户的表述与上述意图都无关，但属于业务问题咨询范畴 | - |
| 不相关 | 用户的表述与上述意图都无关，并且不属于业务问题咨询范畴 | - |

###历史对话###
{}

###用户当前Query###
{}

###输出结果###
    """
    intent_list = open(intent_file, 'r').read().strip().split('\n')
    intent_str = ''
    for item in intent_list:
        intent_str += '| ' + item.strip() + ' | ' + '-' + ' | ' + '-' + ' |' + '\n'
    intent_str = intent_str.strip()
    dialog = '空'
    for line in tqdm(open(data_file, 'r')):
        item = line.strip().split('\t')
        tmp_prompt = prompt.strip().format(intent_str, dialog, item[0])
        # speed
        pred = json.loads(model.chat_request(tmp_prompt.strip()))[0]
        print(pred['意图名称'])
        


        # print(tmp_prompt.strip())
        # print(pred)
        # print('-----------------------\n\n')
        # response = {"意图名称": response, "是否需要澄清": "否", "澄清话术": ''}

##围栏
def evaluate_entity_fence(ori_file, model):
    """评估实体围栏效果
    """
    prompt = """
你是一个善于做判断和信息澄清的客服专家，当前正在收集用户的信息，你需要根据目前已经收集的信息以及对应要求判断该信息是否需要澄清。如果需要澄清，请根据历史对话写一段澄清话术。结果按以下格式输出：
```json
{
    "思考过程": "",
    "信息": [],
    "是否需要澄清": [],
    "澄清话术": ""
}
```

###输出规定###
1. 仔细分析目前已经收集的信息和对应要求，不要考虑历史对话，判断信息是否需要澄清，并在【思考过程】字段输出你的分析思路
2. 对于收集的信息，如果满足要求，【是否需要澄清】字段填写"false"
3. 对于收集的信息，如果不满足要求，【是否需要澄清】字段填写"true"
4. 如果有需要澄清的信息，在【澄清话术】字段填写你的澄清内容，澄清内容不需要输出思考过程，不得发散，只需提醒用户的回复为什么不符合要求即可


下面是真实场景的对话，请根据上述要求对信息进行判断并输出结果。
    """
    data = pd.read_excel(ori_file)
    correct_cnt = 0
    wrong_cnt = 0
    for i in tqdm(range(len(data))):
        dialog = data['sample'][i].strip()
        # print(data['response'][i])
        response = json.loads(data['response'][i].strip().replace('```json', '').replace('```', ''))
        tmp_prompt = prompt.strip() + '\n' + dialog
        pred = json.loads(model.chat_request(tmp_prompt.strip()).strip().replace('```json', '').replace('```', ''))
        response_cla_list = response['是否需要澄清']
        pred_cla_list = pred['是否需要澄清']
        for i in range(len(response_cla_list)):
            if response_cla_list[i] == pred_cla_list[i]:
                correct_cnt += 1
                print(tmp_prompt)
                print('gold: ', response)
                print('pred: ', pred)
                print('\n\n----------------------\n\n')
            else:
                wrong_cnt += 1
                print(tmp_prompt)
                print('gold: ', response)
                print('pred: ', pred)
                print('\n\n----------------------\n\n')
    print('总数量：{}，正确率：{}'.format(correct_cnt + wrong_cnt, correct_cnt / (correct_cnt + wrong_cnt)))


def evaluate_chanrao(ori_file, model):
    """评估意图知识缠绕效果
    """
    prompt = """
你是一位优秀的客服专家，你需要根据历史对话判断用户当前Query与业务、标准问题和文档内容哪个最相关。结果按照如下格式输出：{{"最相关信息": ""}}，其中字段值从["业务", "标准问题", "文档问答"]中选择一个。

请注意以下几个要点：
1、判断与业务的相关程度时，请充分考虑业务的名称、描述和例句信息
2、判断与标准问题的相关程度时，请充分考虑问题和答案信息
3、判断与文档内容的相关程度时，请充分考虑根据文档内容的信息能否回答用户的Query

###业务###
| 名称 | 描述 | 例句 |
| :--: | :--: | :--: |
{}
###标准问题###
| 问题 | 答案 |
| :--: | :--: |
{}
###文档问答###
| 文档内容 |
| :--: |
{}


###历史对话###
{}

###用户当前Query###
{}

###输出结果###
    """
    data = pd.read_excel(ori_file)
    for i in tqdm(range(len(data))):
        dialog = '空'
        query = data['query'][i].strip()
        intent = '| ' + data['intent'][i].strip() + ' | - | - |'
        faq_question = data['question'][i].strip()
        faq_answer = data['answer'][i].strip()
        faq = '| ' + faq_question + ' | ' + faq_answer + ' |'
        document = '| - |'
        tmp_prompt = prompt.strip().format(intent, faq, document, dialog, query)
        # print(tmp_prompt)
        pred = json.loads(model.chat_request(tmp_prompt.strip()).replace('```json', '').replace('```', ''))['最相关信息']
        # pred = model.chat_request(tmp_prompt.strip())
        print(pred)

######改写
def evaluate_query_rewrite(ori_file, model):
    """评估query重写效果
    """
    prompt = """
请你扮演一个智能搜索改写补全机器人，请根据User的搜索历史以及对应的搜索结果，对最后一句话先进行主语继承改写，然后进行上下文信息补全，注意：不要改变原文的意思，答案要尽可能简洁，不要直接回答该问题，不要输出多余的内容。当搜索历史中只有一个User时，不要改变当前的问题。

例子：
搜索历史：
User：今天上午你干嘛了
Assistant：去打篮球啦
User：好玩吗？
答案：
打篮球好玩吗？


搜索历史：
{}
答案：
"""
    data = pd.read_excel(ori_file)
    for i in tqdm(range(len(data))):
        dialog = data['prompt'][i].strip()
        tmp_prompt = prompt.strip().format(dialog)
        # print(tmp_prompt)
        pred = model.chat_request(tmp_prompt.strip())
        # pred = model.chat_request(tmp_prompt.strip())
        print(pred)

if __name__ == "__main__":
    yiyan = YiYan()

    # 测试单条数据
    #prompt = open('sample', 'r').read().strip()
    #print(prompt)
    #response = yiyan.chat_request(prompt.strip())
    #print(response)
    # print(json.dumps(response, ensure_ascii=False))

    # gen_intent_cla(prompt, '../intent_new/intent_data.0428.xlsx', yiyan)

    # 评估多轮改写效果
    # evaluate_query_rewrite('./test_data/多轮改写带answer-testset-0725-0102-千帆res.xlsx', yiyan)

    # 评估意图知识缠绕prompt效果
    # evaluate_chanrao('./test_data/意图知识缠绕测试集-百胜.xlsx', yiyan)

    #评估qa构建的意图数据
    #evaluate_intent_qa('./baoxian.intent', './baoxian.data', yiyan)
    # evaluate_intent_qa('./edu.intent', './edu.data', yiyan)
    #evaluate_intent_qa('./dianshang.intent', './dianshang.data', yiyan)
    #evaluate_intent_qa('./car1.intent', './car1.data', yiyan) 
    evaluate_intent_qa('./tech.intent', './tech.data', yiyan)
    
    # 评估槽位识别
    # evaluate_slot_filling(yiyan, './test_data/槽位收集测试集-0510.xlsx')
    
    # evaluate_other_tasks(yiyan)

    # 评估通用领域意图识别效果
    # evaluate_intent(yiyan, '../intent_new/intent_sft_data.0428.test.jsonl')

    # 评估百胜意图识别效果
    # evaluate_intent_baisheng('./test_data/baisheng_intent_sft.test.0506.jsonl', yiyan)
    
    # 评估实体围栏效果
    # evaluate_entity_fence('./test_data/实体围栏测试集-0509.xlsx', yiyan)

    # 评估百胜意图识别效果
    # for line in open('./test_data/baisheng_intent_case.test.txt', 'r'):
    #     if not line.strip():
    #         continue
    #     item = line.strip().split('\t')
    #     # print(item)
    #     tmp_prompt = prompt.format(item[0])
    #     response = [{"意图名称": item[1], "是否需要澄清": "否", "澄清话术": ''}]
    #     ret = {"prompt": tmp_prompt, "response":[[json.dumps(response, ensure_ascii=False)]]}
    #     ret = json.dumps([ret], ensure_ascii=False)
    #     print(ret)
    #     pred = yiyan.chat_request(tmp_prompt.strip())
    #     pred = re.sub(r'^\d+\.\s+', '', pred)
    #     if pred == '闲聊-澄清':
    #         pred = '闲聊'
    #     if pred == item[1]:
    #         print(pred + '\t1')
    #     elif item[1] in pred:
    #         print(pred + '\t2')
    #     else:
    #         print(pred + '\t0')
