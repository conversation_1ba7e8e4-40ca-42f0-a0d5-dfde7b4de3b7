# -*- coding: UTF-8 -*-
################################################################################
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
本文件是登录接口。
"""

import os

from flask import Blueprint, request

register_service = Blueprint('register_service', __name__)

# 存储系统信息文件的路径
uploadFilePath = os.path.abspath(os.path.join(__file__, "../../loadFile/sysInfo/"))

register_bp = Blueprint('register_bp', __name__)
@register_bp.route('/registry/user/login', methods=['POST'])
def userLogin():
    """
    注册
    :return:
    """
    admin_token = {"roles": ['admin'], "introduction": 'I am a super administrator',
                   "avatar": 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
                   "name": "Super Admin"}
    res = {"code": 20000, "data": {"token": 'admin-token'}}
    return res


@register_bp.route('/registry/user/info', methods=['GET'])
def userInfo():
    """
    注册
    :return:
    """
    token = request.args.get('token')
    res = {"code": 20000,
           "data": {"roles": ["admin"], "introduction": "I am a super administrator",
                    "avatar": "https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif",
                    "name": "Super Admin"}}
    return res


@register_bp.route('/registry/user/logout', methods=['POST'])
def userLogout():
    """
    注册
    :return:
    """
    res = {"code": 20000, "data": "success"}
    return res