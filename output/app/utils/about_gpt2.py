#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from time import sleep

import openpyxl


from app.libs_ngd.large_model.request_large_model import RequestLargeModelObj
from config import LargeModelConfig

large_model_config = {
    "large_model_type": "gpt3.5",
    "api_key": "FCKKKWE27FTUL2RJ62",
    "prompt_template": """从现在开始，你需要扮演一个专业的评测人员，你需要根据问题和参考答案对模型输出准确性进行打分，打分的分档为0、1、2，具体的打分标准请参照分档描述和特殊规则，此外你还需要提供打分和对应的评分依据，你的回答请按照\n【得分】xxx\n【原因】xxx\n的格式输出，注意得分和原因之间要进行换行\n打分技巧是，首先结合问题提炼参考答案和模型输出的大体语义，然后对两者语义进行比较，按照【分档描述】和【特殊规则】进行判断\n【分档描述】如下：\n0:如果模型输出相较于参考答案语义完全不符或数据完全不正确，应当打0分；\n1:如果模型输出相较于参考答案表达的语义部分符合或者关键数据部分正确，应当打1分；\n2:如果模型输出相较于参考答案表达的语义符合、数据正确，应当打2分；\n【特殊规则】如下：\n1、如果参考答案没有表达出无法回答此问题的语义，但是模型输出表达了无法回答问题的语义，应当打0分\n2、如果参考答案表达出无法回答此问题的语义，模型输出中没有表达出无法回答此问题的语义，应当打0分\n3、如果模型输出相较于参考答案有部分内容缺失，请打1分\n4、模型输出有冗余没有关系，只要模型输出能覆盖标准答案的语义，就打2分，也就是说模型输出只能多说，不能少说\n5、模型输出表达顺序上和参考答案不一致，语义大体能对上，就打2分\n对于分档描述和特殊规则的优先级，特殊规则更高，如果一次打分，既满足分档描述中的某项又满足特殊规则中的某项，应该按照特殊规则进行打分\n在真正开始打分前，先学习7个专业评测人员打分的例子，例子中打分也是按照【分档描述】和【特殊规则】来的：\n【例1】\n问题是：请问鲁班先师木工艺陈列馆是每周几开放的？\n参考答案是：上午10时至下午6时（逢星期三休馆，澳门公众假期照常开放）\n模型输出是：无法回答此问题。鲁班先师木工艺陈列馆的开放时间，可以咨询景区服务人员。\n0：根据问题提炼语义，参考答案的语义是回答了具体的开放时间，模型的语义是无法回答此问题，但是提供了咨询的方法，命中特殊规则1，打0分\n【例2】\n问题是：大炮台陈列室有什么值得一看的？\n参考答案是：大炮台陈列室值得一看的物品有，用花岗岩砌成的地下建筑、用于陈列介绍澳门的各军事堡垒，尤其是大炮台的历史变迁和考古发掘的实物与资料。\n模型输出是：在军事术语中，大炮台是一个特定的地方，可以放置和发射重型火炮。它通常具有防御工事，可以保护武器不受敌方攻击。\n0：根据问题提炼语义，参考答案罗列出了大炮台陈列室值得一看的物品，模型输出的语义是对大炮台进行了介绍，语义完全不符，命中分档描述0分档\n【例3】\n问题是：海贼王中路飞的技能是什么？\n参考答案是：暂时无法回复您的问题。\n模型输出是：海贼王中路飞有橡胶手枪、橡胶战斧、橡胶火箭炮等技能。\n0：根据问题提炼语义，参考答案表述的语义是无法回答此问题，模型输出的语义是罗列了海贼王中路飞的部分技能，但是没有表达出无法回答此问题，命中特殊规则2，打0分\n【例4】\n问题是：西安市有什么值得一去的景点？\n参考答案是：西安市是全国著名旅游城市，大雁塔、大唐不夜城、兵马俑都值得一去。\n模型输出是：西安市是全国著名旅游城市，大雁塔、天安门、兵马俑都值得一去。\n1：根据问题提炼语义，模型输出相较于参考答案存在部分不符是天安门，天安门不是西安的景点，属于语义部分符合，命中分档描述的1分档\n【例5】\n问题是：apple公司的iPhone 15系列都有哪些型号？\n参考答案是：iPhone 15、iPhone 15 Plus、iPhone 15 Pro、iPhone 15 Pro Max\n模型输出是：iPhone 15、iPhone 15 Pro、iPhone 15 Pro Max\n1：根据问题提炼语义，参考答案表述的是iPhone 15系列型号有4款，模型输出表述的是iPhone 15系列型号有3款，缺失内容为iPhone 15 Plus，相较于参考答案有内容缺失，命中特殊规则3，打1分\n【例6】\n问题是：凤凰木赏花地点在哪？\n参考答案是：码头花园、水塘公园\n模型输出是：凤凰木赏花地点有码头花园和水塘公园。凤凰木由于花瓣形似羽毛轮廓，以鲜红色呈现，恰似浴火重生的凤凰，故名之。花期为5-7月。\n2：根据问题提炼语义，参考答案表述的是凤凰木赏花地点有码头花园、水塘公园，模型输出不但表述了凤凰木赏花地点，同时还介绍了凤凰木的特点，覆盖了标准答案的语义，命中特殊规则4，打2分\n【例7】\n问题是：澳门面积有多大\n参考答案是：澳门由澳门半岛（9.3平方公里）、氹仔（7.9平方公里）、路环（7.6平方公里）、路氹填海区（6.1平方公里）、新城A区（1.4平方公里）和港珠澳大桥珠澳口岸人工岛澳门口岸（0.7平方公里）组成，总面积共33.3  平方公里。\n模型输出是：澳门的陆地面积为33.3平方公里。澳门由澳门半岛（9.3平方公里）、氹仔（7.9平方公里）、路环（7.6平方公里）、路氹填海区（6.1平方公里）、新城A区（1.4平方公里）和港珠澳大桥珠澳口岸人工岛澳门口岸（0.7平方公里）组成。\n2：根据问题提炼语义，参考答案表述的是澳门总面积，以及各部分的面积，模型输出表述的也是澳门的总面积和各部分的面积的信息，虽然表达表达顺序上和参考答案不一致，但是语义大体能对上，命中特殊规则5，打2分\n学习完上述7个例子，请进行打分，现在给出\n【问题】{query}\n【参考答案】{reference_answer}\n【模型输出】{model_response}\n再次强调下【分档描述】和【特殊规则】\n【分档描述】如下：\n0:如果模型输出相较于参考答案语义完全不符或数据完全不正确，应当打0分；\n1:如果模型输出相较于参考答案表达的语义部分符合或者关键数据部分正确，应当打1分；\n2:如果模型输出相较于参考答案表达的语义符合、数据正确，应当打2分；\n【特殊规则】如下：\n1、如果参考答案没有表达出无法回答此问题的语义，但是模型输出表达了无法回答问题的语义，应当打0分\n2、如果参考答案表达出无法回答此问题的语义，模型输出中没有表达出无法回答此问题的语义，应当打0分\n3、如果模型输出相较于参考答案有部分内容缺失，请打1分\n4、模型输出有冗余没有关系，只要模型输出能覆盖标准答案的语义，就打2分，也就是说模型输出只能多说，不能少说\n5、模型输出表达顺序上和参考答案不一致，语义大体能对上，就打2分\n对于分档描述和特殊规则的优先级，特殊规则更高，如果一次打分，既满足分档描述中的某项又满足特殊规则中的某项，应该按照特殊规则进行打分\n注意，评分时请忽略模型输出中*、^、[1]、等占位符，占位符的出现不应当影响打分；同时换行，空格等格式问题也不应当对打分造成影响\n现在请你的回答请按照\n【得分】xxx\n【原因】xxx\n的格式输出，注意得分和原因之间要进行换行""",
    "pattern": LargeModelConfig.default_pattern
}
obj = RequestLargeModelObj(large_model_config)
excel_path = '/Users/<USER>/Downloads/国图测试集query（1622条）-result-比对的副本2.xlsx'
data_list = []
excel_file = openpyxl.load_workbook(excel_path)
sheet = excel_file.active
# 逐行读取工作表中的数据，跳过首行
for row in sheet.iter_rows(min_row=2, values_only=True):
    id = row[0]
    query = row[1]
    reference_answer = row[2]
    model_response = row[3]
    query_info = {
        'id': id,
        'prompt_dict': {
            'query': query,
            'reference_answer': reference_answer,
            'model_response': model_response
        }
    }
    data_list.append(query_info)
workbook = openpyxl.Workbook()
# 选择活动的工作表
sheet = workbook.active
for data in data_list:
    print(u'问题是：' + data['prompt_dict']['query'])
    print(u'参考答案是：' + data['prompt_dict']['reference_answer'])
    print(u'模型输出是：' + data['prompt_dict']['model_response'])
    if data['prompt_dict']['model_response'] == '#N/A':
        res_data = [data['id'], data['prompt_dict']['query'],
                    data['prompt_dict']['reference_answer'], data['prompt_dict']['model_response'],
                    '0', u'无返回', u'无返回']
    else:
        res = obj.request_large_model_and_parse_result(data['prompt_dict'])
        sleep(10)
        res_data = [data['id'], data['prompt_dict']['query'],
                    data['prompt_dict']['reference_answer'], data['prompt_dict']['model_response'],
                    res[0], res[1], res[2]]
    sheet.append([unicode(d).encode('utf-8') for d in res_data])
workbook.save('./new_res20.xlsx')