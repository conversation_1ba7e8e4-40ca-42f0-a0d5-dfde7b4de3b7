# coding:utf8
"""
@author:da<PERSON><PERSON><PERSON><PERSON>
通过请求core query接口，构造会记记录数据，需要agent提前构造FAQ数据并发布agent
"""
import json
import uuid
import requests
import time
import argparse

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('userName', help='租户名称')  # 租户名称
parser.add_argument('agentId', help='agent id')  # agent id
parser.add_argument('token', help='agent发布后的 token')  # agent发布后的 token
parser.add_argument('host', help='域名：keyue-test or keyue')  # keyue-test or keyue
parser.add_argument('typeHost', help='offline or online')  # offline or online
parser.add_argument('sessionNum', help='70000')  # session数量
parser.add_argument('queryNum', help='10')  # 每个session的query数量

# 解析命令行参数
args = parser.parse_args()

host = args.host
type_host = args.typeHost
user_name = args.userName
agent_id = args.agentId
token = args.token
session_num = int(args.sessionNum)
query_num = int(args.queryNum)

# 请求的url地址
if type_host == "offline":
    url = "{}/core/v5/query".format(host)  # 非流式
elif type_host == "online":
    url = "{}/online/core/v5/stream/query".format(host)  # 流式

# 租户下agentId和token信息
base_info = {
    user_name: {
        "agentId": agent_id,
        "token": token,
    },
}


def core_query(query, user_name, session_num, query_num):
    """
    core query接口请求
    :param query: query内容
    :param user_name: 用户名
    :param session_num: session数量
    :param query_num: 每个session的query数量
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'uid': '**********',
        'username': 'dcj36513',
        'accountType': 'PASSPORT',
        'Token': token,
        'Cookie': 'bceAccountName=PASSPORT:**********; bce-ctl-client-cookies="BDUSS,bce-passport-stoken,\
        bce-device-cuid,bce-device-token,BAIDUID"; bce-ctl-sessionmfa-cookie=bce-session; \
        bce-login-display-name=dcj36513;bce-userbind-source=PASSPORT%3BUUAP; bce-auth-type=PASSPORT;\
         bce-login-type=PASSPORT; loginUserId=**********; Hm_lvt_2e94ac2e1bcde2839e431fe263a8762e=**********;\
          BDUSS=U1QnJaYzdzVE5MVzlPSzVNbHJMYWtFOFZSWXJZa3JMd3lqaDBrZUVvV1AxdU5sSVFBQUFBJCQAAAAAAQAAAAEAAABN\
          -CyCZGNqMzY1MTMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9JvGWPSbxlUE; \
          BDUSS_BFESS=U1QnJaYzdzVE5MVzlPSzVNbHJMYWtFOFZSWXJZa3JMd3lqaDBrZUVvV1AxdU5sSVFBQUFBJCQAAAAAAQAAAAEAAABN\
          \-CyCZGNqMzY1MTMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAI9JvGWPSbxlUE; \
          bce-passport-stoken=23b50d75b85a0ed37d47e0488d8bfebf869a5be992e132c3d9da784ca8f3c2d1;\
           bce-sessionid=0014472e4ff8e7e411bb494a9632f2c81b1; \
           bce-user-info=2024-02-02T09:46:57Z|79b4e04ced710ba4923d0e2438d150f8; \
           bce-session=501b6fe9d6654c7bba8a5817506423ba4f56c6ac126e4a8f8fb9a490229cb46a\
           |da754f4d1428a62e84b47d660e546f17; bce-login-expire-time="2024-02-02T02:16:57Z|9f93f5\
           e271e4dc178a06aa96e3da4611"; BAIDUID=D1F8B9E6B09E7B6A3BC829D817B986E7:FG=1; \
           BAIDUID_BFESS=D1F8B9E6B09E7B6A3BC829D817B986E7:FG=1; \
           Hm_lpvt_2e94ac2e1bcde2839e431fe263a8762e=1706838435; \
           Hm_lvt_2ffc4ed803ded123a87317271c849548=1706498435,1706838465; \
           Hm_lpvt_2ffc4ed803ded123a87317271c849548=1706838465; \
           JSESSIONID=node0k09mel35wgi31ng1kov2huk3n72.node0'
    }
    params = {
        "Token": token
    }
    # 循环发送请求，模拟用户提问，共70000个sessionId，每个sessionId发送10次query
    for i in range(session_num):
        time.sleep(1)
        # 随机生成一个sessionId
        sessionId = str(uuid.uuid4())
        for j in range(query_num):
            json_data = {
                "engines": [
                    "faq",
                ],
                "queryText": query,
                "sessionId": sessionId,
                "channel": "_sys_web"
            }
            # 响应的答案内容
            text = ''
            try:
                http_response = requests.post(
                    url=url,
                    params=params,
                    json=json_data,
                    headers=headers,
                    stream=True
                )
                for line in http_response.iter_lines():
                    if line:
                        # 将二进制数据流转为字符串
                        line = line.decode('utf-8')
                        if line.startswith('data:'):
                            text += json.loads(line[5:])['answer'][0]['reply']['text']
            except Exception as e:
                print(e)

            print("第{}个session，sessionId:{}，第{}个query，query:{}，responseText:{}".format(
                i + 1,
                sessionId,
                j + 1,
                query,
                text)
            )
            time.sleep(0.1)


def main():
    """
    主函数入口
    :return:
    """
    print("开始构造会话记录")
    core_query("怎么充话费", user_name, session_num, query_num)
    print("会话记录构造完成")


if __name__ == '__main__':
    main()
