#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/03/21 11:30:00
"""
from app.generate_data.generateData import gpt

# 读取数据
f = open("querys.txt")
lines = f.readlines()
flag = 1
arr_querys = []
for line in lines:
    if line[-2] != "?" and line[-2] != "？":
        line = line[0:-1] + "?"
    line = line.replace('\n', '').replace('\r', '')
    arr_querys.append(line)
    flag = flag + 1
    if flag == 10:
        content = "以下10个句子: " + " ".join(arr_querys) + "每个句子返回5条口语化的中文的改写方式"
        print(content)
        gpt(content)
        flag = 1
        arr_querys = []
    line = f.readline()
f.close()
#
# 遍历数据访问gp

# 每条query询问10次
#
# 结果写回到txt
