#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2022/11/15 17:00:00
"""
import os

prefix = 'sqlite:////'
dir = os.path.dirname(os.path.abspath(__file__))
DB_URI = prefix + os.path.join(dir, 'db/evaluates.db')
print DB_URI
REDIS_URI = 'redis://@127.0.0.1:6379'
# REDIS_URI = 'redis://:Fa83FOSLE*2023@localhost:8211/0'


class Config(object):
    """
    config
    """
    SQLALCHEMY_DATABASE_URI = DB_URI
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    CELERY_BROKER_URL = REDIS_URI
    # CELERY_RESULT_BACKEND = 'db+'+DB_URI
    CELERY_RESULT_BACKEND = REDIS_URI
    CELERY_TRACK_STARTED = True
    CELERY_TASK_TIME_LIMIT = 1200


class FileAddress(object):
    """
    File
    """
    tableqa_agent = "./app/data/tableqa_data/戴姆勒表格问答单轮数据/梅赛德斯.json"
    tableqa_query = "./app/data/tableqa_data/戴姆勒表格问答单轮数据/total.xlsx"

    tableqa_agent_rounds = "./app/data/tableqa_data/700_效果agent.json"
    tableqa_query_rounds = "./app/data/tableqa_data/tableqa_query_700_rounds.xlsx"
    sys_entity_file = "./app/data/sys_entity_new.xlsx"
    sys_intents_file = "./app/data/sys_intents.xlsx"
    intent_file_bank = "./app/data/intent_10%_bank.xlsx"
    intent_file_airline = "./app/data/intent_10%_airline.xlsx"
    intent_file_communication = "./app/data/intent_10%_communication.xlsx"
    intent_file_operator = "./app/data/intent_10%_operator.xlsx"
    faq_file = "./app/data/faq_10%.xlsx"
    attitude_file = "./app/data/attitude.xlsx"
    # tableqa_query_rounds = "./app/data/tableqa_data/40w.xlsx"
    result_dir = "./data/result_dir"
    intent_agent_bank = "./app/data/效果测试_意图_银行_agent.json"
    intent_agent_airline = "./app/data/效果测试_意图_航空_agent.json"
    intent_agent_communication = "./app/data/效果测试_意图_通信_agent.json"
    intent_agent_operator = "./app/data/效果测试_意图_电信运营商_agent.json"
    faq_agent = "./app/data/效果测试_faq_agent_tenant.json"
    agent_file_path = "./app/data/效果验证.json"
    tqa_agent_file = "./app/data/tableqa_data/711_效果agent_tenant.json"
    # 置信度设置
    intentModelThreshold = 0.7


class LargeModelConfig(object):
    """
    大模型相关配置
    """
    default_pattern = "【得分】([012])\\n【原因】(.*)"
    api_key = "FC9QNBXU71PQSAXH00"
    sleep_time = 30
    template = "你是专业的评测人员，请根据问题和参考答案对模型输出进行准确性进行打分，分档0、1、2，" \
               "具体的打分标准请参照分档描述和注意事项，此外你还需要提供打分和对应的评分依据，你的回答请按照" \
               "【得分】xxx" \
               "【原因】xxx" \
               "的格式输出，注意得分和原因之间要进行换行" \
               "下面给出分档描述、问题、参考答案、模型输出以及注意事项" \
               "【分档描述】" \
               "0: 模型输出与参考答案语义和相关数据信息完全不符合" \
               "1:模型输出与参考答案语义和相关数据信息部分符合，但是允许存在不完整、冗余或者部分错误的情况" \
               "2:模型输出与参考答案语义和相关数据信息完全符合" \
               "【注意事项】" \
               "1.如果模型输出有关于来源的描述，例如如果模型中有'答案由一言生成'、'来源xxx'等内容，请将内容进行忽略" \
               "【问题】{query}" \
               "【参考答案】{reference_answer}" \
               "【模型输出】{model_response}"
