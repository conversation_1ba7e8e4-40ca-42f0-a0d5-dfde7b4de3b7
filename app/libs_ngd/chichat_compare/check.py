#!/usr/bin/python
# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/10/26
"""

from ..utils.core_query import CoreQuery
from ..utils.log import LOG
from ..utils.nlu_recognize import NluRecognize


class Check:
    """
    校验nlu与corequery的返回结果符合期望
    """

    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.conf = conf
        self.url = conf.url

    def checkResult(self, sQuery, expectResult):
        """
        :param sQuery: 用户输入的query
        :param expectResult: 解析excel得到的真实的标准问
        :return:
        """
        # 获取nlu分析结果，并校验
        nluRes = self.getNluResult(sQuery)
        bNluRes = True
        for i in range(len(expectResult)):
            if not expectResult[i] in nluRes:
                bNluRes = False
                LOG.error(
                    "sQuery: " + sQuery + ", nlu-result： " + str(nluRes) + ", expectResult: " + str(expectResult))

        # 获取corequery结果,并校验
        coreRes = self.getCoreQueryResult(sQuery)
        bCoreRes = True
        if coreRes != expectResult:
            bCoreRes = False
            LOG.error("sQuery: " + sQuery + ", nlu-result： " + str(nluRes) + ", expectResult: " + str(expectResult))

        result = bNluRes and bCoreRes
        return nluRes, coreRes, result

    def getNluResult(self, sQuery):
        """
        :param sQuery: 用户输入的query
        :return:
        """
        nluRecog = NluRecognize()
        nluResult = nluRecog.nlu_chitchat_recognize(sQuery, self.conf)
        print("====%s " % nluResult)
        nluRes = []
        try:
            for i in range(len(nluResult["data"]["list"])):
                standardQuestion = nluResult["data"]["list"][i]["chatStandardQuestion"]
                if standardQuestion not in nluResult:
                    nluRes.append(standardQuestion)
        except KeyError as e:
            LOG.error("访问 %s parseNluResult出错" % sQuery)
        return nluRes

    def getCoreQueryResult(self, sQuery):
        """
        :param sQuery: 用户输入的query
        :return:
        """
        corequery = CoreQuery(self.conf)
        coreResult = corequery.core_query(sQuery).json()

        coreRes = []
        try:
            detail = coreResult["data"]["debug"]["detail"]
            coreRes.append(detail["standardQuestion"])
            return coreRes
        except Exception as e:
            print(e)
            LOG.error("抛出异常：访问【%s】解析corequery结果出错" % sQuery)
