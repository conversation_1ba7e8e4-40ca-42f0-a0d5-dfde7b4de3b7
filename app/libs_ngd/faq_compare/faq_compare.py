#!/usr/bin/python
# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/10/26
"""

from openpyxl import Workbook


def save_faq_result(test_case_list, faqQueryCom, conf):
    """
    :param test_case_list:
    :param faq_query:
    :return:
    """
    output_wookbook = Workbook()
    # 选择第一个工作表
    # result_sheet = output_wookbook.active
    result_sheet = output_wookbook.create_sheet(index=0, title=u'测试')

    result_sheet.title = u"测试结果"

    result_sheet.cell(1, 1, "query")
    result_sheet.cell(1, 2, "真实的标准问")
    result_sheet.cell(1, 3, "说明")
    result_sheet.cell(1, 4, "nlu结果")
    result_sheet.cell(1, 5, "corequery结果")
    result_sheet.cell(1, 6, "测试通过")

    row_index = 0

    for test_case in test_case_list:
        sQuery = test_case["query"]  # 用户query
        sInfo = test_case["info"]

        # 获取期望值，真实的标准问
        sStandards = test_case["standard"]
        # expectResult = (str.split(sStandards, "\n"))
        expectResult = sStandards.split("\n")

        row_index += 1

        nluRes, coreRes, bResult = faqQueryCom.checkResult(sQuery, expectResult)

        result_sheet.cell(row_index + 1, 1, sQuery)
        result_sheet.cell(row_index + 1, 2, sStandards)
        result_sheet.cell(row_index + 1, 3, sInfo)
        result_sheet.cell(row_index + 1, 4, str(nluRes))
        result_sheet.cell(row_index + 1, 5, str(coreRes))
        result_sheet.cell(row_index + 1, 6, bResult)

    output_wookbook.save("./output/faq_compare_result.xlsx")
    output_wookbook.save("./display/files/faq_compare_result_%s.xlsx" % conf.ngd_version)

# if __name__ == '__main__':
# log.init_log("./log/nluCoreQueryCompare")
#
# reader = FaqCompareTestReader(conf.faq_compare_file)
# test_case_list = reader.read()
# print("====")
# print(test_case_list)
# LOG.info(test_case_list)
#
# faqQueryCom = FaqQueryCompare(conf)
# save_result(test_case_list, faqQueryCom)
