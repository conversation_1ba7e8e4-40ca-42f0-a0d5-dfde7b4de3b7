#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved

Authors: <AUTHORS>
Date: 2024/03/04 15:10:29
"""
from __future__ import unicode_literals
import requests
import os
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font
# import sys
import time

# reload(sys)
# sys.setdefaultencoding("utf-8")

class LargeModelImportSearch:
    """
    文档检索业务逻辑处理
    """
    def __init__(self, env_config, llm_search_config, data_file_path, task_id=''):
        self.env_config = env_config
        self.llm_search_config = llm_search_config
        self.data_file_path = data_file_path
        self.task_id = task_id

    def llm_import_documents(self):
        """
        导入文档
        1、读取训练集，在默认目录下开启训练，通过0。1。2选择数据行业
        2、0：河北税务局
        3、1：广东政数局
        4、2：澳门旅游局
        """
        try:
            file_path_get = self.data_file_path
            directory_path = os.path.dirname(file_path_get)

            import_faq_file_path = './app/data/llm_search/'

            if (self.llm_search_config["llm_search_industry"] == 0):
                file_path = import_faq_file_path + 'hebei/import'
            elif (self.llm_search_config["llm_search_industry"] == 1):
                file_path = import_faq_file_path + 'guangdong/import'
            elif (self.llm_search_config["llm_search_industry"] == 2):
                file_path = import_faq_file_path + 'macau/import'
            else:
                file_path = import_faq_file_path + 'hebei/import'

            files = os.listdir(file_path)
            headers = {
                "Cookie": self.llm_search_config['cookie'],
            }

            # 打印所有文件名
            msg_list = []
            for file_name in files:
                # 将文件名和目录路径拼接在一起
                full_path = os.path.join(file_path, file_name)
                if os.path.isfile(full_path):
                    # print(full_path)
                    url = self.env_config['backend_address'] + '/api/v2/task/create/llm_document_import' \
                          + '?dirId=0&lang=zh&agentId=' + str(self.env_config['agentId']) \
                          + "&tenantId=" + str(self.env_config['tenantId'])
                    # print(url)
                    file_data = open(full_path, 'rb').read()
                    data = {
                        'file': (file_name, file_data),
                        'taskExtend': ('', '{}')
                    }
                    response = requests.post(url, headers=headers, files=data)
                    # print(response)
                    # print(response.text)
                    msg = response.json()['msg']
                    msg_list.append(msg)
                    if msg == 'OK':
                        agentName = response.json()['data']['agentName']
                        result = agentName + ':' + '文档导入成功，请登陆任务中心查看'
                    else:
                        result = '文档导入失败，请联系管理员****************'
                    print(result)

            print(msg_list)
            print(len(set(msg_list)))
            if len(set(msg_list)) == 1:
                msg = '文档导入成功，请登陆任务中心查看'
            else:
                msg = '有文档任务导入失败，请联系管理员****************'
            return msg
        except Exception as ex:
            print(ex)
            # 触发异常，判断文件是否存在，如果存在将文件删除
            # if os.path.exists(dir):
            #     os.remove(dir)
            return None


    def search_documents(self, query):
        """
        检索文档
        :param query:
        :return:
        """
        # 调用list接口获取dataset_id
        data = {
            "dataset_id": self.llm_search_config['dataset_id'],
            "agent_id": self.env_config['agentId'],
            "version": 0,
            "top_k": 2,
            "threshold": self.llm_search_config['threshold'],
            "query": query,
            "labels": "",
            "dir_ids": []
        }
        headers = {"Content-Type": "application/json"}
        res = requests.post(self.env_config['knowledge_address'] + "/api/v8/document/search", headers=headers,
                            json=data)
        msg = res.json()['msg']
        # 解码返回的JSON数据，并转换为普通字符串
        res = res.json()['data']['data']
        if len(res) != 0 and msg == 'success':
            document_name_list = [row["document_name"]for row in res]
            sentence_content_list = [row["sentence_content"] for row in res]
            # document_name_list = [row["document_name"].encode('utf-8') for row in res]
            # sentence_content_list = [row["sentence_content"].encode('utf-8') for row in res]
            search_score_list = [row['score'] for row in res]
            document_name_res = '\n'.join(str(v) for v in document_name_list)
            sentence_content_res = '\n'.join(str(v) for v in sentence_content_list)
            search_score_res = '\n'.join(str(v) for v in search_score_list)
        else:
            document_name_res, sentence_content_res, search_score_res = '', '', ''
        return document_name_res, sentence_content_res, search_score_res


    def write_result_to_file(self):
        """
        将结果写入文件
        :param self:
        :return:
        """
        try:
            file_path_get = self.data_file_path
            directory_path = os.path.dirname(file_path_get)
            print('directory_path:::', directory_path)
            print(self.llm_search_config["llm_search_industry"])
            # 读取template文件，获取query以及预期结果
            if (self.llm_search_config["llm_search_industry"] == 0):
                file_path = directory_path + '/hebei/hebei_template.xlsx'
            elif (self.llm_search_config["llm_search_industry"] == 1):
                file_path = directory_path + '/guangdong/guangdong_template.xlsx'
            elif (self.llm_search_config["llm_search_industry"] == 2):
                file_path = directory_path + '/macau/macau_template.xlsx'
            else:
                file_path = directory_path + '/hebei/hebei_template.xlsx'

            print('开始读取模板文件')
            # 加载工作簿
            template = load_workbook(filename=file_path, data_only=True)
            # 获取sheet页
            template = template['检索结果']

            query_list = []
            real_doc_name_list = []
            real_doc_res_list = []
            for query in template['A']:
                query_list.append(query.value.encode('utf-8'))
            for val in template['C']:
                real_doc_name_list.append(val.value.encode('utf-8'))
            for val in template['E']:
                real_doc_res_list.append(val.value.encode('utf-8'))

            real_doc_name_list = real_doc_name_list[1:]
            real_doc_res_list = real_doc_res_list[1:]
            query_list = query_list[1:]
            print('模板文件数据读取完成')
            # 创建一个工作簿对象
            wb = Workbook()
            # 获取活动的工作表
            ws = wb.active
            ws = wb.create_sheet('检索结果', 0)
            col_name = ['query', '实际检索文档名称', '预期文档名称', '实际检索', '预期检索', 'search score', '得分', '原因', '备注']
            ws.append(col_name)

            obj = LargeModelImportSearch(self.env_config, self.llm_search_config, self.data_file_path, self.task_id)
            print('开始获取文档实际检索结果, 共需处理' + str(len(query_list)) + '条结果')
            print('Processing Data start!!' + str(len(query_list)) + ' in total!!')
            for i in range(0, len(query_list)):
                query = query_list[i].decode('utf-8')
                print("query", query)
                document_name_res = obj.search_documents(query)[0]
                sentence_content_res = obj.search_documents(query)[1]
                search_score_res = obj.search_documents(query)[2]
                ws.cell(row=i + 2, column=1).value = query
                ws.column_dimensions['A'].width = 30
                ws.cell(row=i + 2, column=2).value = document_name_res
                ws.cell(row=i + 2, column=3).value = real_doc_name_list[i].decode('utf-8')
                ws.column_dimensions['B'].width = 50
                ws.column_dimensions['C'].width = 50
                ws.cell(row=i + 2, column=4).value = sentence_content_res
                ws.cell(row=i + 2, column=5).value = real_doc_res_list[i].decode('utf-8')
                ws.column_dimensions['D'].width = 80
                ws.column_dimensions['E'].width = 80
                ws.cell(row=i + 2, column=6).value = search_score_res
                ws.column_dimensions['F'].width = 20
                print('【第' + str(i) + '个::】' + query)
                print('【第' + str(i) + '个::】' + document_name_res)
                print('【第' + str(i) + '个::】' + sentence_content_res)
                print('【第' + str(i) + '个::】' + search_score_res)

            print('文档实际检索结果获取完成，并存入文件')
            # 设置全部单元 自动换行
            for key in list(ws._cells.keys()): ws._cells[key].alignment = Alignment(wrapText=True)

            now_time = time.localtime()
            str_time_now = time.strftime("%Y%m%d%H%M%S", now_time)
            base_dir = './data/result_dir'
            dir = ''
            if not os.path.exists(base_dir):
                os.makedirs(base_dir)
            if (self.llm_search_config["llm_search_industry"] == 0):
                dir = base_dir + "/" + "hebei_result_%s.xlsx" % str_time_now
                wb.save(dir)
            elif (self.llm_search_config["llm_search_industry"] == 1):
                dir = base_dir + "/" + "guangdong_result_%s.xlsx" % str_time_now
                wb.save(dir)
            elif (self.llm_search_config["llm_search_industry"] == 2):
                dir = base_dir + "/" + "macau_result_%s.xlsx" % str_time_now
                wb.save(dir)
            else:
                dir = base_dir + "/" + "hebei_result_%s.xlsx" % str_time_now
                wb.save(dir)
            print('dir::llm' + dir)
            return dir
        except Exception as ex:
            print(ex)
            # 触发异常，判断文件是否存在，如果存在将文件删除
            # if os.path.exists(dir):
            #     os.remove(dir)
            return None


if __name__ == '__main__':
    cookie = 'UUAP_TRACE_TOKEN=183e270242ac3fe6f0a9b1847c1181de; crosssousername=ansiqi; ' \
             'AGL_USER_ID=e773fc33-4590-4d3b-9308-ef2df3724b06; Hm_lvt_20ba3a41aeedac500c94bdef787f57e6=1712912469;' \
             ' BIDUPSID=797F88A6036CEA9258AC433617EF43C9; PSTM=1714387944; BAIDUID=B5AB7497229BDC3A661B79518BC2B8D3:FG=1; ' \
             'H_WISE_SIDS=40446_40080_60141_60175_60188; BAIDUID_BFESS=B5AB7497229BDC3A661B79518BC2B8D3:FG=1; ' \
             'ZFY=Uzp9MSEn3Qt:Atbm:Aue5NOgFH78Iqzz6kGRtkECu7Yzc:C; H_WISE_SIDS_BFESS=40446_40080_60141_60175_60188; ' \
             '__bid_n=18ed188ed669648f19f7b4; jsdk-uuid=fb534e5e-d409-4d3e-bffc-8bf65c9a7bb3; ssousername=ansiqi; ' \
             'UUAP_P_TOKEN=PT-1005526815617245184-hKV7JdRUex-uuap; SECURE_UUAP_P_TOKEN=PT-1005526815617245184-hKV7JdRUex-uuap; ' \
             'jsdk-user=MG78kRjBffV+pRC9mD196g==; ' \
             'crosgwusercred=gvKeYoptJ0jWZcr6FQV21wFmuff_K6glmJpx16ni5c0cSCf0CYHuhf3ZjAU7KrGejdQ9wx8PuBPsI17ND_xeNg54981955e078fd9ab15233efbac846b5;' \
             ' casgwusercred=gGT0FRuoAka0WDhcbtaI7tEy1jCJXiB5Vq5ySdTBI8JgOusuNVy/SCeANBBE4BPua22MnrLEB34TNfK2HrNZ0qd+j0nWc1GrN4GvX8IeMAZqyfL/DsWz6IQUd/Ax5GUAKyd7e5sA27scIrGHt3zrYYlY1cvhqKeqBq4vr65kFm0=; ' \
             'CURRENT_PAGE=/article/2864549; Hm_lvt_28a17f66627d87f1d046eae152a1c93d=**********; ' \
             'Hm_lpvt_28a17f66627d87f1d046eae152a1c93d=**********; bce-sessionid=001f62abb11fe3b4895a8c209485981790a; ' \
             'bceAccountName=PASSPORT:**********; bce-ctl-client-cookies="BDUSS,bce-passport-stoken,bce-device-cuid,bce-device-token,BAIDUID"; ' \
             'bce-passport-stoken=5e07e4f4d98a13d7c3dfd44534bd7cbf51c906a0b75270d57f492d1e717c5d66; ' \
             'bce-user-info=2024-06-11T17:30:19Z|7cbf635862618e960bcef6ca306c4d3c; bce-ctl-sessionmfa-cookie=bce-session; ' \
             'bce-session=d426a63b0d0b4553be5e4c0d826b931558e62e9b2e124d218560291e17eb9bce|a0a84181518e72b642e4e43bcb6d054f;' \
             ' bce-login-display-name=asq_new1; bce-userbind-source=PASSPORT%3BUUAP; bce-auth-type=PASSPORT;' \
             ' bce-login-type=PASSPORT; bce-login-expire-time="2024-06-11T10:00:19Z|d1a3a9e13d64ebb96c36dcc437213889";' \
             ' loginUserId=**********; ' \
             'ab_sr=1.0.1_MWYwYzdiNzIyMWM2MTE4M2EwNzQyYmZjYTQ5Yjg1M2NkNjJiNmM1ZDYxYzhmYmFmNDA1NjgxZmQ0MDE4NjNkZDBkZm' \
             'JlNjVmM2M4NjUxMzFkNTA4MGMzYzA2ZjViMjIzY2EyOTRkZDEyZjhmNjhmZWI1YWU3MTYwNjQ2YmY3OWRiZWFkN2M2YzQ3NTM5ZDFlNjd' \
             'iMWY0ZjNhODg2NzU0MWE5YmQxNjdiYTZkYWEzYTMyNzQyMmUxNjQ5MjEyYzZkYjdiNDk2ZWNlY2U3YzNjZGU4MGI3NGNkZjg1ZWM1MGU=; ' \
             'RT="z=1&dm=baidu.com&si=e7e2e764-0625-4651-9c7c-6e462b85c4e4&ss=lxab8wwt&sl=0&tt=0&bcn=https%3A%2F%' \
             '2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf"'
    llm_search_config = {
        # 0: 河北, 1: 广东, 2: 澳门
        "llm_search_industry": 0,
        "threshold": 0.6,
        "dataset_id": '1',
        "cookie": cookie,
        "dir_id": '0'
    }
    env_config = {
        'knowledge_address': 'http://************:8889',
        'backend_address': 'http://************:8889',
        'agentId': '8a01b8bb-76a9-4418-8e79-1aaf2e1bb714',
        "tenantId": "34220452032000000",
    }

    data_file_path = './app/data/llm_search/'
    task_id = '12'
    obj = LargeModelImportSearch(env_config, llm_search_config, data_file_path, task_id)

    # 按照行业批量导入文档训练集，需要提前创建好agent，会导入进指定目录，默认目录为【默认】
    # obj.llm_import_documents()

    # 写入文档检索结果到excel文件
    obj.write_result_to_file()
