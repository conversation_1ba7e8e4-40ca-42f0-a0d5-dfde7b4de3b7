#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved

Authors: <AUTHORS>
Date: 2024/03/04 15:10:29
"""
from __future__ import unicode_literals
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
import os
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font
# import sys
import threading
import time
import json

# reload(sys)
# sys.setdefaultencoding("utf-8")


class FaqImportSearch:
    """
    faq检索业务逻辑处理
    """
    def __init__(self, env_config, faq_search_config, data_file_path, task_id=''):
        self.env_config = env_config
        self.faq_search_config = faq_search_config
        self.data_file_path = data_file_path
        self.res_data = []
        self.uuid_dict = {}
        self.lock = threading.Lock()
        self.task_id = task_id
        num_groups = self.faq_search_config['num_groups']
        self.executor = ThreadPoolExecutor(max_workers=num_groups)


    def search_faq(self, query):
        """
        faq检索
        :param query:
        :return:
        """
        data = {
            "faq_list": [
                {
                    "version": 0,
                    "dataset_id": self.faq_search_config['dataset_id'],
                    "agent_id": self.env_config['agentId'],
                    "rewrite_query": query,
                    "query": query,
                    "dir_list": [
                        "*"
                    ],
                    "min_score": self.faq_search_config['min_score'],
                    "max_score": self.faq_search_config['max_score'],
                    "top_k": self.faq_search_config['top_k_faq']
                }
            ]
        }
        headers = {"Content-Type": "application/json"}
        res = requests.post(self.env_config['knowledge_address'] + "/api/v8/faq/search/ref", headers=headers, json=data)
        msg = res.json()['msg']
        if msg == 'Success':
            # 解码返回的JSON数据，并转换为普通字符串
            res = res.json()['data']['answer']
            print(res)
            if len(res) != 0:
                if len(res[0]['answer_list']) != 0:
                    standard_question = res[0]['answer_list'][0]['standard_question']
                    score = res[0]['answer_list'][0]['score']
                else:
                    standard_question = '未查询到实际检索标准问'
                    score = 0
            else:
                standard_question = '未查询到实际检索标准问'
                score = 0
        else:
            standard_question = '接口请求异常，返回【 ' + msg + ' 】，未获取到实际检索标准问'
            score = 0
        return standard_question, score

    def get_faq_template_query_question(self):
        """
        获取faq模板文件中的查询词和标准问
        :return:
        """
        file_path = self.data_file_path
        print('开始读取模板文件')
        # 加载工作簿
        template = load_workbook(filename=file_path, data_only=True)
        # 获取sheet页
        template = template['Sheet1']

        query_list = []
        standard_question_list = []
        for query in template['A']:
            query_list.append(query.value.encode('utf-8'))
        for val in template['B']:
            standard_question_list.append(val.value.encode('utf-8'))

        standard_question_list = standard_question_list[1:]
        query_list = query_list[1:]
        print('faq模板文件数据读取完成')
        return query_list, standard_question_list

    # 在类的外面定义一个异步函数来执行 search_faq 方法
    @staticmethod
    def search_faq_async(obj, query):
        """
        异步调用search_faq方法
        :param obj:
        :param query:
        :return:
        """
        return obj.search_faq(query)

    @staticmethod
    def set_red_font(ws, row_index):
        """
        设置单元格字体为红色
        :param ws:
        :param row_index:
        :return:
        """
        # 设置字体颜色为红色
        red_font = Font(color="FF0000")
        # 获取行
        row = ws[row_index]
        # 设置每个单元格的字体颜色为红色
        for cell in row:
            cell.font = red_font

    def write_to_excel(self, ws, thread_name, start_index, end_index, query_list, standard_question_list):
        """
        将检索结果写入excel
        :param ws:
        :param thread_name:
        :param start_index:
        :param end_index:
        :param query_list:
        :param standard_question_list:
        :return:
        """
        obj = FaqImportSearch(self.env_config, self.faq_search_config, self.data_file_path, self.task_id)
        print("线程 %s 正在写入数据..." % thread_name)
        print('开始获取faq实际检索结果, 共需处理' + str(end_index - start_index) + '条结果')
        try:
            for i in range(start_index, end_index):
                query = query_list[i].decode('utf-8')

                print("query", query)
                # 使用异步方式调用 search_faq 方法
                future = self.executor.submit(self.search_faq_async, obj, query)
                standard_question_resp, score = future.result()
                score = obj.search_faq(query)[1]
                print('[啦啦啦啦' + str(i) + 'th:]' + str(query))
                print(self.faq_search_config['min_score'])
                print('[' + str(i) + 'th:]' + standard_question_resp)
                print(self.env_config['knowledge_address'])
                print('[' + str(i) + 'th:]' + str(score))
                ws.cell(row=i + 2, column=1).value = query
                ws.column_dimensions['A'].width = 30
                ws.cell(row=i + 2, column=2).value = standard_question_list[i]
                ws.cell(row=i + 2, column=3).value = standard_question_resp
                ws.column_dimensions['B'].width = 30
                ws.column_dimensions['C'].width = 30
                ws.cell(row=i + 2, column=4).value = score
                ws.column_dimensions['D'].width = 20
                if standard_question_list[i] == standard_question_resp:
                    ws.cell(row=i + 2, column=5).value = 1
                else:
                    ws.cell(row=i + 2, column=5).value = 0
                    # self.set_red_font(ws, i + 2)
                ws.column_dimensions['E'].width = 5

            print('faq实际检索结果获取完成，并存入文件')
            print("线程 %s 写入数据完成。" % thread_name)
        except Exception as ex:
            print('线程写入失败')
            print(ex)


    # 定义一个函数，用于创建并启动多个线程
    def write_data_to_excel_threads(self, ws):
        """
        将检索结果写入excel
        :param ws:
        :return:
        """
        threads = []
        num_groups = self.faq_search_config['num_groups']
        query_list, standard_question_list = self.get_faq_template_query_question()[0], \
                                             self.get_faq_template_query_question()[1]
        group_size = len(query_list) // num_groups

        with ThreadPoolExecutor(max_workers=num_groups) as executor:
            for i in range(num_groups):
                start_index = i * group_size
                end_index = (i + 1) * group_size if i < num_groups - 1 else len(query_list)
                thread_name = "Thread-" + str(i + 1)
                future = executor.submit(self.write_to_excel, ws, thread_name, start_index,
                                         end_index, query_list, standard_question_list)
                threads.append(future)

        for future in as_completed(threads):
            future.result()

        print("所有线程写入数据完成。")

    def write_faq_result_to_file(self):
        """
        将检索结果写入excel
        :return:
        """
        now_time = time.localtime()
        str_time_now = time.strftime("%Y%m%d%H%M%S", now_time)
        base_dir = './data/result_dir'
        dir = base_dir + "/" + "faq_search_result_%s.xlsx" % str_time_now
        try:
            # 创建一个工作簿对象
            wb = Workbook()
            # 获取活动的工作表
            ws = wb.create_sheet('检索结果', 0)
            col_name = ['query', '预期标准问题', '实际检索问题', 'search score', '是否正确']
            ws.append(col_name)
            self.write_data_to_excel_threads(ws)
            # 设置全部单元 自动换行
            for key in list(ws._cells.keys()):
                print(key)
                ws._cells[key].alignment = Alignment(wrapText=True)

            if not os.path.exists(base_dir):
                os.makedirs(base_dir)
            wb.save(dir)
            return dir
        except Exception as ex:
            print('写入excel失败')
            print(ex)
            # 触发异常，判断文件是否存在，如果存在将文件删除
            if os.path.exists(dir):
                os.remove(dir)
            return None

        # # 结果汇总
        # faq_result_file_path = "./faq_search_result.xlsx"
        # print('faq检索效果结果汇总中。。。')
        # # 加载工作簿
        # template1 = load_workbook(filename=faq_result_file_path)
        # # 获取sheet页
        # template1 = template1['检索结果']
        #
        # correct_list = []
        # for correct in template1['E']:
        #     correct_list.append(correct.value)
        #
        # correct_list = correct_list[1:]
        #
        # count = 0
        # for num in correct_list:
        #     if num == 1:
        #         count += 1
        #
        # value = (count / len(correct_list)) * 100
        # insert_value = str(value) + '%'
        #
        # ws.cell(row=len(correct_list) + 5, column=5).value = insert_value
        # ws.cell(row=len(correct_list) + 5, column=4).value = '准确率'

    def import_faq_to_tenant(self):
        """
        将faq导入到租户中
        :return:
        """
        # import_faq_file_path = FileAddress.faq_search_train_dataset
        import_faq_file_path = './app/data/llm_search/faq/faq_import.xlsx'
        headers = {
            "Cookie": self.faq_search_config['cookie'],
        }

        url = self.env_config['backend_address'] + '/api/v2/task/create/faq_import' \
              + '?dirId=' + self.faq_search_config['dir_id'] + '&lang=zh&agentId=' + str(self.env_config['agentId']) + \
              "&tenantId=" + str(self.env_config['tenantId'])
        print(url)
        file_name = {'file': open(import_faq_file_path, 'rb')}
        print(file_name)
        response = requests.post(url, headers=headers, files=file_name)
        msg = response.json()['msg']
        print(response)
        print(response.text)
        if msg == 'OK':
            agentName = response.json()['data']['agentName']
            result = agentName + ':' + 'FAQ导入成功，请登陆任务中心查看'
        else:
            result = 'FAQ导入失败，请联系管理员****************'
        return result


if __name__ == '__main__':
    cookie = 'test'
    env_config = {
        'knowledge_address': 'http://************:8889',
        'backend_address': 'http://************:8889',
        'agentId': '8a01b8bb-76a9-4418-8e79-1aaf2e1bb714',
        "tenantId": "34220452032000000",
    }

    faq_search_config = {
        "min_score": 0.6,
        "max_score": 1,
        "dataset_id": '1',
        "top_k_faq": 1,
        "num_groups": 3,
        "dir_id": '0',
        "cookie": cookie,
    }

    data_file_path = './app/data/llm_search/faq/faq_test_template.xlsx'
    current_time = time.strftime('%Y-%m-%d %H:%M:%S')
    print("Current time:", current_time)
    task_id = '12'
    obj = FaqImportSearch(env_config, faq_search_config, data_file_path, task_id)
    #
    # # 导入faq训练集到指定租户，，会导入进指定目录，默认目录为【默认】
    # res = obj.import_faq_to_tenant()
    # print(res)
    # 导入完成后运行，写入faq检索结果到excel文件中并计算准确率
    path = obj.write_faq_result_to_file()
    print(path)
    print("Current time:", current_time)
