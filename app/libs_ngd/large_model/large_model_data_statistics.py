#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/10/11 14:17:06
"""
from app.models.models import LargeModelAnalysis
import traceback

"""
大模型数据统计工具
"""


def data_statistics(data, task_id, version, time):
    """
    数据统计
    :param data:
    :param task_id:
    :param version:
    :param time:
    :return:
    """
    """
    data数据应该为这样的
    [
        ['id', '用户query', '标准答案', 'coreQuery结果', '得分', '原因', '第三方评判信息'],
        ['id1', 'query1', 'query1', 'query1', 'query1', 'query1', 'query1'],
        ......
    ]
    """
    print '要处理的数据长这样'
    print data
    test_case_count = len(data)
    response_count = len(filter(lambda x: x[6] != u'没有请求第三方大模型，并未命中，直接判0分' and \
                                          x[6] != u'请求ngd失败，请检查环境信息是否正确', data))
    no_response_count = test_case_count - response_count
    return_correct_count = len(filter(lambda x: x[4] == '2' or x[4] == 2, data))
    call_percent = response_count / float(test_case_count)
    correct_percent = return_correct_count / float(test_case_count)
    # 计算总分
    total_score = 0
    acceptable_count = 0
    for row in data:
        print '得分为'
        print row[4]
        print type(row[4])
        if type(row[4]) in (int, float, long):
            total_score += row[4]
            if row[4] >= 1:
                acceptable_count += 1
        else:
            if row[4].isdigit():
                total_score += int(row[4])
                if int(row[4]) >= 1:
                    acceptable_count += 1
    scoring_rate_percent = total_score / float(test_case_count * 2)
    acceptability = acceptable_count / float(test_case_count)
    avg_score = total_score / float(test_case_count)
    str_time_now = time
    analysis_data = LargeModelAnalysis(
        testcasecount=test_case_count,
        response_count=response_count,
        no_response_count=no_response_count,
        return_correct_count=return_correct_count,
        call_percent=call_percent,
        correct_percent=correct_percent,
        scoring_rate_percent=scoring_rate_percent,
        acceptable_count=acceptable_count,
        acceptability=acceptability,
        avg_score=avg_score,
        task_id=task_id,
        finish_time=str_time_now,
        version=version,
        version_finish_time=version + '_' + str_time_now
    )
    print analysis_data.object2dict()
    return analysis_data


if __name__ == '__main__':
    list1 = [
        [1, '用户query1', '标准答案1', 'coreQuery结果1', '0', '原因1', '第三方评判信息1'],
        [2, '用户query2', '标准答案2', 'coreQuery结果2', '1', '原因2', '第三方评判信息2'],
        [3, '用户query3', '标准答案3', 'coreQuery结果3', '2', '原因3', '第三方评判信息3'],
        [4, '用户query4', '标准答案4', 'coreQuery结果4', 0, '原因4', '第三方评判信息4'],
        ['5', '用户query5', '标准答案5', 'coreQuery结果5', 2, '原因5', '第三方评判信息5'],
        ['6', '用户query6', '标准答案6', 'coreQuery结果6', 1, '原因6', '第三方评判信息6'],
        [u'7', u'用户query7', u'标准答案7', u'coreQuery结果7', 1, u'原因7', u'第三方评判信息7'],
        [u'8', u'用户query8', u'标准答案8', u'coreQuery结果8', u'0', u'并未命中，直接判0分',
         u'没有请求第三方大模型，并未命中，直接判0分'],
        [u'9', u'用户query9', u'标准答案9', u'coreQuery结果9', u'得分未知', u'答案解析未知', u'请求第三方大模型失败']
    ]
    analysis_data = data_statistics(list1, 'task1', 'version1', '2017-10-10 00:00:00')
    # i = 1
    # print i.isdigit()
