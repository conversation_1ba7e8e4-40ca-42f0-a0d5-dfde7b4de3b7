# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/01/18
"""
import time
import traceback
import os
import sys
from openpyxl import Workbook
from app.models.models import Task, IntentsAnalysis

dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(dir)
from config import FileAddress
from app.models.exts import get_db

global db
db = get_db()
from ..utils.log import LOG


class MulitIntents:
    """
    多线程
    """

    def __init__(self, test_case_list, intent_query, module, conf, model, agentFlag):
        self.output_wookbook = Workbook()
        # 选择第一个工作表
        # result_sheet = output_wookbook.active
        self.result_sheet = self.output_wookbook.create_sheet(index=0, title=u'测试')
        self.report_sheet = self.output_wookbook.create_sheet(index=1, title=u"统计数据")
        # self.profession_label_sheet = self.output_wookbook.create_sheet(index=2, title=u"行业标签")
        # self.label_sheet = self.output_wookbook.create_sheet(index=3, title=u"标签")
        self.result_sheet.title = u"测试结果"

        self.result_sheet.cell(1, 1, "query")
        self.result_sheet.cell(1, 2, "真实意图ID")
        self.result_sheet.cell(1, 3, "真实意图名称")
        self.result_sheet.cell(1, 4, "识别出的意图名称")
        self.result_sheet.cell(1, 5, "是否一致")
        self.result_sheet.cell(1, 6, "相似度")
        self.result_sheet.cell(1, 7, "是否澄清")
        self.result_sheet.cell(1, 8, "澄清的意图名称")
        self.result_sheet.cell(1, 9, "来源")

        self.row_index = 0
        self.module = module
        self.model = model
        self.agentFlag = agentFlag
        self.correct_count = 0
        self.response_correct_count = 0
        self.clarify_correct_count = 0
        self.response_count = 0
        self.clarify_count = 0
        self.has_response_count = 0
        self.response_wrong_count = 0
        self.response_clarify_wrong_count = 0

        self.test_case_list = test_case_list
        self.result_list = []

        self.intent_query = intent_query

        self.conf = conf
        self.nlu_enable = self.conf['nlu_enable']
        self.nlu_response_count = 0
        self.nlu_correct_count = 0

        self.corequery_response_count = 0
        self.corequery_correct_count = 0

    def query_functions(self, test_case):
        """
        获取query结果
        """
        query = test_case['query']
        standard = test_case['standard_question']
        standard_id = test_case['standard_id']
        LOG.info(standard)
        standard = standard.encode('utf8')

        response, questions_id, questions_type = self.intent_query.intent_standard_question(query)

        if response:
            self.has_response_count += 1
            response_str = ""
            clarify_str = ""
            confidence = ""
            source = ""
            response_flag = False
            clarity_flag = False
            response_flag_2 = False
            clarity_flag_2 = False

            if standard_id in questions_id:
                for x in response:
                    if x["id"] == standard_id:
                        if "name" in x and x["name"] is not None:
                            self.response_count += 1
                            response_flag = (x["id"] == standard_id)
                            response_str = x["name"].encode('utf8')
                            confidence = x["confidence"]
                            clarify_str = ""
                            source = x["source"]
                            if response_flag:
                                self.response_correct_count += 1
                        elif "clarify_name" in x and x["clarify_name"] is not None:
                            self.clarify_count += 1
                            clarity_flag = (x["id"] == standard_id)
                            response_str = ""
                            confidence = x["confidence"]
                            clarify_str = x["clarify_name"].encode('utf8')
                            source = x["source"]
                            LOG.info(('%s, %s') % (standard, clarify_str))
                            if clarity_flag:
                                self.clarify_correct_count += 1
            else:
                if "intents" in questions_type:
                    self.response_wrong_count += 1
                    response_str_list = []
                    confidence_list = []
                    source_list = []
                    for x in response:
                        response_str_list.append(x["name"].encode('utf8'))
                        confidence_list.append(str(x["confidence"]))
                        source_list.append(x["source"])
                    response_str = ",".join(response_str_list)
                    confidence = ",".join(confidence_list)
                    clarify_str = ""
                    source = ",".join(source_list)
                elif "clarifyIntents" in questions_type:
                    self.response_clarify_wrong_count += 1
                    confidence_list = []
                    clarify_str_list = []
                    source_list = []
                    for x in response:
                        clarify_str_list.append(x["clarify_name"].encode('utf8'))
                        confidence_list.append(str(x["confidence"]))
                        source_list.append(x["source"])
                    response_str = ""
                    confidence = ",".join(confidence_list)
                    clarify_str = ",".join(clarify_str_list)
                    source = ",".join(source_list)

            self.result_list.append(
                query + '%*' + standard_id + '%*' + standard + '%*' +
                response_str + '%*' + str(response_flag) + '%*' + str(confidence) + '%*' + str(
                    clarity_flag) + '%*' + clarify_str + '%*' + source)


        else:
            LOG.info("responce is none")
            self.result_list.append(
                query + '%*' + standard_id + '%*' + standard + '%*' +
                "" + '%*' + "0" + '%*' + "0" + '%*' + "0" + '%*' + "" + '%*' + "")

        self.row_index = self.row_index + 1

    def save_intents_result(self):
        """
        保存结果
        """
        # 处理self.result_list
        # print self.result_list
        print("total_count", self.row_index)
        print("has_response_count", self.has_response_count)
        print("response_wrong_count", self.response_wrong_count)
        print("response_clarify_wrong_count", self.response_clarify_wrong_count)
        print("response_correct_count", self.response_correct_count)
        print("clarify_correct_count", self.clarify_correct_count)
        print("response_count", self.response_count)
        print("clarify_count", self.clarify_count)
        for i in range(0, len(self.result_list)):
            row_num = i + 2
            result_data = self.result_list[i]
            result_data_list = result_data.split("%*")

            for i in range(len(result_data_list)):
                self.result_sheet.cell(row_num, i + 1, result_data_list[i])

        # self.report_sheet.cell(1, 2, "corequery分析结果")

        self.report_sheet.cell(1, 1, "用例总数")
        self.report_sheet.cell(2, 1, "有返回数据")
        self.report_sheet.cell(3, 1, "有返回数据（澄清）")
        self.report_sheet.cell(4, 1, "有返回数据（非澄清）")
        self.report_sheet.cell(5, 1, "直接命中正确")
        self.report_sheet.cell(6, 1, "澄清命中")
        self.report_sheet.cell(7, 1, "召回率（包含澄清）")
        self.report_sheet.cell(8, 1, "召回率（不含澄清）")
        self.report_sheet.cell(9, 1, "准确召回率（包含澄清）")
        self.report_sheet.cell(10, 1, "准确召回率（不含澄清）")
        self.report_sheet.cell(11, 1, "精确率（包含澄清）")
        self.report_sheet.cell(12, 1, "精确率（不含澄清）")

        testcasecount = len(self.test_case_list)

        self.report_sheet.cell(1, 2, self.row_index)
        self.report_sheet.cell(2, 2, self.has_response_count)
        self.report_sheet.cell(3, 2, self.response_clarify_wrong_count + self.clarify_correct_count)
        self.report_sheet.cell(4, 2, self.response_wrong_count + self.response_correct_count)
        self.report_sheet.cell(5, 2, self.response_correct_count)
        self.report_sheet.cell(6, 2, self.clarify_correct_count)
        self.report_sheet.cell(7, 2, float(self.has_response_count) / float(self.row_index))
        self.report_sheet.cell(8, 2, float(self.response_wrong_count + self.response_count) / float(self.row_index))
        self.report_sheet.cell(9, 2, float(self.response_count + self.clarify_count) / float(self.row_index))
        self.report_sheet.cell(10, 2, float(self.response_correct_count) / float(self.row_index))
        prec_percent_include_clarify = 0
        prec_percent_not_include_clarify = 0
        if (self.has_response_count) != 0:
            self.report_sheet.cell(11, 2, float(self.response_correct_count + self.clarify_correct_count) / float(
                self.has_response_count))
            self.report_sheet.cell(12, 2, float(self.response_correct_count) / float(self.has_response_count))
            prec_percent_include_clarify = float(self.response_correct_count + self.clarify_correct_count) / float(
                self.has_response_count)
            prec_percent_not_include_clarify = float(self.response_correct_count) / float(self.has_response_count)
        else:
            self.report_sheet.cell(11, 2, 0)
            self.report_sheet.cell(12, 2, 0)
        new_analysis = IntentsAnalysis(testcasecount=self.row_index,
                                       response_count=self.has_response_count,
                                       response_count_claify=self.response_clarify_wrong_count +
                                                             self.clarify_correct_count,
                                       response_count_not_claify=self.response_wrong_count +
                                                                 self.response_correct_count,
                                       response_correct_count=self.response_correct_count,
                                       clarify_correct_count=self.clarify_correct_count,
                                       call_percent_include_clarify=float(self.has_response_count) / float(
                                           self.row_index),
                                       call_percent_not_include_clarify=float(
                                           self.response_wrong_count + self.response_count)
                                                                        / float(self.row_index),
                                       correct_call_percent_include_clarify=float(
                                           self.response_count + self.clarify_count) / float(self.row_index),
                                       correct_call_percent_not_include_clarify=float(
                                           self.response_correct_count) / float(self.row_index),
                                       prec_percent_include_clarify=prec_percent_include_clarify,
                                       prec_percent_not_include_clarify=prec_percent_not_include_clarify,
                                       task_id=self.conf['task_id'])
        msg = ''
        try:
            db.session.add(new_analysis)
            db.session.commit()
        except Exception as e:
            print(e)
            traceback.print_exc()
            msg = e
        try:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            finish_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            base_dir = FileAddress.result_dir
            if not os.path.exists(base_dir):
                os.makedirs(base_dir)
            dir = base_dir + "/" + "%s_evaluate_result_%s_%s_%s.xlsx" % \
                  (self.module, time_now, self.model, self.agentFlag)
            self.output_wookbook.save(dir)

            task = Task.query.filter(Task.task_id == self.conf['task_id']).first()
            print "获取到的task"
            print task.task_id
            print task.create_time
            task.result_dir = dir.decode('utf-8')
            task.finish_time = finish_time
            analysis = IntentsAnalysis.query.filter(IntentsAnalysis.task_id == self.conf['task_id']).first()
            analysis.version = task.version
            analysis.finish_time = finish_time
            analysis.version_finish_time = task.version + "_" + finish_time + "_" + self.model + "_" + self.agentFlag
            time.sleep(2)
        except Exception as e:
            print(e)
            traceback.print_exc()
            msg = e

        # self.output_wookbook.save(
        #     "./output/%s_evaluate_result_%s_%s_%s.xlsx" % (
        #         self.module, self.model, self.agentFlag, self.conf['version']))

# if __name__ == '__main__':
#     reader = FaqTestReader()
#     test_case_list = reader.read()
#     faq_query = FaqQuery()
#     evaluate(test_case_list, faq_query)
