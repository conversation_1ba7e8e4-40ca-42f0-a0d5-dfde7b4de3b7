#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""

from ..utils.log import LOG
from ..utils.nlu_recognize import NluRecognize


class IntentsQuery:
    """
    FaqQuery
    """

    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.suthorization = conf['authorization']
        self.conf = conf

        print conf['backend_address']
        # self.url = conf.backend_address
        self.url = conf['backend_address']

    def intent_standard_question(self, query):
        """
        :param query:
        :return:
        """
        res = NluRecognize.nlu_recognize(query, self.conf)
        data = res['data']
        # LOG.info("nlu返回结果" + str(data))
        questions = []
        questions_id = []
        questions_type = []
        if data is None:
            return None, None, None

        if 'intents' in data and data['intents'] is not None:
            for x in data['intents']:
                questions.append({"id": x['name'], "name": x['nameZh'],
                                  "confidence": x['confidence'], "source": x['source']})
                questions_id.append(x["name"])
                questions_type.append("intents")
        if 'clarifyIntents' in data and data['clarifyIntents'] is not None:
            LOG.info("clarifyIntents res: %s" % questions)
            for x in data['clarifyIntents']:
                questions.append({"id": x['name'], "clarify_name": x['nameZh'],
                                  "confidence": x['confidence'], "source": x['source']})
                questions_id.append(x["name"])
                questions_type.append("clarifyIntents")
        LOG.info("nlu res: %s" % questions)
        return questions, questions_id, questions_type
