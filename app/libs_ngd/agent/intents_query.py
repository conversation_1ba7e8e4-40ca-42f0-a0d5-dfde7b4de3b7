#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""

from ..utils.log import LOG
from ..utils.nlu_recognize import NluRecognize


class AgentIntentsQueryFromPlaform:
    """
    FaqQuery
    """

    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.url = conf.url
        self.suthorization = conf.authorization
        self.conf = conf

    def intent_standard_question(self, query):
        """
        :param query:
        :return:
        """
        res = NluRecognize.nlu_recognize(query, self.conf)
        data = res['data']

        questions = []
        questions_id = []
        questions_type = []
        if data is None:
            return None, None, None

        if 'intents' in data and data['intents'] is not None:
            for x in data['intents']:
                questions.append({"id": x['name'], "name": x['nameZh'],
                                  "confidence": x['confidence'], "source": x['source']})
                questions_id.append(x["name"])
                questions_type.append("intents")
        if 'clarifyIntents' in data and data['clarifyIntents'] is not None:
            LOG.info("clarifyIntents res: %s" % questions)
            for x in data['clarifyIntents']:
                questions.append({"id": x['name'], "clarify_name": x['nameZh'],
                                  "confidence": x['confidence'], "source": x['source']})
                questions_id.append(x["name"])
                questions_type.append("clarifyIntents")
        LOG.info("nlu res: %s" % questions)
        return questions, questions_id, questions_type

    def entity_question(self, query):
        """
        :param query:
        :return:
        """
        res = NluRecognize.nlu_recognize(query, self.conf)
        data = res['data']
        LOG.info("data: %s" % data)
        questions = []
        questions_value = []
        num = 0
        LOG.info(data['entities'])
        if data is None or data['entities'] is None or len(data['entities']) == 0:
            LOG.info("data is none")
            return None
        for entity_id, entities in data['entities'].items():
            LOG.info("entity_id: %s" % entity_id)
            LOG.info("entity_nlu: %s" % entities)

            if "sys_" in entity_id:
                LOG.info("是系统实体，跳过: %s" % entity_id)
                continue
            for entity in entities:
                num += 1
                questions.append(entity['nameZh'])
                questions_value.append(entity['value'])
                LOG.info("tntity : %s" % entity)
        # LOG.info("用户实体：%s, 用户实体值： %s" % (questions, questions_value))
        if num == 0:
            return None
        else:
            return questions