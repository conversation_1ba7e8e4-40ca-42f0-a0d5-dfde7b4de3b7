#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
from ..utils.http_request import HttpRequest

class AgentIntentsReaderFromPlatfrom:
    """
    FaqTestReader
    """

    def __init__(self, conf):
        """
        init
        """
        self.conf = conf

    def read(self):
        """
        :return:
        """
        return self.intent_list()

    def intent_list(self):
        """
        Turn on all switches
        :return:
        """
        ids = []
        intent_values = []
        ps = 20
        res = self.get_intent_list(1, ps)
        total = res['data']['total']

        if total <= ps:
            for i in range(total):
                data = res['data']['list']
                ids.append(data[i]['id'])
                intent_values.extend(self.intent_detail(data[i]['id']))
        else:
            pn_num = total / ps
            ps_end = total % ps
            if ps_end > 0:
                pn_num += 1
            for i in range(pn_num):
                res = self.get_intent_list(pn_num, ps)
                for k in range(len(res['data']['list'])):
                    data = res['data']['list']
                    ids.append(data[k]['id'])
                    intent_values.extend(self.intent_detail(data[i]['id']))
        return intent_values

    def intent_detail(self, intent_id):
        """
        :param intent_id:
        :return:
        """
        ps = 10
        res = self.get_intent_detail(intent_id, 1, ps)
        total = int(res['data']['total'])
        intent_values = []
        if total <= ps:
            intent_values.extend(self.get_intent_values(res, intent_id))
        else:
            pn_num = total / ps
            ps_end = total % ps
            if ps_end > 0:
                pn_num += 1
            for i in range(pn_num):
                res = self.get_intent_detail(intent_id, 1, ps)
                # LOG.info("第 % s页面实体值" % i)
                intent_values.extend(self.get_intent_values(res, intent_id))

        return intent_values

    def get_intent_list(self, pn, ps):
        """
        获取意图列表
        :return:
        """
        api = "/api/v2/intent/user_intent?agentId=" + self.conf.agentId + "&pn=" + str(pn) + "&ps=" + str(ps) + \
              "&permCode=intent:w&referer=index"
        res = HttpRequest.get(api, self.conf)
        self.check_get_list_res(res)
        return res

    def get_intent_detail(self, intent_id, pn, ps):
        """
        进意图详情，获取示例
        :param intent_id:
        :param pn:
        :param ps:
        :return:
        """
        api = "/api/v2/intent/example?intentId=" + intent_id + "&pn=" + str(pn) + "&keyword=&ps=" + str(ps) + \
              "&agentId=" + self.conf.agentId + "&permCode=intent:w&referer=index"
        res = HttpRequest.get(api, self.conf)
        self.check_get_list_res(res)
        return res

    @staticmethod
    def get_intent_values(res, intent_id):
        """
        :param intent_id:
        :param res:
        :return:
        """
        intent_values = []
        data = res['data']['list']
        for entity in data:
            intent_values.append({'query': u'%s' % entity['value'], 'nameZh': intent_id})
        return intent_values

    @staticmethod
    def check_get_list_res(res):
        """

        :param res:
        :return:
        """
        assert res['code'] == 200
        assert res['msg'] == "OK"
        assert res['data']['list'] is not None

    def read_entity(self, test_cases_query, test_list):
        """
        组合意图和对应的实体
        :param test_cases_query:
        :param test_list:
        :return:
        """
        # LOG.info(test_cases_query)
        for query_list in test_cases_query:
            # LOG.info(query_list)
            query = query_list['query']
            entity_list = []
            entity_nameZh_list = []
            # LOG.info(test_list)
            for data in test_list:
                # LOG.info(data)
                if data['entity_answer'] in query:
                    if data['entity_nameZh'] in entity_nameZh_list:
                        continue
                    entity_list.append("%s-%s" % (data['entity_nameZh'], data['entity_answer']))
                    entity_nameZh_list.append(data['entity_nameZh'])
            query_list['entity_list'] = entity_list
            query_list['entity_nameZh_list'] = entity_nameZh_list
        # LOG.info("test_cases_query : %s" % test_cases_query)
        return test_cases_query
