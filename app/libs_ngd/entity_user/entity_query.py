#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""

from ..utils.log import LOG
from ..utils.nlu_recognize import NluRecognize


class UserEntityQuery:
    """
    FaqQuery
    """
    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.conf = conf

    def entity_question(self, query, entity_id):
        """
        :param entity_id:
        :param query:
        :return:
        """
        # LOG.info("%s-%s" % (query, entity_id))
        res = NluRecognize.nlu_recognize(query, self.conf)
        data = res['data']
        if data is None or entity_id is None or data['entities'] is None:
            return None
        return data

    def entity_value(self, data, entity_id):
        """
        :param data:
        :param entity_id:
        :return:
        """
        response = []
        LOG.info(entity_id)
        LOG.info(data)
        if entity_id in data['entities']:
            # LOG.info(len(data['entities'][entity_id]))
            for i in range(len(data['entities'][entity_id])):
                value = self.getValue(data['entities'][entity_id][i], entity_id)
                response.append(value)
            # LOG.info(response)
            return response

        return None

    def getValue(self, res, entity_id):
        """
        :param res:
        :param entity_id:
        :return:
        """
        type2 = ['sys_money_range']
        type3 = ['sys_time_range']
        if entity_id in type2:
            if res['extra']['start'] is not None and res['extra']['end'] is not None:
                value = "%s-%s" % (res['extra']['start'].encode('utf-8'), res['extra']['end'].encode('utf-8'))
                return value
        elif entity_id in type3:
            if res['value'] is not None:
                # LOG.info(res['value'])
                value = res['value']
                value_list = value.split(' ~ ')
                # LOG.info("value_list is %s" % value_list)
                return '-'.join(value_list).encode('utf-8')
            else:
                return None
        else:
            if res['value'] is not None:
                return res['value'].encode('utf-8')
            else:
                return None

    def entity_nameZh(self, data, entity_id):
        """
        entity_nameZh
        :param data:
        :param entity_id:
        :return:
        """
        response = []
        LOG.info(entity_id)
        LOG.info(data)
        if entity_id in data['entities']:
            # LOG.info(len(data['entities'][entity_id]))
            for i in range(len(data['entities'][entity_id])):
                value = self.getNameZh(data['entities'][entity_id][i])
                response.append(value)
            # LOG.info(response)
            return response

        return None

    def getNameZh(self, res):
        """
        :param res:
        :param entity_id:
        :return:
        """
        if res['nameZh'] is not None and len(res['nameZh']) != 0:
            return res['nameZh'].encode('utf-8')
        else:
            return None