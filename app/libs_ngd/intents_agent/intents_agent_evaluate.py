# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""

from openpyxl import Workbook

from ..intents_agent.intents_agent_query import IntentsAgentQuery
from ..utils.log import LOG


def intents_agent_evaluate_entity(test_case_list, conf):
    """
    :param test_case_list:
    :return:
    """
    output_wookbook = Workbook()
    # 选择第一个工作表
    # result_sheet = output_wookbook.active
    result_sheet = output_wookbook.create_sheet(index=0, title=u'测试')

    result_sheet.title = u"测试结果"

    result_sheet.cell(1, 1, "query")
    result_sheet.cell(1, 2, "id")
    result_sheet.cell(1, 3, "正确结果")
    result_sheet.cell(1, 4, "识别结果-nlu")
    result_sheet.cell(1, 5, "是否正确")

    row_index = 0

    correct_count = 0
    wrong_count = 0
    entity_switch = []
    for test_case in test_case_list:

        row_index += 1
        query = test_case['query']
        standard_id = test_case['standard_id']
        # 应该包含的
        entity_list = test_case['entity_list']
        entity_nameZh_list = test_case['entity_nameZh_list']

        # LOG.info("query is %s" % query)
        # LOG.info("standard is %s " % standard)
        # standard = standard.encode('utf8')

        entity = IntentsAgentQuery(conf)
        # 识别到的
        # entity_nameZh, entity_value = entity.entity_question(query)
        entity_nameZh = entity.entity_question(query)

        if len(entity_nameZh_list) == 0:
            entity_nameZh_list_str = ""
        else:
            entity_nameZh_list_str = ','.join(entity_nameZh_list)

        if entity_nameZh is None or len(entity_nameZh) == 0:
            entity_nameZh_str = ""
        else:
            entity_nameZh_str = ','.join(entity_nameZh)
        LOG.info("nlu entity_nameZh_str=%s" % entity_nameZh_str)
        LOG.info("excel entity_nameZh_list_str=%s" % entity_nameZh_list_str)
        if entity_nameZh == entity_nameZh_list:
            correct_count += 1
            result_sheet.cell(row_index + 1, 1, query)
            result_sheet.cell(row_index + 1, 2, standard_id)
            result_sheet.cell(row_index + 1, 3, entity_nameZh_list_str)
            result_sheet.cell(row_index + 1, 4, entity_nameZh_str)
            result_sheet.cell(row_index + 1, 5, 1)
        else:
            wrong_count += 1
            result_sheet.cell(row_index + 1, 1, query)
            result_sheet.cell(row_index + 1, 2, standard_id)
            result_sheet.cell(row_index + 1, 3, entity_nameZh_list_str)
            result_sheet.cell(row_index + 1, 4, entity_nameZh_str)
            result_sheet.cell(row_index + 1, 5, 0)

    print("total_count", row_index)
    print("correct_count", correct_count)
    print("wrong_count", wrong_count)

    report_sheet = output_wookbook.create_sheet(index=1, title=u"统计数据")

    report_sheet.cell(1, 1, "用例总数")
    report_sheet.cell(2, 1, "识别正确")
    report_sheet.cell(3, 1, "识别错误")
    report_sheet.cell(4, 1, "正确率")
    report_sheet.cell(5, 1, "错误率")

    report_sheet.cell(1, 2, row_index)
    report_sheet.cell(2, 2, correct_count)
    report_sheet.cell(3, 2, wrong_count)
    report_sheet.cell(4, 2, float(correct_count) / float(row_index))
    report_sheet.cell(5, 2, float(wrong_count) / float(row_index))

    # time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
    # output_wookbook.save("./data/entity_evaluate_result_%s.xlsx" % time_now)
    output_wookbook.save("./output/agent_entity_evaluate_result.xlsx")

    return entity_switch
