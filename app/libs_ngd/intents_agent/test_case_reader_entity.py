#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
import openpyxl

from ..utils.log import LOG


class EntityAgentTestReader:
    """
    FaqTestReader
    """

    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        LOG.info(len(sheet_names))
        test_cases = []
        sheet = workbook.get_sheet_by_name(sheet_names[0])
        skip_first = True
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue
            query = row_i[3].value
            LOG.info('实体值: %s' % row_i[3].value)
            if query is None or len(query) == 0:
                LOG.info('实体值为空，继续: %s' % row_i[3].value)
                continue
            entity_answer = row_i[3].value
            entity_answer = entity_answer
            # LOG.info("entity_answer: %s" % entity_answer)
            # LOG.info(type(query))
            entity_id = row_i[0].value
            if entity_id == 'sys_time_range' or entity_id == 'sys_money_range':
                entity_answer_2 = row_i[3].value
                entity_answer = u"%s-%s" % (u"%s" % entity_answer, u"%s" % entity_answer_2)
            elif entity_id == 'sys_date':
                entity_answer = u"%s" % (entity_answer.strftime("%Y-%m-%d"))
            else:
                entity_answer = u"%s" % entity_answer
            test_cases.append({'query': u'%s' % query, 'entity_answer': u'%s' % entity_answer,
                               'entity_id': entity_id, 'entity_nameZh': u'%s' % row_i[1].value})
            LOG.info({'query': u'%s' % query, 'entity_answer': u'%s' % entity_answer,
                      'entity_id': entity_id, 'entity_nameZh': u'%s' % row_i[1].value})
            # test_cases.append({'query': u'%s' % query, 'entity_answer': entity_answer, 'entity_id': entity_id})
            # LOG.info({'query': u'%s' % query, 'entity_answer': entity_answer, 'entity_id': entity_id})
            LOG.info('同义词: %s' % row_i[4].value)
            LOG.info('同义词length: %s' % len(row_i[4].value))
            similar = row_i[4].value
            if similar is not None and len(similar) != 0:
                query = row_i[4].value
                similar_list = query.split(',')
                for que in similar_list:
                    query = que
                    entity_answer = row_i[3].value
                    entity_answer = entity_answer
                    # LOG.info("entity_answer: %s" % entity_answer)
                    # LOG.info(type(query))
                    entity_id = row_i[0].value
                    if entity_id == 'sys_time_range' or entity_id == 'sys_money_range':
                        entity_answer_2 = row_i[3].value
                        entity_answer = u"%s-%s" % (u"%s" % entity_answer, u"%s" % entity_answer_2)
                    elif entity_id == 'sys_date':
                        entity_answer = u"%s" % (entity_answer.strftime("%Y-%m-%d"))
                    else:
                        entity_answer = u"%s" % entity_answer
                    test_cases.append({'query': u'%s' % query, 'entity_answer': u'%s' % entity_answer,
                                       'entity_id': entity_id, 'entity_nameZh': u'%s' % row_i[1].value})
                    LOG.info({'query': u'%s' % query, 'entity_answer': u'%s' % entity_answer,
                              'entity_id': entity_id, 'entity_nameZh': u'%s' % row_i[1].value})

        return test_cases
