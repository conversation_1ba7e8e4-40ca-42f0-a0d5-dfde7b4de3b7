#!/usr/bin/python
# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/05/06
"""
import time
import os
import sys
import traceback
from openpyxl import Workbook
from app.models.models import Task, FaqAnalysis

# from libs.tableqa import conf_auto

dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(dir)
from config import FileAddress
from app.models.exts import get_db

global db
db = get_db()


class MutilFaq:
    """
    多线程
    """

    def __init__(self, test_case_list, faq_query, conf, model):
        self.version = conf['version']
        self.output_wookbook = Workbook()
        # 选择第一个工作表
        # result_sheet = output_wookbook.active
        self.result_sheet = self.output_wookbook.create_sheet(index=0, title=u'测试')
        self.result_sheet.title = u"测试结果"

        self.result_sheet.cell(1, 1, "query")
        self.result_sheet.cell(1, 2, "正确标准问")
        self.result_sheet.cell(1, 3, "识别标准问")
        self.result_sheet.cell(1, 4, "是否一致")
        self.result_sheet.cell(1, 5, "澄清标准问")
        self.result_sheet.cell(1, 6, "澄清是否命中")
        self.test_case_list = test_case_list
        self.faq_query = faq_query
        self.conf = conf
        self.model = model
        self.row_index = 0

        self.correct_count = 0
        self.response_correct_count = 0
        self.clarify_correct_count = 0
        self.response_count = 0
        self.clarify_count = 0
        self.sid_result_list = []

    def query_functions(self, test_case):
        """
        获取query结果
        """
        # sid_result_list = []
        self.row_index += 1
        query = test_case['query']
        standard = test_case['standard_question']
        # LOG.info(standard)
        standard = standard.encode('utf8')
        time.sleep(1)
        response = self.faq_query.faq_standard_question(query)
        if response is not None:
            response_str = ""
            clarify_str = ""
            response_flag = False
            clarity_flag = False
            if isinstance(response, unicode):
                # 直接命中
                if isinstance(response.encode('utf8'), str):
                    response = response.encode('utf8')
                    response_str = response
                    clarify_str = ""
                    # LOG.info('%s ; %s ' % (response, standard))
                    response_flag = (response == standard)
                    self.response_count += 1
                    print("response_count: %s" % self.response_count)
                    if response_flag:
                        self.response_correct_count += 1
            elif isinstance(response, set):
                # 澄清
                response_str = ""
                clarify_str = "\n".join(response)
                clarify_str = clarify_str.encode('utf8')
                clarity_flag = standard in clarify_str
                # LOG.info(('%s, %s') % (standard, clarify_str))
                if clarify_str is not None:
                    self.clarify_count += 1
                if clarity_flag:
                    self.clarify_correct_count += 1

            if response_flag or clarity_flag:
                # LOG.info('%s,%s' % (response_flag, clarity_flag))
                self.correct_count += 1

            # self.result_sheet.cell(self.row_index + 1, 1, query)
            # self.result_sheet.cell(self.row_index + 1, 2, standard)
            # self.result_sheet.cell(self.row_index + 1, 3, response_str)
            # self.result_sheet.cell(self.row_index + 1, 4, int(response_flag) if isinstance(response, str) else "")
            # self.result_sheet.cell(self.row_index + 1, 5, clarify_str)
            # self.result_sheet.cell(self.row_index + 1, 6, int(clarity_flag) if isinstance(response, set) else "")
            self.sid_result_list.append(
                query + '%*' + standard + '%*' + response_str + '%*' +
                str(int(response_flag) if isinstance(response, str) else "") + '%*' + str(clarify_str) + '%*'
                + str(int(clarity_flag) if isinstance(response, set) else ""))
        else:
            # LOG.info("没有命中")
            # self.result_sheet.cell(self.row_index + 1, 1, query)
            # self.result_sheet.cell(self.row_index + 1, 2, standard)
            # self.result_sheet.cell(self.row_index + 1, 3, "")
            # self.result_sheet.cell(self.row_index + 1, 4, 0)
            # self.result_sheet.cell(self.row_index + 1, 5, "")
            # self.result_sheet.cell(self.row_index + 1, 6, 0)
            self.sid_result_list.append(
                query + '%*' + standard + '%*' + "" + '%*' +
                str("0") + '%*' + "" + '%*'
                + str("0"))

    def save_faq_result(self):
        """
        保存结果
        """
        report_sheet = self.output_wookbook.create_sheet(index=1, title=u"统计数据")
        for i in range(0, len(self.sid_result_list)):
            row_num = i + 2
            result_data = self.sid_result_list[i]
            result_data_list = result_data.split("%*")

            for i in range(len(result_data_list)):
                self.result_sheet.cell(row_num, i + 1, result_data_list[i])

        report_sheet.cell(1, 1, "用例总数")
        report_sheet.cell(2, 1, "直接命中+澄清命中正确")
        report_sheet.cell(3, 1, "直接命中正确")
        report_sheet.cell(4, 1, "澄清命中")
        report_sheet.cell(5, 1, "有返回数据")
        report_sheet.cell(6, 1, "无返回数据")
        report_sheet.cell(7, 1, "召回率")
        report_sheet.cell(8, 1, "准确召回率（包含澄清）")
        report_sheet.cell(9, 1, "准确召回率（不含澄清）")
        report_sheet.cell(10, 1, "精确率（包含澄清）")

        report_sheet.cell(1, 2, self.row_index)
        report_sheet.cell(2, 2, self.correct_count)
        report_sheet.cell(3, 2, self.response_correct_count)
        report_sheet.cell(4, 2, self.clarify_correct_count)
        report_sheet.cell(5, 2, self.response_count + self.clarify_count)
        report_sheet.cell(6, 2, self.row_index - self.response_count - self.clarify_count)
        report_sheet.cell(7, 2, float(self.response_count + self.clarify_count) / float(self.row_index))
        report_sheet.cell(8, 2, float(self.correct_count) / float(self.row_index))
        report_sheet.cell(9, 2, float(self.response_correct_count) / float(self.row_index))
        new_analysis = FaqAnalysis(testcasecount=self.row_index,
                                   hit_clarify_count=self.correct_count,
                                   hit_count=self.response_correct_count,
                                   clarify_count=self.clarify_correct_count,
                                   response_count=self.response_count + self.clarify_count,
                                   no_response_count=self.row_index - self.response_count - self.clarify_count,
                                   task_id=self.conf['task_id'],
                                   call_percent=float(self.response_count + self.clarify_count) / float(self.row_index),
                                   correct_call_percent_include_clarify=
                                   float(self.correct_count) / float(self.row_index),
                                   correct_call_percent_not_include_clarify=
                                   float(self.response_correct_count) / float(self.row_index)
                                   )

        msg = ''
        try:
            db.session.add(new_analysis)
            db.session.commit()
        except Exception as e:
            print(e)
            traceback.print_exc()
            msg = e
        analysis = FaqAnalysis.query.filter(FaqAnalysis.task_id == self.conf['task_id']).first()
        if (self.response_count + self.clarify_count) != 0:
            report_sheet.cell(10, 2, float(self.correct_count) / float(self.response_count + self.clarify_count))
            analysis.prec_percent_include_clarify = float(self.correct_count) / float(
                self.response_count + self.clarify_count)
        else:
            report_sheet.cell(10, 2, 0)
            analysis.prec_percent_include_clarify = 0

        time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
        finish_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        base_dir = FileAddress.result_dir
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        dir = base_dir + "/" + "faq_evaluate_result_%s_%s.xlsx" % (self.model, time_now)
        self.output_wookbook.save(dir)

        task = Task.query.filter(Task.task_id == self.conf['task_id']).first()
        print task.id
        print task.version
        task.result_dir = dir.decode('utf-8')
        task.finish_time = finish_time
        time.sleep(2)
        analysis = FaqAnalysis.query.filter(FaqAnalysis.task_id == self.conf['task_id']).first()
        analysis.version = task.version
        analysis.finish_time = finish_time
        analysis.version_finish_time = task.version + "_" + finish_time
