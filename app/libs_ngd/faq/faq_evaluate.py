# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
import time

from openpyxl import Workbook

from ..utils.log import <PERSON>O<PERSON>


def evaluate(test_case_list, faq_query, conf):
    """
    :param test_case_list:
    :param faq_query:
    :return:
    """
    version = conf.ngd_version
    output_wookbook = Workbook()
    # 选择第一个工作表
    # result_sheet = output_wookbook.active
    result_sheet = output_wookbook.create_sheet(index=0, title=u'测试')

    result_sheet.title = u"测试结果"

    result_sheet.cell(1, 1, "query")
    result_sheet.cell(1, 2, "正确标准问")
    result_sheet.cell(1, 3, "识别标准问")
    result_sheet.cell(1, 4, "是否一致")
    result_sheet.cell(1, 5, "澄清标准问")
    result_sheet.cell(1, 6, "澄清是否命中")

    row_index = 0

    correct_count = 0
    response_correct_count = 0
    clarify_correct_count = 0
    response_count = 0
    clarify_count = 0

    for test_case in test_case_list:
        row_index += 1
        query = test_case['query']
        standard = test_case['standard_question']
        LOG.info(standard)
        standard = standard.encode('utf8')
        time.sleep(1)
        response = faq_query.faq_standard_question(query)
        if response is not None:
            response_str = ""
            clarify_str = ""
            response_flag = False
            clarity_flag = False
            if isinstance(response, unicode):
                # 直接命中
                if isinstance(response.encode('utf8'), str):
                    response = response.encode('utf8')
                    response_str = response
                    clarify_str = ""
                    LOG.info('%s ; %s ' % (response, standard))
                    response_flag = (response == standard)
                    response_count += 1
                    print("response_count: %s" % response_count)
                    if response_flag:
                        response_correct_count += 1
            elif isinstance(response, set):
                # 澄清
                response_str = ""
                clarify_str = "\n".join(response)
                clarify_str = clarify_str.encode('utf8')
                clarity_flag = standard in clarify_str
                LOG.info(('%s, %s') % (standard, clarify_str))
                if clarify_str is not None:
                    clarify_count += 1
                if clarity_flag:
                    clarify_correct_count += 1

            if response_flag or clarity_flag:
                LOG.info('%s,%s' % (response_flag, clarity_flag))
                correct_count += 1

            result_sheet.cell(row_index + 1, 1, query)
            result_sheet.cell(row_index + 1, 2, standard)
            result_sheet.cell(row_index + 1, 3, response_str)
            result_sheet.cell(row_index + 1, 4, int(response_flag) if isinstance(response, str) else "")
            result_sheet.cell(row_index + 1, 5, clarify_str)
            result_sheet.cell(row_index + 1, 6, int(clarity_flag) if isinstance(response, set) else "")
        else:
            LOG.info("没有命中")
            result_sheet.cell(row_index + 1, 1, query)
            result_sheet.cell(row_index + 1, 2, standard)
            result_sheet.cell(row_index + 1, 3, "")
            result_sheet.cell(row_index + 1, 4, 0)
            result_sheet.cell(row_index + 1, 5, "")
            result_sheet.cell(row_index + 1, 6, 0)

    print("total_count", row_index)
    print("correct_count", correct_count)
    print("response_correct_count", response_correct_count)
    print("clarify_correct_count", clarify_correct_count)
    print("response_count", response_count)
    print("clarify_count", clarify_count)

    report_sheet = output_wookbook.create_sheet(index=1, title=u"统计数据")

    report_sheet.cell(1, 1, "用例总数")
    report_sheet.cell(2, 1, "直接命中+澄清命中正确")
    report_sheet.cell(3, 1, "直接命中正确")
    report_sheet.cell(4, 1, "澄清命中")
    report_sheet.cell(5, 1, "有返回数据")
    report_sheet.cell(6, 1, "无返回数据")
    report_sheet.cell(7, 1, "召回率")
    report_sheet.cell(8, 1, "准确召回率（包含澄清）")
    report_sheet.cell(9, 1, "准确召回率（不含澄清）")
    report_sheet.cell(10, 1, "精确率（包含澄清）")

    report_sheet.cell(1, 2, row_index)
    report_sheet.cell(2, 2, correct_count)
    report_sheet.cell(3, 2, response_correct_count)
    report_sheet.cell(4, 2, clarify_correct_count)
    report_sheet.cell(5, 2, response_count + clarify_count)
    report_sheet.cell(6, 2, row_index - response_count - clarify_count)
    report_sheet.cell(7, 2, float(response_count + clarify_count) / float(row_index))
    report_sheet.cell(8, 2, float(correct_count) / float(row_index))
    report_sheet.cell(9, 2, float(response_correct_count) / float(row_index))
    if (response_count + clarify_count) != 0:
        report_sheet.cell(10, 2, float(correct_count) / float(response_count + clarify_count))
    else:
        report_sheet.cell(10, 2, 0)
    time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
    output_wookbook.save("./output/faq_evaluate_result_%s.xlsx" % time_now)
    output_wookbook.save("./display/files/faq_evaluate_result_%s.xlsx" % version)

# if __name__ == '__main__':
#     reader = FaqTestReader()
#     test_case_list = reader.read()
#     faq_query = FaqQuery()
#     evaluate(test_case_list, faq_query)
