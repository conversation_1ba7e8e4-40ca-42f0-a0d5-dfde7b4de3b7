#!/usr/bin/python
# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2022/09/30
"""
import time
import traceback
import os
import sys
from openpyxl import Workbook
from app.models.models import Task, Analysis

dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(dir)
from config import FileAddress
from app.models.exts import get_db

global db
db = get_db()


class MulitTableRoundsQa:
    """
    多线程
    """

    def __init__(self, test_case_list, profession_label_list, label_list, tableqaCheck, conf):

        self.output_wookbook = Workbook()
        # 选择第一个工作表
        # result_sheet = output_wookbook.active
        self.result_sheet = self.output_wookbook.create_sheet(index=0, title=u'测试')
        self.report_sheet = self.output_wookbook.create_sheet(index=1, title=u"统计数据")
        self.profession_label_sheet = self.output_wookbook.create_sheet(index=2, title=u"行业标签")
        self.label_sheet = self.output_wookbook.create_sheet(index=3, title=u"标签")
        self.result_sheet.title = u"测试结果"

        self.result_sheet.cell(1, 1, "id")
        self.result_sheet.cell(1, 2, "query")
        self.result_sheet.cell(1, 3, "corequery期望结果")
        self.result_sheet.cell(1, 4, "corequery实际结果")
        self.result_sheet.cell(1, 5, "corequery测试结论")
        self.result_sheet.cell(1, 6, "行业标签")
        self.result_sheet.cell(1, 7, "标签")
        self.result_sheet.cell(1, 8, "sql")
        self.conf = conf
        self.nlu_enable = self.conf['nlu_enable']
        if self.nlu_enable:
            self.result_sheet.cell(1, 9, "nlu期望结果")
            self.result_sheet.cell(1, 10, "nlu实际结果")
            self.result_sheet.cell(1, 11, "nlu测试结论")
        self.testcasecount = 0
        self.result_list = []
        self.row_index = 0
        self.nlu_response_count = 0
        self.corequery_response_count = 0
        self.nlu_correct_count = 0
        self.corequery_correct_count = 0
        self.profession_label_result = {}
        self.tableqaCheck = tableqaCheck
        self.label_result = {}
        for profession_label in profession_label_list:
            self.profession_label_result[profession_label] = {"nlu_correct": 0, "nlu_error": 0, "nlu_total": 0,
                                                              "core_correct": 0, "core_error": 0, "core_total": 0,
                                                              "response_total": 0}
        for label in label_list:
            self.label_result[label] = {"nlu_correct": 0, "nlu_error": 0, "nlu_total": 0,
                                        "core_correct": 0, "core_error": 0,
                                        "core_total": 0, "response_total": 0}

    def query_functions(self, seeeionid_case):
        """
        获取query结果 seeeionid_case: {id:[{第一轮},{第二轮}]}
        """
        sid_result_list = []
        sId = seeeionid_case.keys()[0]
        sessionid_case_list = seeeionid_case.values()[0]
        for test_case in sessionid_case_list:
            # sId = test_case["id"]  # 用户query
            sQuery = test_case["query"]  # 用户query
            sCoreExpect = test_case["coreExpect"]
            profession_label = test_case['professionLabel']
            label = test_case['label']

            self.row_index += 1

            # coreRes, bCoreResult = self.tableqaCheck.checkCoreResult(sQuery, sCoreExpect, sId)
            coreRes, coreQueryExpectResult, bCoreResult = self.tableqaCheck.checkCoreResult(sQuery, sCoreExpect,
                                                                                                  sId)
            sSql = self.tableqaCheck.getSql()

            self.testcasecount = self.testcasecount + 1
            sid_result_list.append(
                str(sId) + '%*' + sQuery + '%*' + coreQueryExpectResult + '%*' + coreRes + '%*' +
                str(bCoreResult) + '%*' + str(profession_label) + '%*' + str(label) + '%*' + sSql)

            # if coreRes != "回复为空":
            # 召回数
            if self.conf['no_answer'] not in coreRes:
                self.corequery_response_count += 1
                if profession_label is not None:
                    self.profession_label_result[profession_label]["response_total"] = \
                        self.profession_label_result[profession_label][
                            "response_total"] + 1
                if label is not None:
                    sub_label_list = label.split(',')
                    for sub_label in sub_label_list:
                        self.label_result[sub_label]["response_total"] = self.label_result[sub_label][
                                                                             "response_total"] + 1
            # 成功数
            if bCoreResult:
                self.corequery_correct_count += 1
                if profession_label is not None:
                    self.profession_label_result[profession_label]["core_correct"] = \
                        self.profession_label_result[profession_label][
                            "core_correct"] + 1
                if label is not None:
                    sub_label_list = label.split(',')
                    for sub_label in sub_label_list:
                        self.label_result[sub_label]["core_correct"] = self.label_result[sub_label][
                                                                           "core_correct"] + 1
            else:
                if profession_label is not None:
                    self.profession_label_result[profession_label]["core_error"] = \
                        self.profession_label_result[profession_label][
                            "core_error"] + 1
                if label is not None:
                    sub_label_list = label.split(',')
                    for sub_label in sub_label_list:
                        self.label_result[sub_label]["core_error"] = self.label_result[sub_label][
                                                                         "core_error"] + 1
            if profession_label is not None:
                self.profession_label_result[profession_label]["core_total"] = \
                    self.profession_label_result[profession_label][
                        "core_total"] + 1
            if label is not None:
                sub_label_list = label.split(',')
                for sub_label in sub_label_list:
                    self.label_result[sub_label]["core_total"] = self.label_result[sub_label][
                                                                     "core_total"] + 1
                # print sub_label, str(self.label_result[sub_label]["core_total"]), sQuery, sId

        self.result_list.append(sid_result_list)

    def save_tableqa_result(self):
        """
        保存结果
        """
        # 处理self.result_list
        # print self.result_list
        fin_result_list = []
        for sid_result_list in self.result_list:
            # print sid_result_list
            for i in sid_result_list:
                fin_result_list.append(i)

        for i in range(0, len(fin_result_list)):
            row_num = i + 2
            result_data = fin_result_list[i]
            result_data_list = result_data.split("%*")

            for i in range(len(result_data_list)):
                self.result_sheet.cell(row_num, i + 1, result_data_list[i])

        self.report_sheet.cell(1, 2, "corequery分析结果")
        if self.nlu_enable:
            self.report_sheet.cell(1, 3, "nlu结果")
        self.report_sheet.cell(2, 1, "用例总数")
        self.report_sheet.cell(3, 1, "有返回数据")
        self.report_sheet.cell(4, 1, "无返回数据")
        self.report_sheet.cell(5, 1, "返回正确数据")
        self.report_sheet.cell(6, 1, "返回错误数据")
        self.report_sheet.cell(7, 1, "召回率")
        self.report_sheet.cell(8, 1, "准确率")

        testcasecount = self.testcasecount
        no_response_count = testcasecount - self.corequery_response_count
        error_count = testcasecount - self.corequery_correct_count
        call_percent = float(self.corequery_response_count) / float(testcasecount)
        prec_percent = float(self.corequery_correct_count) / float(testcasecount)
        self.report_sheet.cell(2, 2, testcasecount)
        self.report_sheet.cell(3, 2, self.corequery_response_count)
        self.report_sheet.cell(4, 2, testcasecount - self.corequery_response_count)
        self.report_sheet.cell(5, 2, self.corequery_correct_count)
        self.report_sheet.cell(6, 2, error_count)
        self.report_sheet.cell(7, 2, call_percent)
        self.report_sheet.cell(8, 2, prec_percent)
        new_analysis = Analysis(testcasecount=testcasecount,
                                response_count=self.corequery_response_count,
                                no_response_count=no_response_count,
                                correct_count=self.corequery_correct_count,
                                error_count=error_count,
                                call_percent=call_percent,
                                task_id=self.conf['task_id'],
                                prec_percent=prec_percent)
        msg = ''
        try:
            db.session.add(new_analysis)
            db.session.commit()
        except Exception as e:
            print(e)
            traceback.print_exc()
            msg = e
        if self.nlu_enable:
            self.report_sheet.cell(2, 3, testcasecount)
            self.report_sheet.cell(3, 3, self.nlu_response_count)
            self.report_sheet.cell(4, 3, testcasecount - self.nlu_response_count)
            self.report_sheet.cell(5, 3, self.nlu_correct_count)
            self.report_sheet.cell(6, 3, testcasecount - self.nlu_correct_count)
            self.report_sheet.cell(7, 3, float(self.nlu_response_count) / float(testcasecount))
            self.report_sheet.cell(8, 3, float(self.nlu_correct_count) / float(testcasecount))

        # profession_label_sheet
        if self.nlu_enable:
            row0 = [u'标签', u'用例总数', u'core成功数', u'core失败数', u'core召回率',
                    u'core准确率', u'nlu成功数', u'nlu失败数', u'nlu召回率',
                    u'nlu准确率', ]
        else:
            row0 = [u'标签', u'用例总数', u'core成功数', u'core失败数', u'core召回率', u'core准确率']
        for i in range(0, len(row0)):
            self.profession_label_sheet.cell(1, i + 1, row0[i])
        column0 = self.profession_label_result.keys()
        for i in range(0, len(column0)):
            self.profession_label_sheet.cell(i + 2, 1, column0[i])
            self.profession_label_sheet.cell(i + 2, 2, self.profession_label_result[column0[i]]['core_total'])
            self.profession_label_sheet.cell(i + 2, 3, self.profession_label_result[column0[i]]['core_correct'])
            self.profession_label_sheet.cell(i + 2, 4, self.profession_label_result[column0[i]]['core_error'])
            self.profession_label_sheet.cell(i + 2, 5, float(
                self.profession_label_result[column0[i]]['response_total']) / float(
                self.profession_label_result[column0[i]]['core_total']))
            self.profession_label_sheet.cell(i + 2, 6,
                                             float(self.profession_label_result[column0[i]]['core_correct']) / float(
                                                 self.profession_label_result[column0[i]]['core_total']))
            if self.nlu_enable:
                self.profession_label_sheet.cell(i + 2, 7, self.profession_label_result[column0[i]]['nlu_correct'])
                self.profession_label_sheet.cell(i + 2, 8, self.profession_label_result[column0[i]]['nlu_error'])
                self.profession_label_sheet.cell(i + 2, 9, float(
                    self.profession_label_result[column0[i]]['nlu_correct'] + self.profession_label_result[column0[i]][
                        'nlu_error']) / float(self.profession_label_result[column0[i]]['nlu_total']))
                self.profession_label_sheet.cell(i + 2, 10,
                                                 float(self.profession_label_result[column0[i]]['nlu_correct']) / float(
                                                     self.profession_label_result[column0[i]]['nlu_total']))
        if self.nlu_enable:
            row0 = [u'标签', u'用例总数', u'core成功数', u'core失败数', u'core召回率', u'core准确率', u'nlu成功数',
                    u'nlu失败数', u'nlu召回率',
                    u'nlu准确率']
        else:
            row0 = [u'标签', u'用例总数', u'core成功数', u'core失败数', u'core召回率', u'core准确率']
        for i in range(0, len(row0)):
            self.label_sheet.cell(1, i + 1, row0[i])
        column0 = self.label_result.keys()
        for i in range(0, len(column0)):
            self.label_sheet.cell(i + 2, 1, column0[i])
            self.label_sheet.cell(i + 2, 2, self.label_result[column0[i]]['core_total'])
            self.label_sheet.cell(i + 2, 3, self.label_result[column0[i]]['core_correct'])
            self.label_sheet.cell(i + 2, 4, self.label_result[column0[i]]['core_error'])
            self.label_sheet.cell(i + 2, 5, float(
                self.label_result[column0[i]]['response_total']) / float(self.label_result[column0[i]]['core_total']))
            self.label_sheet.cell(i + 2, 6, float(self.label_result[column0[i]]['core_correct']) / float(
                self.label_result[column0[i]]['core_total']))
            if self.nlu_enable:
                self.label_sheet.cell(i + 2, 7, self.label_result[column0[i]]['nlu_correct'])
                self.label_sheet.cell(i + 2, 8, self.label_result[column0[i]]['nlu_error'])
                self.label_sheet.cell(i + 2, 9, float(
                    self.label_result[column0[i]]['nlu_correct'] + self.label_result[column0[i]][
                        'nlu_error']) / float(self.label_result[column0[i]]['nlu_total']))
                self.label_sheet.cell(i + 2, 10, float(self.label_result[column0[i]]['nlu_correct']) / float(
                    self.label_result[column0[i]]['nlu_total']))

        time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
        finish_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        base_dir = FileAddress.result_dir
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        dir = base_dir + "/" + "tableqa_rounds_result_%s.xlsx" % time_now
        self.output_wookbook.save(dir)

        task = Task.query.filter(Task.task_id == self.conf['task_id']).first()
        task.result_dir = dir
        task.finish_time = finish_time
        analysis = Analysis.query.filter(Analysis.task_id == self.conf['task_id']).first()
        analysis.version = task.version
        analysis.finish_time = finish_time
        analysis.version_finish_time = task.version + "_" + finish_time

        # self.output_wookbook.save("./display/files/tableqa_rounds_result_%s.xlsx" % self.conf['version'])

# if __name__ == '__main__':
#     log.init_log("./log/tableqa")
#
# reader = TableQaTestReader(conf_auto.tableqa_file)
# test_case_list = reader.read()
# LOG.info(test_case_list)
# tableQaCheck = TableQaCheck(conf_auto)
# save_tableqa_result(test_case_list, tableQaCheck)
