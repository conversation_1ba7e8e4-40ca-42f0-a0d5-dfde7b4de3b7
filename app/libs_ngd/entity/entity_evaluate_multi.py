#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/02/16 17:23:06
"""
from multiprocessing import Pool

from openpyxl import Workbook

from entity_query import EntityQuery
import os
import sys
import traceback
from ..utils.entity.open_switch import Switches
from app.models.models import Task, EntityAnalysis

dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(dir)
from config import FileAddress
from app.models.exts import get_db

global db
db = get_db()
from ..utils.log import LOG

import time


class MutilEntity:
    """
    多线程
    """

    def __init__(self, test_case_list, module, conf):
        # self.version = conf['ngd_version']
        self.conf = conf
        self.module = module
        self.output_wookbook = Workbook()
        # 选择第一个工作表
        # result_sheet = output_wookbook.active
        self.result_sheet = self.output_wookbook.create_sheet(index=0, title=u'测试')

        self.result_sheet.title = u"测试结果"

        self.result_sheet.cell(1, 1, "query")
        self.result_sheet.cell(1, 2, "id")
        self.result_sheet.cell(1, 3, "正确结果")
        self.result_sheet.cell(1, 4, "识别结果")
        self.result_sheet.cell(1, 5, "是否正确")

        self.result_list = []

        self.row_index = 0
        self.correct_count = 0
        self.wrong_count = 0
        self.entity_switch = []

    def entity_evaluate(self, test_case):
        """
        :param test_case_list:
        :return:
        """
        self.row_index += 1
        query = test_case['query']
        standard = test_case['entity_answer']
        entity_id = test_case['entity_id']

        print "111------"
        print(query)
        print(entity_id)
        print(standard)
        print "222------"

        if entity_id == "sys_time":
            print(standard)
            print(query)
            print(entity_id)
        # open_s = Switches(conf)
        #
        # if entity_id not in entity_switch:
        #     for id in entity_switch:
        #         open_s.close_switches(id)
        #         entity_switch.remove(id)
        #         LOG.info("禁用中：%s" % entity_switch)
        #     open_s.open_switch(entity_id)
        #     entity_switch.append(entity_id)
        #     LOG.info("启用中：%s" % entity_switch)

        LOG.info("standard is %s " % standard)
        standard = standard.encode('utf8')

        # time.sleep(120)

        entity = EntityQuery(self.conf)
        response = entity.entity_question(query, entity_id)
        LOG.info("response is %s " % response)
        if response:
            LOG.info("ok")
        else:
            LOG.info("empty")
        if response is not None and response:
            response_flag = True
            res = ','.join(response)

            # if str(standard) in response:
            if standard in response:
                self.result_list.append(
                    query + '%*' + entity_id + '%*' + standard + '%*' +
                    res + '%*' + str(1))

                # self.write_query_data(query, entity_id, standard, res, 1)
                # print 33333
                # print standard
                self.correct_count += 1
                # self.result_sheet.cell(self.row_index + 1, 1, query)
                # self.result_sheet.cell(self.row_index + 1, 2, entity_id)
                # self.result_sheet.cell(self.row_index + 1, 3, standard)
                # self.result_sheet.cell(self.row_index + 1, 4, res)
                # self.result_sheet.cell(self.row_index + 1, 5, 1)
            else:
                self.result_list.append(
                    query + '%*' + entity_id + '%*' + standard + '%*' +
                    res + '%*' + str(0))
                # self.write_query_data(query, entity_id, standard, res, 0)
                # print 4444
                # print standard
                self.wrong_count += 1
                # self.result_sheet.cell(self.row_index + 1, 1, query)
                # self.result_sheet.cell(self.row_index + 1, 2, entity_id)
                # self.result_sheet.cell(self.row_index + 1, 3, standard)
                # self.result_sheet.cell(self.row_index + 1, 4, res)
                # self.result_sheet.cell(self.row_index + 1, 5, 0)
        else:
            self.result_list.append(
                query + '%*' + entity_id + '%*' + standard + '%*' +
                " " + '%*' + str(0))
            # self.write_query_data(query, entity_id, standard, "", 0)
            # print 55555
            # print standard
            self.wrong_count += 1
            # self.result_sheet.cell(self.row_index + 1, 1, query)
            # self.result_sheet.cell(self.row_index + 1, 2, entity_id)
            # self.result_sheet.cell(self.row_index + 1, 3, standard)
            # self.result_sheet.cell(self.row_index + 1, 4, "")
            # self.result_sheet.cell(self.row_index + 1, 5, 0)

    def write_query_data(self, query, entity_id, standard, res, result):
        """
        excel结果中的第一个sheet，写入query、期望结果、实际结果
        :return:
        """
        print standard
        self.wrong_count += 1
        self.result_sheet.cell(self.row_index + 1, 1, query)
        self.result_sheet.cell(self.row_index + 1, 2, entity_id)
        self.result_sheet.cell(self.row_index + 1, 3, standard)
        self.result_sheet.cell(self.row_index + 1, 4, res)
        self.result_sheet.cell(self.row_index + 1, 5, result)

    def save_statistics(self):
        """
        保存结果
        Returns:

        """
        for i in range(0, len(self.result_list)):
            row_num = i + 2
            result_data = self.result_list[i]
            result_data_list = result_data.split("%*")

            for i in range(len(result_data_list)):
                self.result_sheet.cell(row_num, i + 1, result_data_list[i])

        print("total_count", self.row_index)
        print("correct_count", self.correct_count)
        print("wrong_count", self.wrong_count)

        report_sheet = self.output_wookbook.create_sheet(index=1, title=u"统计数据")

        report_sheet.cell(1, 1, "用例总数")
        report_sheet.cell(2, 1, "识别正确")
        report_sheet.cell(3, 1, "识别错误")
        report_sheet.cell(4, 1, "正确率")
        report_sheet.cell(5, 1, "错误率")

        report_sheet.cell(1, 2, self.row_index)
        report_sheet.cell(2, 2, self.correct_count)
        report_sheet.cell(3, 2, self.wrong_count)
        if self.row_index != 0:
            report_sheet.cell(4, 2, float(self.correct_count) / float(self.row_index))
            report_sheet.cell(5, 2, float(self.wrong_count) / float(self.row_index))

        time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
        finish_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        base_dir = FileAddress.result_dir
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        dir = base_dir + "/" + "%s_evaluate_result_%s.xlsx" % (self.module, time_now)
        self.output_wookbook.save(dir)

        new_analysis = EntityAnalysis(testcasecount=self.row_index,
                                      correct_count=self.correct_count,
                                      error_count=self.wrong_count,
                                      correct_percent=float(self.correct_count) / float(self.row_index),
                                      error_percent=float(self.wrong_count) / float(self.row_index),
                                      task_id=self.conf['task_id'])
        msg = ''
        try:
            db.session.add(new_analysis)
            db.session.commit()
        except Exception as e:
            print(e)
            traceback.print_exc()
            msg = e
        task = Task.query.filter(Task.task_id == self.conf['task_id']).first()
        task.result_dir = dir.decode('utf-8')
        task.finish_time = finish_time
        analysis = EntityAnalysis.query.filter(EntityAnalysis.task_id == self.conf['task_id']).first()
        analysis.version = task.version
        analysis.finish_time = finish_time
        analysis.version_finish_time = task.version + "_" + finish_time

        # time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
        # self.output_wookbook.save("./output/%s_evaluate_result_%s.xlsx" % (self.module, time_now))
        # self.output_wookbook.save("./display/files/%s_evaluate_result_%s.xlsx" % (self.module, self.version))

# if __name__ == '__main__':
#     main()
