#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
配置日志参数

@Authors: <AUTHORS>
@Date:      2022/05/14 5:13 PM
"""
import logging
import os
from logging import handlers

# from high_availability_backend.services.utils.design_manager.design_mode import Singleton


# class LoggerManager(metaclass=Singleton):
class LoggerManager():
    """
    日志管理类
    """
    # __metaclass__ = Singleton

    def __init__(self, name="", log_dir=None, stream_level=None, file_level=None):
        """
        初始化日志对象
        """

        self.name = name
        self.log_dir = log_dir

        if stream_level is None:
            stream_level = logging.ERROR
        self.stream_level = stream_level

        if file_level is None:
            file_level = logging.DEBUG
        self.file_level = file_level

        if self.log_dir and not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir, exist_ok=True)

        # Format
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(lineno)d - %(module)s - %(message)s')
        # StreamHandler
        self.stream_handler = logging.StreamHandler()
        self.stream_handler.setLevel(level=self.stream_level)
        self.stream_handler.setFormatter(formatter)

        # FileHandler
        if self.log_dir:
            self.file_handler = handlers.RotatingFileHandler('{}/output.log'.format(self.log_dir),
                                                             mode='a',
                                                             maxBytes=1024 * 1024 * 100,
                                                             backupCount=10)
            # 设定显示级别
            self.file_handler.setLevel(level=self.file_level)
            self.file_handler.setFormatter(formatter)
        else:
            self.file_handler = None

        self.logger_set = {}

    def get_logger(self, name=None):
        """
        :param name:
        :return:
        """
        if name is None:
            name = self.name

        if name in self.logger_set:
            return self.logger_set[name]

        logger = logging.getLogger(name)
        logger.setLevel(level=logging.DEBUG)
        logger.addHandler(self.stream_handler)
        if self.file_handler:
            logger.addHandler(self.file_handler)

        self.logger_set[name] = logger
        return logger


logger = LoggerManager().get_logger()