#!/usr/bin/python
# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/04/25

闲聊没有澄清
问答和意图澄清出来的内容就是标准问
"""

import re
import time

from ..core_query import CoreQuery
from ..log import LOG
import sys

reload(sys)
sys.setdefaultencoding('utf8')


class CoreQueryCheck:
    """
    tableqa
    """

    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.conf = conf

        print conf['backend_address']
        self.url = conf['backend_address']

    def getCoreQueryResult(self, sQuery, sessionId):

        """

        Args:
            sQuery:
            sessionId:

        Returns:

        """
        # print "=======getCoreQueryResult======="
        print "sQuery---------------"
        print sQuery
        print "sessionId-------------"
        print sessionId
        time.sleep(1)
        corequery = CoreQuery(self.conf)
        coreResult = {}
        results = corequery.core_query(sQuery, sessionId)
        if results is not None:
            coreResult = results.json()
        else:
            response = str(
                sessionId) + '%*' + sQuery + '%*' + "-" + '%*' + "-" + '%*' + "请求报错了，检查ngd环境是否可用" + '%*'
            # print "===else"
            return response

        coreRes = []
        try:
            if coreResult["data"]["source"] == "clarify":
                res_list = coreResult["data"]["debug"]["detail"]["clarifyQuestions"]["text"]["list"]

                # if self.conf['data_from'] == "query":
                # 期望值如果是从会话窗口中获取的
                res_str = self.conf['clarify_answer']
                for i in range(len(res_list)):
                    res_str = res_str + "\n" + str(i + 1) + ". " + res_list[i]
                # print(res_str)
                coreRes.append(res_str)
                engine = coreResult["data"]["answer"]["clarifyType"]  # 来源引擎
                classification = self.getClassification(True, engine, coreResult)
            else:
                coreRes.append(coreResult["data"]["answer"]["answerText"])
                engine = coreResult["data"]["source"]  # 来源引擎
                classification = self.getClassification(False, engine, coreResult)
            scoreRes = " ".join(coreRes)
            response = str(sessionId) + '%*' + sQuery + '%*' + engine + '%*' + classification + '%*' + scoreRes + '%*'
            # print "------123"
            # print scoreRes
            # print response
            return response
        except Exception as e:
            LOG.error("====getCoreQueryResult: " + str(e))
            response = str(
                sessionId) + '%*' + sQuery + '%*' + "-" + '%*' + "-" + '%*' \
                       + "解析结果发生了异常，联系zhangjingjing12" + '%*'
            return response
            LOG.error("抛出异常：访问【%s】解析corequery结果出错" % sQuery.encode('utf-8'))

    def getClassification(self, isClarify, engine, coreRes):
        """
        获取命中了哪个分类：获取命中的问答标准问、闲聊标准问、意图名、实体名
        """
        classification = "-"
        if isClarify:
            classification = ",".join(coreRes["data"]["debug"]["detail"]["clarifyQuestions"]["text"]["list"])
        else:
            if engine == "faq" or engine == "chitchat":
                classification = coreRes["data"]["debug"]["detail"]["standardQuestion"]
            elif engine == "task_based":
                entities = coreRes["data"]["debug"]["detail"]["nlu"]["entities"]
                intents = coreRes["data"]["debug"]["detail"]["nlu"]["intents"]
                # print "===entities===intents==="
                # print entities
                # print intents
                if entities:
                    classification = ",".join(entities.keys())
                if intents:
                    classification = intents["nameZh"]
        return classification
