#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide http request.

Authors: <AUTHORS>
Date:    2020/10/21 22:04:06
"""
import time

from ..http_request import HttpRequest
from ..log import LOG


class Bot(object):
    """
    init
    """

    def __init__(self, conf):
        """
        init
        :param conf:
        """
        self.conf = conf

    def createBot(self, name):
        """
        create bot and get bot's accessToken
        :param name:
        :return:
        """
        api = "/api/v2/bot/manage/create"
        json = {"name": name}
        flag = 1
        while flag < 10:
            try:
                res = HttpRequest.post(api, json, self.conf)
                LOG.info("create bot result：%s" % res)
                flag += 1
                LOG.info("创建的bot信息：%s" % res)
                LOG.info("创建的bot token：%s" % res["data"]["accessToken"])
                return res["data"]["accessToken"]
            except Exception as e:
                time.sleep(2)
                print("出现如下异常%s" % e)

        if res['code'] != 200 or res['code'] != 4002403:
            print("\n")
            print(res['code'])
            print(res['msg'])
            raise Exception("create bot error {}".format(res["msg"]))

    def checkBotIsExist(self, name):
        """
        检查bot是否存在
        :param name:
        :return:
        """
        api = "/api/v2/bot/manage?agentId=%s&permCode=bot:w&referer=bot" % self.conf['agentId']
        json = {"agentId": self.conf['agentId'], "startTime": "", "endTime": "", "timeType": "created", "keyword": name,
                "pn": 1, "ps": 200}
        flag = 1
        while flag < 10:
            try:
                res = HttpRequest.post(api, json, self.conf)
                LOG.info("check bot result：%s" % res)
                flag += 1
                LOG.info("搜索结果：%s" % res)
            except Exception as e:
                time.sleep(2)
                print("出现如下异常%s" % e)

        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])
            raise Exception('未检测到bot:{}'.format(res["msg"]))

        for bot in res['data']['list']:
            LOG.info("%s-%s" % (type(bot['name']), bot['name']))
            LOG.info("%s-%s" % (type(name), name))

            if bot['name'] == name:
                LOG.info("bot【%s】已经存在" % bot["name"])
                return bot['accessToken']
        return None

    def deleteBot(self, bot):
        """
        删除bot
        :param bot:
        :return:
        """
        api = "/api/v2/bot/manage/delete?agentId=%s&permCode=bot:w&referer=bot" % self.conf['agentId']
        json = bot
        flag = 1
        while flag < 10:
            try:
                res = HttpRequest.post(api, json, self.conf)
                LOG.info("delete bot result：%s" % res)
                flag += 1
                LOG.info("删除bot的结果：%s" % res)
            except Exception as e:
                time.sleep(2)
                print("出现如下异常%s" % e)
        if res['code'] != 200:
            print("\n")
            print(res['code'])
            print(res['msg'])
            raise Exception('删除bot失败:{}'.format(res["msg"]))

    def createTableBot(self, name):
        """
        create bot and get bot's accessToken
        :param name:
        :return:
        """
        api = "/api/v2/bot/manage/create"
        json = {"name": name}
        flag = 1
        # while flag < 10:
        #     try:
        res = HttpRequest.post(api, json, self.conf)
        LOG.info("create bot result：%s" % res)
        flag += 1
        LOG.info("创建的bot信息：%s" % res)
        LOG.info("创建的bot token：%s" % res["data"]["accessToken"])
        agentId = res["data"]["agentId"]
        botId = res["data"]["id"]
        LOG.info("开始绑定tableqa====")
        self.bindTableqa(agentId, botId)
        return res["data"]["accessToken"]
    # except Exception as e:
    #     time.sleep(2)
    #     print("出现如下异常%s" % e)


    def bindTableqa(self, agentId, botId):
        """
        :param agentId:
        :param botId:
        :return:
        """
        # 获取类目id、类目名称
        api2 = "/api/v2/tableqa/category/all?permCode=tableqa_manage:w&agentId=" + agentId
        res = HttpRequest.get(api2, self.conf)
        categoryId0 = res["data"][0]["id"]
        categoryIdName0 = res["data"][0]["categoryName"]
        categoryId1 = res["data"][1]["id"]
        categoryIdName1 = res["data"][1]["categoryName"]

        # 获取技能id
        api3 = "/api/v2/bot/tableqa/category?botId=" + botId + "&agentId=" + agentId
        res2 = HttpRequest.get(api3, self.conf)
        skillId0 = res2["data"]["categorys"][0]["skillId"]
        skillId1 = res2["data"]["categorys"][1]["skillId"]

        # 绑定tableqa
        update_api = "/api/v2/bot/tableqa/category/update?agentId=" + agentId
        update_json = {"categorys": [
            {"total": "0", "isExpand": "false", "id": "" + categoryId0 + "", "categoryName": "" + categoryIdName0 + "",
             "skillId": "" + skillId0 + "", "tables": [], "sort": 1, "hasRel": "true"},
            {"total": 0, "isExpand": "false", "id": "" + categoryId1 + "", "categoryName": "" + categoryIdName1 + "",
             "skillId": "" + skillId1 + "", "tables": [], "sort": 2, "hasRel": "true"}],
            "botId": "" + botId + "", "version": 0}
        print("=========绑定tableqa========")
        update_res = HttpRequest.post(update_api, update_json, self.conf)
        print(update_res)