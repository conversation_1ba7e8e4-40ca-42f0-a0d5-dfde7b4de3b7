#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
from ..http_request import HttpRequest
from ..log import LOG


class Switches(object):
    """
    打开开关
    """
    def __init__(self, conf):
        """
        init
        """
        self.agentId = conf['agentId']
        self.conf = conf

    def open_switches(self):
        """
        Turn on all switches
        :return:
        """
        api = "/api/v2/entities/sys_entity?ps=9999&agentId=" + self.agentId
        res = HttpRequest.get(api, self.conf)
        self.checkGetList(res)
        open_switch_api = "/api/v2/entities/sys_entity/enable?agentId=" + self.agentId
        for i in range(int(res['data']['total'])):
            json = {'sysEntityId': res['data']['list'][i]['sysEntityId']}
            # LOG.info('json : %s' % json)
            res_open_switch = HttpRequest.post(open_switch_api, json, self.conf)
            self.checkOpenResult(res_open_switch)
            # LOG.info("result is : %s" % res_open_switch)

    def open_switch(self, entity_id):
        """
        Open a switch
        :param entity_id:
        :return:
        """
        api = "/api/v2/entities/sys_entity?ps=9999&agentId=" + self.agentId
        res = HttpRequest.get(api, self.conf)
        self.checkGetList(res)
        open_switch_api = "/api/v2/entities/sys_entity/enable?agentId=" + self.agentId
        for i in range(int(res['data']['total'])):
            if entity_id == res['data']['list'][i]['name']:
                json = {'sysEntityId': res['data']['list'][i]['sysEntityId']}
                # LOG.info('json : %s' % json)
                res_open_switch = HttpRequest.post(open_switch_api, json, self.conf)
                self.checkOpenResult(res_open_switch)
                # LOG.info("result is : %s" % res_open_switch)

    def close_switches(self, entity_id):
        """
        Turn off switche
        :return:
        """
        api = "/api/v2/entities/sys_entity?ps=9999&agentId=" + self.agentId
        res = HttpRequest.get(api, self.conf)
        self.checkGetList(res)
        close_switch_api = "/api/v2/entities/sys_entity/disable?agentId=" + self.agentId
        for i in range(int(res['data']['total'])):
            if entity_id == res['data']['list'][i]['name']:
                # LOG.info(res['data']['list'][i])
                close_json = {'uEntityId': res['data']['list'][i]['uEntityId']}
                # LOG.info(close_json)
                res_close_switch = HttpRequest.post(close_switch_api, close_json, self.conf)
                self.checkCloseResult(res_close_switch)

    def checkGetList(self, res):
        """
        checkGetList
        Args:
            res:

        Returns:

        """
        assert res['code'] == 200
        assert res['msg'] == "OK"
        assert res['data']['list'] is not None

    def checkOpenResult(self, res):
        """
        :param res:
        :return:
        """
        LOG.info(res)
        assert res['code'] == 4002801 or res['code'] == 4000123
        assert res['msg'] == u"启用系统实体成功" or res['msg'] == u"重复操作"

    def checkCloseResult(self, res):
        """

        :param res:
        :return:
        """
        LOG.info(res)
        assert res['code'] == 4002801 or res['code'] == 4000123
        assert res['msg'] == u"禁用系统实体成功" or res['msg'] == u"重复操作"

