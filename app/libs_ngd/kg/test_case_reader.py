#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/04/09 22:23:06
"""
import sys

import openpyxl
from ..utils.log import LOG

reload(sys)
sys.setdefaultencoding('utf8')


class KgTestReader:
    """
    FaqTestReader
    """

    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[0])

        test_cases = []

        skip_first = True
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue

            query = row_i[2].value
            # standard_question = row_i[1].value

            # test_cases.append({'query': query, 'standard_question': standard_question})
            test_cases.append(query)
        return test_cases

    def read_type_num(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[1])

        test_cases = []
        test_cases_dict = {}
        total = sheet.rows
        for row_i in sheet.rows:
            manual_annotation_type = row_i[0].value
            query_type = row_i[1].value
            test_cases_dict[u"%s" % query_type] = u"%s" % manual_annotation_type

        LOG.info(test_cases)
        return total, test_cases_dict
