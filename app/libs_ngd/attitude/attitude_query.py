#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""
from ..utils.nlu_recognize import NluRecognize


class AttitudeQuery:
    """
    FaqQuery
    """
    def __init__(self, conf):
        """
        :param url:
        :param token:
        """
        self.conf = conf

    def attitude_question(self, query):
        """
        :param entity_id:
        :param query:
        :return:
        """
        res = NluRecognize.nlu_recognize(query, self.conf)
        # print res
        data = res['data']
        # response = []
        # prob = []
        if data is None:
            return None, 0
        if data['rawQuery'] == query:
            response = data['attitude']['nameZh']
            prob = data['attitude']['prob']
            return response.encode('utf-8'), prob

        return None, 0
