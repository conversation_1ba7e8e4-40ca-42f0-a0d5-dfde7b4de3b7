#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/04/23
"""

import openpyxl


class TestReaderMulti:
    """
    FaqTestReader
    """

    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[0])

        test_cases = []
        sid_case_list = {}

        skip_first = True
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue
            if row_i[0].value is not None:
                id = row_i[0].value
                query = row_i[1].value
            else:
                break
            if id not in sid_case_list:
                sid_case_list[id] = [{'id': id, 'query': query}]
            else:
                sid_case_list[id].append({'id': id, 'query': query})
        case_list = []
        for id in sid_case_list.keys():
            case_list.append({id: sid_case_list[id]})

        print case_list

        return case_list

class TestReaderMultiEndToEnd:
    """
    场内端到端场景的文件读取
    """
    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[0])

        test_cases = []
        sid_case_list = {}
        skip_first = True
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue
            if row_i[0].value is not None:
                id = row_i[0].value
                query = row_i[1].value
                expected_res = row_i[2].value
            else:
                break
            if id not in sid_case_list:
                sid_case_list[id] = [{'id': id, 'query': query, 'expected_res': expected_res}]
            else:
                sid_case_list[id].append({'id': id, 'query': query, 'expected_res': expected_res})
        case_list = []
        for id in sid_case_list.keys():
            case_list.append({id: sid_case_list[id]})

        print '333333333333333333333333333333333333'
        print case_list

        return case_list