#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/04/09 22:23:06
"""
import sys

import openpyxl
from ..utils.log import LOG


reload(sys)
sys.setdefaultencoding('utf8')

class ClusterTestReader:
    """
    FaqTestReader
    """

    def __init__(self, file):
        """
        init
        """
        self.file = file

    def read(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)
        # workbook = xlrd.open_workbook(self.file)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[0])
        # sheet1 = workbook.sheet_by_index(0)

        # print(sheet1.cell(0, 0).value)

        test_cases = []
        test_cases_dict = {}

        skip_first = True
        num = 0
        for row_i in sheet.rows:
            num += 1
            # for row_i in sheet1.nrows:
            if skip_first:
                skip_first = False
                continue

            query = row_i[0].value
            # LOG.info(query)
            manual_annotation_type = row_i[2].value


            test_cases_dict[u"%s" % query] = u"%s" % manual_annotation_type
            test_cases.append(manual_annotation_type)
            # str = u"%s" % query
            # if len(str.decode('utf-8')) <= 30:
            #     print("%s 长度未超过30：%s" % (query, len(str.decode('utf-8'))))
            #     # test_cases.append({'query': u"%s" % query, 'manual_annotation_type': u"%s" % manual_annotation_type})
            #     test_cases_dict[u"%s" % query] = u"%s" % manual_annotation_type
            #     test_cases.append(manual_annotation_type)
            # else:
            #     print("%s 长度超过30：%s" % (query, len(str.decode('utf-8'))))
            print("num %s" % num)
        type_list = sorted(set(test_cases), key=test_cases.index)
        # LOG.info(len("type_list %s" % type_list))
        print("type_list: %s" % type_list)
        print("type_list_length: %s" % len(type_list))
        return test_cases_dict, type_list

    def read_type_num(self):
        """
        :return:
        """
        workbook = openpyxl.load_workbook(self.file, data_only=True)

        sheet_names = workbook.get_sheet_names()
        sheet = workbook.get_sheet_by_name(sheet_names[1])

        test_cases = []
        test_cases_dict = {}
        total = sheet.rows
        for row_i in sheet.rows:
            manual_annotation_type = row_i[0].value
            query_type = row_i[1].value
            test_cases_dict[u"%s" % query_type] = u"%s" % manual_annotation_type

        LOG.info(test_cases)
        return total, test_cases_dict
