# -*- coding:UTF-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/03/02 17:23:06
"""

from openpyxl import Workbook

from ..cluster.cluster_query import Cluster
from ..utils.log import LOG


def cluster_evaluate(test_case_list, type_list, conf):
    """
    :param test_case_list:
    :param faq_query:
    :return:
    """
    output_wookbook = Workbook()
    # 选择第一个工作表
    # result_sheet = output_wookbook.active
    result_sheet = output_wookbook.create_sheet(index=0, title=u'测试')

    result_sheet.title = u"测试结果"

    result_sheet.cell(1, 1, "query")
    result_sheet.cell(1, 2, "簇个数")
    result_sheet.cell(1, 3, "簇id")
    result_sheet.cell(1, 4, "簇号")
    result_sheet.cell(1, 5, "用户标注类别")
    result_sheet.cell(1, 6, "提问次数")

    row_index = 0
    cluster_num = 0
    type_dict_new = []  # 每一个clusterId对应的详细信息，包含计算出的是哪个人工标注的类型以及量级
    type_total_num = {}  # 每一个人工标注对应的机器标注的总个数
    cluster = Cluster(conf)
    cluster_list = cluster.cluster(conf)

    for key in cluster_list:
        cluster_num += 1
        clusterId = key['clusterId']
        LOG.info("key %s" % key)
        cluster_count = key['count']
        cluser_detail = key['detail']
        LOG.info(cluser_detail)
        type_dict_for_every_cluster_internal = {}  # 每一个clusterId里找到人工标注的类型中对应量级最大的类型，即定位为这个簇的类型
        for i in cluser_detail:
            row_index += 1
            manual_annotation_type = test_case_list[u"%s" % i['question']]
            result_sheet.cell(row_index + 1, 1, i['question'])
            result_sheet.cell(row_index + 1, 2, cluster_count)
            result_sheet.cell(row_index + 1, 3, clusterId)
            result_sheet.cell(row_index + 1, 4, cluster_num)
            result_sheet.cell(row_index + 1, 5, manual_annotation_type)
            result_sheet.cell(row_index + 1, 6, i["count"])
            if manual_annotation_type in type_dict_for_every_cluster_internal.keys():
                LOG.info("用户标注类别个数:%s" % type_dict_for_every_cluster_internal[manual_annotation_type])
                type_dict_for_every_cluster_internal[manual_annotation_type] += i["count"]
            else:
                type_dict_for_every_cluster_internal[manual_annotation_type] = i["count"]
            # 计算全量数据中，所有簇中每一个类型的叠加量级
            if manual_annotation_type in type_total_num.keys():
                LOG.info("用户标注类别个数:%s" % type_total_num[manual_annotation_type])
                type_total_num[manual_annotation_type] += 1
            else:
                type_total_num[manual_annotation_type] = 1
        LOG.info("type_total_num： %s " % type_dict_for_every_cluster_internal)
        max_type = sorted(type_dict_for_every_cluster_internal, key=lambda x: type_dict_for_every_cluster_internal[x])[
            -1]
        max_num = max(type_dict_for_every_cluster_internal.values())
        LOG.info("%s-%s" % (cluster_count, max_num))
        purity = float(max_num) / float(cluster_count)  # 纯度
        type_dict_new.append({"clusterId": clusterId, "count": cluster_count, "max_type": max_type,
                              "max_num": max_num, "type_num": len(type_dict_for_every_cluster_internal),
                              "purity": purity, "types": type_dict_for_every_cluster_internal})
    # 聚类后簇内数据相似性,簇的大小与数量
    weight_purity_avg = 0  # 加权平均簇纯度
    weight_total_avg = 0  # 加权平均簇大小
    weight_purity_avg_more_than_one = 0  # 加权平均簇纯度,大于1的
    weight_total_avg_more_than_one = 0  # 加权平均簇大小
    row_index_more_than_one = row_index  # 计算大于1的簇总量
    for data in type_dict_new:
        weight = float(data["count"]) / float(row_index)
        data["weight"] = weight
        data["weight_purity"] = weight * data["purity"]
        data["weight_total"] = weight * data["count"]
        weight_purity_avg += weight * data["purity"]
        weight_total_avg += weight * data["count"]
        if data["count"] == 1:
            row_index_more_than_one -= data["count"]
    flag = 0
    for data in type_dict_new:
        if data["count"] > 1:
            flag += 1
            weight_more_than_one = float(data["count"]) / float(row_index_more_than_one)
            weight_purity_avg_more_than_one += weight_more_than_one * data["purity"]
            weight_total_avg_more_than_one += weight_more_than_one * data["count"]
    print("%s-%s" % (flag, row_index_more_than_one))
    # 均一聚合度
    # 簇1样本总类别数：3；簇2样本总类别数：2；簇3样本总类别数：2；簇4样本总类别数：1；
    # 均一聚合度 = 1 / 4 * (1 / 3 + 1 / 2 + 1 / 2 + 1 / 1) * 100 %= 58.3 %
    weight_normalization_avg = 0
    # 完整聚合度：
    # 样本类别A被归类的总簇数：3；样本类别B被归类的总簇数：3；样本类别C被归类的总簇数：2
    # 均一聚合度 = 1 / 3 * (1 / 3 + 1 / 3 + 1 / 2) * 100 %= 38.9 %
    completeness_avg = 0  # 完整聚合度

    completeness_avg_dict = {}
    print("type_list" % type_list)
    for data in type_dict_new:
        print("data %s" % data)
        weight_normalization_avg += 1 / float(data["type_num"])
        list = data["types"]
        for i in list:
            if i in completeness_avg_dict.keys():
                completeness_avg_dict[i] += 1
            else:
                completeness_avg_dict[i] = 1

    for (key, value) in completeness_avg_dict.items():
        completeness_avg += 1 / float(value)
    completeness_avg = float(completeness_avg) / float(len(completeness_avg_dict))

    print("平均簇纯度：%s" % weight_purity_avg)
    print("平均簇大小：%s" % weight_total_avg)
    print("归一聚合度：%s" % (float(weight_normalization_avg) / float(len(type_dict_new))))
    print("完整聚合度对比：%s-%s" % (completeness_avg, completeness_avg_dict))

    report_sheet1 = output_wookbook.create_sheet(index=1, title=u"统计数据")

    report_sheet1.cell(1, 1, "用例总数")
    report_sheet1.cell(2, 1, "簇总量")
    report_sheet1.cell(3, 1, "人工分类")
    report_sheet1.cell(4, 1, "加权平均簇纯度")
    report_sheet1.cell(5, 1, "加权平均簇大小")
    report_sheet1.cell(6, 1, "均一聚合度")
    report_sheet1.cell(7, 1, "完整聚合度")
    report_sheet1.cell(8, 1, "加权平均簇纯度-剔除为1的簇")
    report_sheet1.cell(9, 1, "加权平均簇大小-剔除为1的簇")

    report_sheet1.cell(1, 2, row_index)
    report_sheet1.cell(2, 2, cluster_num)
    report_sheet1.cell(3, 2, len(completeness_avg_dict))
    report_sheet1.cell(4, 2, weight_purity_avg)
    report_sheet1.cell(5, 2, weight_total_avg)
    report_sheet1.cell(6, 2, float(weight_normalization_avg) / float(cluster_num))
    report_sheet1.cell(7, 2, completeness_avg)
    report_sheet1.cell(8, 2, weight_purity_avg_more_than_one)
    report_sheet1.cell(9, 2, weight_total_avg_more_than_one)

    report_sheet2 = output_wookbook.create_sheet(index=2, title=u"细致数据")

    report_sheet2.cell(1, 1, "clusterId")
    report_sheet2.cell(1, 2, "簇总量")
    report_sheet2.cell(1, 3, "簇类(取量级最大的类别，反查人工标注)")
    report_sheet2.cell(1, 4, "正确分类量级(簇类个数)")
    report_sheet2.cell(1, 5, "簇包含的类个数")
    report_sheet2.cell(1, 6, "权重")
    report_sheet2.cell(1, 7, "纯度")
    report_sheet2.cell(1, 8, "加权簇纯度")
    report_sheet2.cell(1, 9, "加权簇大小")

    row_index_new = 0
    for data in type_dict_new:
        row_index_new += 1
        report_sheet2.cell(row_index_new + 1, 1, data['clusterId'])
        report_sheet2.cell(row_index_new + 1, 2, data['count'])
        report_sheet2.cell(row_index_new + 1, 3, data['max_type'])
        report_sheet2.cell(row_index_new + 1, 4, data['max_num'])
        report_sheet2.cell(row_index_new + 1, 5, data['type_num'])
        report_sheet2.cell(row_index_new + 1, 6, data["weight"])
        report_sheet2.cell(row_index_new + 1, 7, data['purity'])
        report_sheet2.cell(row_index_new + 1, 8, data['weight_purity'])
        report_sheet2.cell(row_index_new + 1, 9, data['weight_total'])

    report_sheet3 = output_wookbook.create_sheet(index=3, title=u"完整聚合度数据集")

    report_sheet3.cell(1, 1, "类别")
    report_sheet3.cell(1, 2, "簇个数")
    row_index_new_type = 0
    for (key, value) in completeness_avg_dict.items():
        row_index_new_type += 1
        report_sheet3.cell(row_index_new_type + 1, 1, key)
        report_sheet3.cell(row_index_new_type + 1, 2, value)

    output_wookbook.save("./output/cluster_evaluate_result.xlsx")
    output_wookbook.save("./display/files/cluster_evaluate_result_%s.xlsx" % conf.verison)
