#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2024/03/10 00:09:06
"""
import os
import time
from time import sleep

import openpyxl

from app.libs_ngd.large_model.request_large_model import RequestLargeModelObj
from config import LargeModelConfig, FileAddress


def request_gpt_file(file_path, prompt_template):
    """
    根据文件请求gpt，最终返回一个处理好的文件
    :param file_path:
    :param prompt_template:
    :return:
    """
    new_prompt_template = to_unicode(prompt_template)
    large_model_config = {
        "large_model_type": "gpt3.5",
        "api_key": LargeModelConfig.api_key,
        "prompt_template": new_prompt_template,
        "pattern": LargeModelConfig.default_pattern
    }
    obj = RequestLargeModelObj(large_model_config)
    excel_path = file_path
    data_list = []
    source_data = []
    excel_file = openpyxl.load_workbook(excel_path)
    sheet = excel_file.active
    # 逐行读取工作表中的数据，跳过首行
    for row in sheet.iter_rows(min_row=2, values_only=True):
        source_data_row = []
        # query = row[0]
        # actual_doc_name = row[1]
        # expected_doc_name = row[2]
        # model_response = row[3]
        # reference_answer = row[4]
        # search_source = row[5]
        for i in range(6):
            source_data_row.append(row[i])
        # query_info = {
        #     'id': '',
        #     'prompt_dict': {
        #         'query': query,
        #         'reference_answer': reference_answer,
        #         'model_response': model_response
        #     },
        #     'source': '',
        #     'negative': ''
        # }
        # data_list.append(query_info)
        source_data.append(source_data_row)
    workbook = openpyxl.Workbook()
    # 选择活动的工作表
    sheet = workbook.active
    sheet_head = ['query', u'实际检索文档名称', u'预期文档名称', u'实际检索', u'预期检索', 'search score', u'得分', u'原因', u'备注']
    sheet.append([unicode(d).encode('utf-8') for d in sheet_head])
    for row in source_data:
        data = {
            'query': row[0] if row[0] is not None else '',
            'reference_answer': row[4] if row[4] is not None else '',
            'model_response': row[3] if row[3] is not None else ''
        }
        print "data is %s" % data
        res = obj.request_large_model_and_parse_result(data)
        sleep(10)
        res_data = row + res
        sheet.append([unicode(d).encode('utf-8') for d in res_data])
    now_time = time.localtime()
    base_dir = FileAddress.result_dir
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    str_time_now = time.strftime("%Y%m%d%H%M%S", now_time)
    dir = base_dir + "/" + "file_search_res_result_%s.xlsx" % str_time_now
    workbook.save(dir)
    return dir


def to_unicode(s):
    print("s type is %s" % type(s))
    # 如果s是字节串，则解码为Unicode字符串
    if isinstance(s, str):
        try:
            # 尝试使用UTF-8解码（或选择适合你数据的其他编码）
            return s.decode('utf-8')
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，可以尝试其他编码，或处理错误
            print("解码错误：可能不是有效的UTF-8编码字符串")
            # 或者返回原字符串，或使用其他方式处理错误
            return s
    elif isinstance(s, unicode):
        # 如果s已经是Unicode字符串，直接返回
        return s
    else:
        # 如果s既不是str也不是unicode（例如，如果s是数字），则抛出错误或进行其他处理
        raise ValueError("输入必须是str或unicode类型")




if __name__ == '__main__':
    prompt_template = """你是专业的评测人员，请根据问题和参考答案对模型输出进行准确性进行打分，分档0、1、2，具体的打分标准请参照分档描述和注意事项，此外你还需要提供打分和对应的评分依据，你的回答请按照
【得分】xxx
【原因】xxx
的格式输出，注意得分和原因之间要进行换行
下面给出分档描述、问题、参考答案、模型输出以及注意事项
【分档描述】
0: 模型输出与参考答案语义和相关数据信息完全不符合
1:模型输出与参考答案语义和相关数据信息部分符合，但是允许存在不完整、冗余或者部分错误的情况
2:模型输出与参考答案语义和相关数据信息完全符合
【注意事项】
1.如果模型输出有关于来源的描述，例如如果模型中有"答案由一言生成"、"来源xxx"等内容，请将内容进行忽略
【问题】{query}
【参考答案】{reference_answer}
【模型输出】{model_response}
"""
    print request_gpt_file('/Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate/data/result_dir/guangdong_result_20240701173700.xlsx', prompt_template)