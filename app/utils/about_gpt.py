#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide http request.

Authors: <AUTHORS>
Date:    2023/09/05 22:04:06
"""
import re
import requests
import json


def create_prompt(template, prompt_dict):
    """
    创建prompt
    :param template: prompt模板，需要替换的变量请用{变量名}进行替换
    :param prompt_dict: prompt模板中需要的参数
    :return: 返回一个完整的prompt
    """
    for key, value in prompt_dict.items():
        template = template.replace("{{{}}}".format(key), value)
    return template


def request_gpt(api_key, prompt):
    """
    通过接口请求chat gpt
    :param api_key: openai的api key
    :param prompt: 用户文本
    :return:
    """
    url = "https://api.openai.com/v1/chat/completions"
    payload = json.dumps({
        "model": "gpt-3.5-turbo",
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ]
    })
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + api_key
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    print response.text
    res_text = response.json()["choices"][0]["message"]["content"]
    return res_text


def get_key_by_answer_text(answer_text, pattern, key_num):
    """
    从答案文本answer_text中，根据正则表达式re_str，提取key_num个关键词
    :param answer_text: 答案文本
    :param pattern: 正则表达式
    :param key_num: 关键词个数
    :return:
    """
    key_list = []
    search = re.search(pattern, answer_text)
    if search:
        for i in range(1, key_num + 1):
            key_list.append(search.group(i))
        return key_list
    else:
        return None


if __name__ == '__main__':
#     # 0.定义需要的参数
#     template = """
# 你是专业的评测人员，请根据问题和参考答案对模型输出进行准确性进行打分，分档0、1、2，具体的打分标准请参照分档描述，此外你还需要提供打分和对应的评分依据，如果模型回答中有关于来源的描述请忽略该描述在进行作答，你的回答请按照
# 【得分】xxx
# 【原因】xxx
# 的格式输出，注意得分和原因之间要进行换行
# 下面给出分档描述、问题、参考答案和模型输出
# 【分档描述】
# 0: 模型输出与参考答案完全不符合
# 1:模型输出与参考答案语义基本符合,但是允许存在不完整、冗余或者部分错误的情况
# 2:模型输出与参考答案语义完全符合
# 【问题】{query}
# 【参考答案】{reference_answer}
# 【模型输出】{model_response}
#         """
#     prompt_dict = {
#         "query": "6个免费产业都有啥",
#         "reference_answer": "适应产业: 新一代电子信息,模具和机械制造,饲料",
#         "model_response": "新一代电子信息，模具和机械炜制造，饲料。以上答案由一言生成，来源：《项目建设六项免费服务.docx》、《企业技术改造支持政策.docx》"
#     }
#     api_key = '***************************************************'
#     pattern = ur"【得分】([012])\n【原因】([\u4e00-\u9fa5]+)"
#     # 1.组装prompt
#     prompt = create_prompt(template, prompt_dict)
#     print prompt
#     # 2.调用大模型
#     res_text = request_gpt(api_key, prompt)
#     # 3.解析结果
#     key_list = get_key_by_answer_text(res_text, pattern, 2)
#     print key_list

    # pattern = ur"【得分】([012])\n【原因】([\u4e00-\u9fa5]+)"
    pattern = ur"【得分】([012])\n【原因】(.*)"
#     res_text = u"""
# 【得分】1
# 【原因】模型输出与参考答案语义基本符合，但存在部分错误和冗余。
# 详细评分依据：
# 参考答案中提到了四种情况下的奖励金额，而模型输出中也包含了这四种情况，并且金额也是正确的（630万元和200万元），因此语义上基本符合。
# 然而，模型输出中出现了一些冗余和错误的部分。例如，在第2个答案中，模型多次重复了引号和多余的双引号，这是冗余和错误的。此外，在第4个答案中，模型使用了"挂牌"而不是"挂牌给予奖补"，这也是一个错误。
# 综上所述，模型输出与参考答案语义基本符合，但存在部分错误和冗余，因此给予0.5的得分。"""
#     key_list = get_key_by_answer_text(res_text, pattern, 2)
#     print key_list
    print pattern

