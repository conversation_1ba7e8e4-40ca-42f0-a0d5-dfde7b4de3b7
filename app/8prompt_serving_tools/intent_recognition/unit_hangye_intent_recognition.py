# coding:utf8
'''
@author:da<PERSON><PERSON><PERSON><PERSON>
@date:2024-08-21
@description:直接访问环境中的意图识别接口做评测

curl -X 'POST' \
  'http://************:8080/prompt/intention_recognition' \
  -H 'Content-Type: application/json' \
  -d '{
        "session_id": "041a6d77-70c0-4dbb-bf1d-bd3f77e6be91",
        "intent_list": [
            {"name": "预订酒店", "explanation": "", "sample": ""},
            {"name": "预订机票", "explanation": "", "sample": ""},
            {"name": "查话费", "explanation": "", "sample": ""},
            {"name": "车辆保养", "explanation": "", "sample": ""},
        ],
        "conversation": [
            {"role": "客服", "content": "您好，有什么可以帮助您？"},
        ],
        "query": {"role": "用户", "content": "预订"}
    }'



'''
import requests
import time
import openpyxl
import argparse
import uuid
import json
import re

from openpyxl import Workbook

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('ip', help='prompt_serving服务所在机器ip')  # prompt_serving服务所在机器ip  ************
parser.add_argument('port', help='prompt_serving服务所在机器的node port')  # prompt_serving服务所在机器的node port 8080
parser.add_argument('api', help='prompt_serving服务提供的api')  # prompt_serving服务提供的api /prompt/intention_recognition
# prompt_serving服务模型："kasmo", "EB-Speed", "EB-3.5", "EB-4.0", "EB-4.0-Turbo"
parser.add_argument('model', help='prompt_serving服务模型，"kasmo", "EB-Speed", "EB-3.5", "EB-4.0", "EB-4.0-Turbo"')
parser.add_argument('hangye', help='测试集行业：all（全部）、jiaoyu（教育）、qiche（汽车）、keji（科技）、baoxian（保险）、dianshang（电商）')
parser.add_argument('scene', help='单/多意图场景：all（全部）、single_intent（单意图）、multi_intent（多意图）')
parser.add_argument('sleep_time', help='query请求间隔时间，单位秒，默认1秒，最小1秒，最大60秒')  # query请求间隔时间，单位秒，默认1秒，最小1秒，最大60秒
parser.add_argument('version', help='当前测试时，客悦专业版的版本，如：1.3.2')  # 当前测试时，客悦专业版的版本，如：1.3.2

# 解析并提取命令行参数
args = parser.parse_args()
ip = args.ip
port = args.port
api = args.api
sleep_time = int(args.sleep_time)
model = args.model
version = args.version
hangye_param = args.hangye
scene = args.scene
if sleep_time < 1:
    sleep_time = 1
elif sleep_time > 60:
    sleep_time = 60
# 拼接请求的url
url = "http://" + ip + ":" + port + api


def parse_excel(excel_name, sheet_names):
    """
    解析excel表格数据，存储为字典+数组
    :param excel_name: excel文件名
    :param sheet_names: sheet名
    :return: tables对象，key为sheet名，value为表格数据
    """
    workbook = openpyxl.load_workbook(excel_name)
    tables = {}
    # 通过工作表名称选择当前活动的工作表
    for sheet_name in sheet_names:
        sheet = workbook[sheet_name]
        table = {}
        # 标志变量，用于标记是否已经处理表头
        header_processed = False
        # 有数据的行号
        row_index = 1

        for row in sheet.iter_rows(min_row=1, values_only=True):
            # 判断当前行是否有数据，如果为空，则跳过当前循环
            if any(cell is not None for cell in row):
                if not header_processed:
                    # 处理表头
                    for cell_value in row:
                        table[cell_value] = []
                    header_processed = True
                else:
                    # 处理每一行数据
                    for col_index in range(sheet.max_column):
                        cell_value = row[col_index]
                        if cell_value is None:
                            table[sheet.cell(row=1, column=col_index + 1).value].append("")
                            continue
                        table[sheet.cell(row=1, column=col_index + 1).value].append(cell_value)

                row_index += 1
        tables[sheet_name] = table
    return tables


def get_session_id():
    """
    生成session_id
    :return: session_id
    """
    return str(uuid.uuid4())


def due_prompt(file_name):
    """
    prompt预处理：读取prompt文件，获取意图列表、用户query和期望识别意图

    :param file_name: prompt文件名
    :return: intent_list_arr 意图信息列表、用户query和期望识别意图
    """

    # 保存意图信息列表、用户query和期望识别意图
    intent_list_arr = []

    # 读取prompt文件
    file_name = "./{}".format(file_name)
    with open(file_name, 'r', encoding='utf-8') as readFile:
        for promptLine in readFile:
            # 将文件中的每一行数据转换为json格式
            json_data = json.loads(promptLine)
            # 提取prompt
            prompt = json_data[0]['prompt']
            # 提取response
            query_answer_old = json_data[0]['response'][0][0]

            # 提取prompt中的意图列表
            start_index = prompt.find("| 意图名称 | 意图解释 | 意图例句 |") + len("| :--: | :--: | :--: |")
            end_index = prompt.find("###历史对话###")
            intent_list = prompt[start_index:end_index]
            lines = intent_list.split('\n')
            intent_list_dit = {}
            intent_list_arr_data = []
            for line in lines:
                # 通过正则提取：意图名、意图描述、意图示例
                match1 = re.search(r'\| (.+?) \|', line)
                match2 = re.search(r'\| (.+?) \| (.+?) \|', line)
                match3 = re.search(r'\| (.+?) \| (.+?) \| (.+?) \|', line)
                # If a match was found
                if match1:
                    intent_names = match1.group(1)
                    intent_description = match2.group(2)
                    intent_examples = match3.group(3)
                    if intent_names not in (':--:', '不相关', '问题咨询'):
                        intent_list_arr_data1 = {}
                        intent_list_arr_data1['name'] = intent_names
                        intent_list_arr_data1['explanation'] = intent_description.replace('-', '')
                        intent_list_arr_data1['sample'] = intent_examples.replace('-', '')
                        intent_list_arr_data.append(intent_list_arr_data1)
            # 1. 保存参考意图列表
            intent_list_dit['intent_list'] = intent_list_arr_data

            # 提取prompt中的用户query
            start_index_query = prompt.find("###用户当前Query###") + len("###用户当前Query###")
            end_index_query = prompt.find("###输出结果###")
            query_list = prompt[start_index_query:end_index_query]
            query = query_list.split('用户：')

            # 2. 保存用户query内容
            intent_list_dit['query_content'] = query[1].strip()

            # 3. 保存期望识别意图
            intent_list_dit['expected_intent'] = json.loads(query_answer_old).get("意图")

            # 当prompt有字符串 "###历史对话###\n空\n" 时，表示没有会话历史记录，不需要提取会话历史记录
            if "###历史对话###\n空\n" in prompt:
                intent_list_dit['conversation'] = []
            # 当prompt没有字符串   "###历史对话###\n空\n"  时，表示有会话历史记录，需要提取会话历史记录
            else:
                start_index_conversation = prompt.find("###历史对话###") + len("###历史对话###")
                end_index_conversation = prompt.find("###用户当前Query###")
                conversation_list = prompt[start_index_conversation:end_index_conversation].strip().split("\n")

                conversation_dict_list = []
                for conversation in conversation_list:
                    conversation = conversation.split('：')
                    if conversation[0] == "客服":
                        conversation_dict = {
                            "role": "客服",
                            "content": conversation[1].strip()
                        }
                    elif conversation[0] == "用户":
                        conversation_dict = {
                            "role": "用户",
                            "content": conversation[1].strip()
                        }
                    else:
                        # 有时prompt的会话历史记录中存在错值，比如既不是客服也不是用户，而是其他角色
                        # 此时就根据上一个会话历史记录的role来赋值，如上一个是客服，则该会话历史记录的role就是客服，反之同理
                        # 取上一个的时候注意下标是否越界
                        if len(conversation_dict_list) == 0:
                            # 默认第1个role为客服
                            conversation_dict = {
                                "role": "客服",
                                "content": conversation[1].strip()
                            }
                        elif len(conversation) == 1:
                            # 如果通过冒号分割出来的内容没有角色
                            # 有可能是当前角色的query内容换行导致，则默认这个会话历史为上一个角色
                            conversation_dict = {
                                "role": conversation_dict_list[-1].get("role"),
                                "content": conversation[0].strip()
                            }
                        else:
                            last_role = conversation_dict_list[-1].get("role")
                            # 如果上一个角色是用户，则该会话历史记录的role就是客服
                            if last_role == "用户":
                                conversation_dict = {
                                    "role": "客服",
                                    "content": conversation[1].strip()
                                }
                            # 如果上一个角色是客服，则该会话历史记录的role就是用户
                            elif last_role == "客服":
                                conversation_dict = {
                                    "role": "用户",
                                    "content": conversation[1].strip()
                                }
                            # 如果上一个角色是其他，则该会话历史记录的role就是客服
                            else:
                                conversation_dict = {
                                    "role": "客服",
                                    "content": conversation[1].strip()
                                }

                    # 追加到格式化的会话记录列表中
                    conversation_dict_list.append(conversation_dict)
                # 4. 保存会话历史记录
                intent_list_dit['conversation'] = conversation_dict_list

            # 追加当前prompt的关键信息到列表中
            intent_list_arr.append(intent_list_dit)

    return intent_list_arr


def query(session_id, model, intent_list, conversation, query_content):
    """
    请求 prompt_serving 服务的接口
    :param session_id: session_id
    :param model: 模型
    :param intent_list: 意图列表
    :param conversation: 会话历史记录
    :param query_content: 用户query内容
    :return: 返回响应的json数据
    """
    headers = {
        "Content-Type": "application/json",
    }
    query = {
        "session_id": session_id,
        "model": model,
        "intent_list": intent_list,
        "conversation": conversation,
        "query": {"role": "用户", "content": query_content}
    }

    http_post = requests.post(url=url, json=query, headers=headers,
                              stream=True, timeout=60)
    return http_post.json()


def query_control(hangye_intent_list, hangye_query_list):
    """
    控制请求 prompt_serving 服务的接口
    :param hangye_intent_list: 行业意图列表
    :param hangye_query_list: 行业query列表（包含有期望意图或澄清意图语句列表）
    :return:
    """

    all_query_answer_data_list = {}
    # 遍历每个行业
    for hangye in hangye_intent_list.keys():
        intent_list = hangye_intent_list.get(hangye)

        # 遍历每个行业下的场景，如：科技-单、科技-多
        for key in hangye_query_list.keys():
            query_answer_data_list = []
            # 一个行业可能会有单、多意图场景，根据存储的key来判断，非本行业的跳过
            if key.startswith(hangye):
                query_content_list = hangye_query_list.get(key).get('query_content')
                expected_intent_list = hangye_query_list.get(key).get('expected_intent')

                # 遍历每个场景下的query与期望的意图
                for i in range(len(query_content_list)):
                    session_id = get_session_id()
                    conversation = []
                    query_content = query_content_list[i]
                    expected_intent = expected_intent_list[i].replace("，", ",").split(',')
                    # 请求 prompt_serving 服务的接口
                    response = query(session_id=session_id,
                                     model=model,
                                     intent_list=intent_list,
                                     conversation=conversation,
                                     query_content=query_content)
                    actual_intent = response.get('result').get("intent")
                    actual_intent_clarify_result = response.get('result').get("clarify")  # True or False
                    actual_intent_clarify_sentence = response.get('result').get("clarify_sent")
                    # 判断是否识别正确
                    # 1. 首先判断实际识别的意图数量和期望的意图数量是否一致，如果不一致则认为识别错误
                    # 2. 然后再判断，如果期望的意图全部在识别结果中，则认为识别正确
                    intent_recognition_result = True
                    if len(actual_intent) != len(expected_intent):
                        intent_recognition_result = False
                    else:
                        for intent in actual_intent:
                            # 只要有一个意图不在期望的意图列表中，则认为识别错误
                            if intent not in expected_intent:
                                intent_recognition_result = False

                    # # 整理所有关键数据
                    query_answer_data = {}
                    query_answer_data['hangye'] = hangye
                    query_answer_data['intent_scene'] = key
                    query_answer_data['session_id'] = session_id
                    query_answer_data['query_content'] = query_content
                    query_answer_data['expected_intent'] = expected_intent
                    query_answer_data['actual_intent'] = actual_intent
                    query_answer_data['intent_list'] = intent_list
                    query_answer_data['conversation'] = conversation
                    query_answer_data['response'] = response
                    query_answer_data['actual_intent_clarify_result'] = actual_intent_clarify_result
                    query_answer_data['actual_intent_clarify_sentence'] = actual_intent_clarify_sentence
                    query_answer_data['intent_recognition_result'] = intent_recognition_result
                    query_answer_data_list.append(query_answer_data)
                    print("请求结果：{}".format(query_answer_data))

                    # 请求间隔一段时间，避免请求过于频繁
                    time.sleep(sleep_time)
                all_query_answer_data_list[key] = query_answer_data_list
    return all_query_answer_data_list


def generate_html_table(url, intent_scene, query_answer_data_list):
    """
    生成html表格，展示请求数据

    :param url: 请求的url
    :param intent_scene: 意图场景
    :param query_answer_data_list: 每个意图场景关键信息数据列表
    :return:
    """
    # 统计所有prompt请求中，预期意图识别正确率
    intent_recognition_correct = 0
    total_prompt_num = len(query_answer_data_list)
    for row in query_answer_data_list:
        if row.get('intent_recognition_result'):
            intent_recognition_correct += 1
    intent_recognition_rate = round(intent_recognition_correct / total_prompt_num * 100, 2)

    html = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><title>客悦专业版-意图识别评测报告</title></head>"
    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0'><tr>\
                <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
                <strong>客悦专业版{version}，行业：{intent_scene}意图场景 \
                 意图识别评测报告，直接请求 prompt_serving 服务的接口，模型【{model}】，url为【{url}】<br>\
                共【{total_prompt_num}】条query，正确【{intent_recognition_correct}】条，\
                错误【{total_prompt_num - intent_recognition_correct}】条，意图识别正确率为：\
                【<span style=\"color: red;\">{intent_recognition_rate}%</span>】\
                query以及结果如下（黑色字体表示识别正确，<span style=\"color: red;\">红色字体</span>表示识别错误）<br>\
                判断规则：(注：触发澄清的也算在失败中)<br>\
                1. 首先判断实际识别的意图数量和期望的意图数量是否一致，如果不一致则认为识别错误<br>\
                2. 如果数量一致，然后再判断，如果期望的意图全部在识别结果中，则认为识别正确<br>\
                点击右侧按钮后，可以在 [全部显示]/[只显示识别错误或澄清的内容] 两种方式中进行切换\
                <button id='toggleButton' style='color: blue'>切换显示</button>\
                </strong></td></tr>"

    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0' id='detail'>\
                <thead><tr bgcolor='yellow'>\
                <td width='100px' align='center'><strong>session id</strong></td>\
                <td width='100px' align='center'><strong>历史会话记录</strong></td>\
                <td width='100px' align='center'><strong>用户query</strong></td>\
                <td width='120px' align='center'><strong>实际识别的意图</strong></td>\
                <td width='120px' align='center'><strong>期望识别的意图</strong></td>\
                <td width='100px' align='center'><strong>回复是否澄清</strong></td>\
                <td width='100px' align='center'><strong>回复澄清语句</strong></td>\
                <td width='800px' align='center'><strong>请求传参的意图列表</strong></td>\
                <td width='auto' align='center'><strong>响应体</strong></td>\
                </tr></thead>"

    # 打印每个prompt的请求数据详情
    html += "<tbody>"
    for row in query_answer_data_list:
        session_id = row.get('session_id')
        query_content = row.get('query_content')
        actual_intent = row.get('actual_intent')
        expected_intent = row.get('expected_intent')
        intent_list = row.get('intent_list')
        conversation = row.get('conversation')
        intent_clarify_result = row.get('actual_intent_clarify_result')
        intent_clarify_sentence = row.get('actual_intent_clarify_sentence')
        response = row.get('response')
        intent_recognition_result = row.get('intent_recognition_result')

        # html表格的一行开始
        if intent_recognition_result:
            html += "<tr>"
        else:
            html += "<tr style='color:red;font-weight:bold'>"

        # 展示sessionId
        html += f"<td>session_id：<br>{session_id}</td>"
        # 展示历史会话记录
        html += f"<td>历史会话记录：<br>"
        for i in range(len(conversation)):
            html += f"{conversation[i]}<br>"
        html += "</td>"
        # 展示用户query
        html += f"<td>query：<br>{query_content}</td>"
        # 展示实际识别的意图
        html += f"<td>实际意图：<br>{actual_intent}</td>"
        # 展示期望识别的意图
        html += f"<td>期望意图：<br>{expected_intent}</td>"
        # 展示回复是否澄清
        html += f"<td>回复是否澄清：<br>{intent_clarify_result}</td>"
        # 展示回复澄清语句
        html += f"<td>回复澄清语句：<br>{intent_clarify_sentence}</td>"
        # 展示请求传参的意图列表
        html += f"<td>请求意图列表：<br>"
        for i in range(len(intent_list)):
            html += f"{intent_list[i]}<br>"
        html += "</td>"
        # 展示响应体 ensure_ascii=False为False，则正常输出中文，否则输出unicode编码
        html += f"<td>响应体：<br>{json.dumps(response, ensure_ascii=False)}</td>"

        # html表格的一行结束
        html += "</tr>"

    html += "</tbody></table>"
    # 添加一个按钮，点击后可以显示/隐藏所有识别错误的行，增加对应的js代码
    html += """<script>  
        let isFiltered = false;  

        function toggleTableRows() {  
            const rows = document.querySelectorAll("#detail tbody tr");  
            rows.forEach(row => {  
                if (isFiltered) {  
                    // 如果当前是在“隐藏”模式，显示所有行  
                    row.style.display = "";  
                } else {  
                    // 如果当前是在“显示”模式，只显示带样式的行  
                    if (row.getAttribute("style") === "color:red;font-weight:bold") {  
                        row.style.display = "";  
                    } else {  
                        row.style.display = "none";  
                    }  
                }  
            });  
            isFiltered = !isFiltered; // 切换状态  
        }  

        document.getElementById("toggleButton").addEventListener("click", toggleTableRows);  
    </script>"""
    html += "</body></html>"
    return html


def main():
    """
    主函数
    :return:
    """
    # 第1步：读取excel文件，获取意图列表和query列表
    # 意图列表所在excel文件
    intent_excel_name = "5-hangye-intent-list.xlsx"
    intnet_sheet_names = ['教育', '汽车', '科技', '保险', '电商']
    hangye_map = {
        "all": "all",
        "jiaoyu": "教育",
        "qiche": "汽车",
        "keji": "科技",
        "baoxian": "保险",
        "dianshang": "电商"
    }
    hangye = hangye_map.get(hangye_param)

    # 获取5个行业的意图列表
    all_intent_list = parse_excel(intent_excel_name, intnet_sheet_names)

    # query所在的excel文件
    query_excel_name = "unit7QAIntentEndToEnd.xlsx"
    query_sheet_names = ['教育-单', '教育-多',
                         '汽车-单', '汽车-多',
                         '科技-单', '科技-多',
                         '保险-单', '保险-多',
                         '电商-单', '电商-多']
    query_sheet_maps = {
        "教育-单": "jiaoyu-dan",
        "教育-多": "jiaoyu-duo",
        "汽车-单": "qiche-dan",
        "汽车-多": "qiche-duo",
        "科技-单": "keji-dan",
        "科技-多": "keji-duo",
        "保险-单": "baoxian-dan",
        "保险-多": "baoxian-duo",
        "电商-单": "dianshang-dan",
        "电商-多": "dianshang-duo"
    }
    # 获取5个行业的query列表
    all_query_list = parse_excel(query_excel_name, query_sheet_names)

    # 第2步：提取excel有用的信息
    # 根据传入的参数 hangye 来构造不同行业的意图、query、expected_intent列表，将意图、query列表转换为字典格式，用于API接口的参数
    hangye_intent_list = {}
    hangye_query_list = {}
    if hangye == "all":
        # 测试所有行业的意图
        for hy in intnet_sheet_names:
            # 将意图列表转换为字典格式，用于API接口的参数
            hangye_intent_list[f'{hy}'] = [
                {
                    "name": name,
                    "explanation": explanation,
                    "sample": sample}
                for name, explanation, sample in zip(
                    all_intent_list.get(hy).get("name"),
                    all_intent_list.get(hy).get("explanation"),
                    all_intent_list.get(hy).get("sample"))
            ]

            # 提取query、expected_intent列表
            if scene == "all":
                sheet_name = f"{hy}-单"
                hangye_query_list[sheet_name] = {
                    "query_content": all_query_list.get(sheet_name).get("query"),
                    "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
                }
                sheet_name = f"{hy}-多"
                hangye_query_list[sheet_name] = {
                    "query_content": all_query_list.get(sheet_name).get("query"),
                    "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
                }
            elif scene == "single_intent":
                sheet_name = f"{hy}-单"
                hangye_query_list[sheet_name] = {
                    "query_content": all_query_list.get(sheet_name).get("query"),
                    "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
                }
            elif scene == "multi_intent":
                sheet_name = f"{hy}-多"
                hangye_query_list[sheet_name] = {
                    "query_content": all_query_list.get(sheet_name).get("query"),
                    "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
                }

        print(f"测试【{hangye}】的意图列表为：{hangye_intent_list}")
        print(f"测试【{hangye}】的query、expected_intent列表为：{hangye_query_list}")
    else:
        # 测试指定行业的意图
        hangye_intent_list = {
            f"{hangye}": [
                {
                    "name": name,
                    "explanation": explanation,
                    "sample": sample}
                for name, explanation, sample in zip(
                    all_intent_list.get(hangye).get("name"),
                    all_intent_list.get(hangye).get("explanation"),
                    all_intent_list.get(hangye).get("sample"))
            ]
        }

        # 提取query、expected_intent列表
        if scene == "all":
            sheet_name = f"{hangye}-单"
            hangye_query_list[sheet_name] = {
                "query_content": all_query_list.get(sheet_name).get("query"),
                "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
            }
            sheet_name = f"{hangye}-多"
            hangye_query_list[sheet_name] = {
                "query_content": all_query_list.get(sheet_name).get("query"),
                "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
            }
        elif scene == "single_intent":
            sheet_name = f"{hangye}-单"
            hangye_query_list[sheet_name] = {
                "query_content": all_query_list.get(sheet_name).get("query"),
                "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
            }
        elif scene == "multi_intent":
            sheet_name = f"{hangye}-多"
            hangye_query_list[sheet_name] = {
                "query_content": all_query_list.get(sheet_name).get("query"),
                "expected_intent": all_query_list.get(sheet_name).get("expected_intent")
            }

        print(f"测试【{hangye}】的意图列表为：{hangye_intent_list}")
        print(f"测试【{hangye}】的query、expected_intent列表为：{hangye_query_list}")

    # 第3步：请求query控制函数，将结果进行整理汇总
    all_query_answer_data_list = query_control(hangye_intent_list, hangye_query_list)

    print(all_query_answer_data_list)

    # 每4步：输出html结果文件并保存
    # 循环处理每个行业的意图场景，单独生成报告
    for intent_scene in all_query_answer_data_list.keys():
        print(f"开启处理报告文件：{intent_scene}")

        # 生成HTML表格
        html_content = generate_html_table(url=url,
                                           intent_scene=intent_scene,
                                           query_answer_data_list=all_query_answer_data_list.get(intent_scene))

        # 将HTML代码写入文件
        try:
            with open(f"{query_sheet_maps.get(intent_scene)}-report.html", "w", encoding='utf-8') as f:
                f.write(html_content)
        except Exception as e:
            print(e)

        print(f"报告文件【{query_sheet_maps.get(intent_scene)}-report.html】生成成功！")

    # 第5步：将query识别的意图结果与原表综合，再写入新excel文件
    for key in all_query_answer_data_list.keys():
        all_query_list[key]["prompt_serving_intent"] = []
        for i in range(len(all_query_answer_data_list.get(key))):
            actual_intent = all_query_answer_data_list.get(key)[i]['actual_intent']
            actual_intent_str = ",".join(actual_intent)
            all_query_list[key]["prompt_serving_intent"].append(actual_intent_str)
    write_to_excel("prompt_serving_identify_result.xlsx", all_query_list)
    print("写入原query excel完成，新excel名为：prompt_serving_identify_result.xlsx")


def write_to_excel(file_name, data):
    """
    将数据写入到excel文件中
    :param file_name: 文件名
    :param data: 数据
    :return:
    """
    # 创建一个Excel文件
    wb = Workbook()
    # 根据意图场景，创建不同的sheet
    for key in data.keys():
        ws = wb.create_sheet(title=key)
        sheet_data = data.get(key)
        # 将数据写入到sheet中
        for i, (col_name, col_data) in enumerate(sheet_data.items()):
            ws.cell(row=1, column=i + 1).value = col_name
            for j, value in enumerate(col_data):
                ws.cell(row=j + 2, column=i + 1).value = value
    # 保存Excel文件
    wb.save(file_name)


if __name__ == '__main__':
    main()
