# coding:utf8
'''
@author:<PERSON><PERSON><PERSON><PERSON><PERSON>
@date:2024-12-07
@description:直接访问prompt_serving服务的实体抽取接口做评测
'''
import requests
import time
import openpyxl
import argparse
import uuid
import json
import re

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('ip', help='prompt_serving服务所在机器ip')  # prompt_serving服务所在机器ip  ************
parser.add_argument('port', help='prompt_serving服务所在机器的node port')  # prompt_serving服务所在机器的node port 8080
parser.add_argument('api', help='prompt_serving服务提供的api')  # prompt_serving服务提供的api /prompt/slot_filling
# prompt_serving服务模型："kasmo", "EB-Speed", "EB-3.5", "EB-4.0", "EB-4.0-Turbo"
parser.add_argument('model', help='prompt_serving服务模型，"kasmo", "EB-Speed", "EB-3.5", "EB-4.0", "EB-4.0-Turbo"')
parser.add_argument('sleep_time', help='query请求间隔时间，单位秒，默认1秒，最小1秒，最大60秒')  # query请求间隔时间，单位秒，默认1秒，最小1秒，最大60秒
parser.add_argument('version', help='当前测试时，客悦专业版的版本，如：1.3.2')  # 当前测试时，客悦专业版的版本，如：1.3.2
parser.add_argument('compare_words_value_threshold', help='文本相似度阈值')  # 文本相似度阈值

# 解析并提取命令行参数
args = parser.parse_args()
ip = args.ip
port = args.port
api = args.api
sleep_time = int(args.sleep_time)
model = args.model
version = args.version
compare_words_value_threshold = float(args.compare_words_value_threshold)
if sleep_time < 1:
    sleep_time = 1
elif sleep_time > 60:
    sleep_time = 60
# 拼接请求的url
url = "http://" + ip + ":" + port + api


def parse_excel(excel_name, sheet_names):
    """
    解析excel表格数据，存储为字典+数组
    :param excel_name: excel文件名
    :param sheet_names: sheet名
    :return: tables对象，key为sheet名，value为表格数据
    """
    workbook = openpyxl.load_workbook(excel_name)
    tables = {}
    # 通过工作表名称选择当前活动的工作表
    for sheet_name in sheet_names:
        sheet = workbook[sheet_name]
        table = {}
        for row_index in range(1, sheet.max_row + 1):
            # 拿到表头数据名
            if row_index == 1:
                for col_index in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=1, column=col_index)
                    table[cell.value] = []
                continue
            # 存储第个表头下的所有数据，以表头为key，数据为value数组
            for col_index in range(1, sheet.max_column + 1):
                cell = sheet.cell(row=row_index, column=col_index)
                table[sheet.cell(row=1, column=col_index).value].append(cell.value)

        tables[sheet_name] = table
    return tables


def get_session_id():
    """
    生成session_id
    :return: session_id
    """
    return str(uuid.uuid4())


def calculate_similarity(str1, str2):
    """
    调用客悦专业版的agent，计算两个字符串的相似度（调用时，注意请求频率，不要占用太多线上资源）
    所在租户：dcj36513（生产环境）
    agent：文本相似度对比打分专用
    :param str1: 第一个字符串
    :param str2: 第二个字符串
    :return: 相似度分数
    """
    headers = {
        "token": "39a0333a-9c18-4e70-a029-22a9d9ccee9b"
    }
    sessionId = str(uuid.uuid4())

    try:
        # 先发空query
        data = {
            "queryText": "",
            "sessionId": sessionId,
            "channel": "_sys_web"
        }
        requests.post(
            url="https://keyue.cloud.baidu.com/online/core/v5/stream/query",
            headers=headers,
            json=data
        )
        # 再发真实请求
        data = {
            "queryText": str1 + "##########" + str2,
            "sessionId": sessionId,
            "channel": "_sys_web"
        }
        response = requests.post(
            url="https://keyue.cloud.baidu.com/online/core/v5/stream/query",
            headers=headers,
            json=data
        )
        http_data = b''
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                http_data += chunk
        http_response = http_data.decode('utf-8')
        res_text = json.loads(re.findall(r'data:(.*)', http_response)[0])
        similarity_score = float(res_text.get("variables").get("sim_value"))
        print(f"文本1 ==> {str1}\n文本2 ==> {str2}\n二者相似度分数：{similarity_score}\n")
        return similarity_score
    except Exception as e:
        print(e)


def due_prompt(prompt):
    """
    prompt预处理：读取prompt内容，获取参考的收集实体列表、用户与客服对话内容

    :param prompt: prompt内容
    :return: slot_list, conversation_list: 参考的收集实体列表、用户与客服对话内容
    """
    # 正则匹配出需要收集的信息
    match = re.findall(r"(###需要收集的信息###)([\s\S]*?)(###对话内容###)", prompt)
    slot_list = []
    if match:
        for slot in match[0][1].strip('\n').split("\n"):
            slot_and_candidate = slot.split("，")
            candidate_list = []
            if len(slot_and_candidate) == 2:
                for candidate in re.findall(r'】([\S]*)', slot_and_candidate[1]):
                    candidate_list.append(candidate)

            expected_slot = {
                "item_name": slot_and_candidate[0],
                "candidate_list": candidate_list
            }
            slot_list.append(expected_slot)

    # 正则匹配出对话内容
    match = re.findall(r"(###对话内容###)([\s\S]*?)(###输出###)", prompt)
    conversation_list = []
    if match:
        for conversation in match[0][1].strip('\n').split("\n"):
            conversation = conversation.split('：')
            if conversation[0] == "客服":
                conversation_dict = {
                    "role": "客服",
                    "content": conversation[1].strip()
                }
            elif conversation[0] == "用户":
                conversation_dict = {
                    "role": "用户",
                    "content": conversation[1].strip()
                }
            else:
                conversation_dict = {}
            # 追加到格式化的会话记录列表中
            conversation_list.append(conversation_dict)
    return slot_list, conversation_list


def query(session_id, model, slot_list, conversation):
    """
    请求 prompt_serving 服务的接口
    :param session_id: session_id
    :param model: 模型
    :param slot_list: 收集实体列表
    :param conversation: 会话历史记录
    :return: 返回响应的json数据
    """
    headers = {
        "Content-Type": "application/json",
    }
    query = {
        "session_id": session_id,
        "model": model,
        "slot_list": slot_list,
        "conversation": conversation,
        "example": []
    }

    http_post = requests.post(url=url, json=query, headers=headers,
                              stream=True, timeout=60)
    return http_post.json()


def query_control(slot_list_array, conversation_list_array, expected_response_list):
    """
    对每一组prompt中的实体列表进行测试
    :param slot_list_array: 收集实体列表数组
    :param conversation_list_array: 对话历史记录数组
    :param expected_response_list: 期望的收集实体列表
    :return: 每个 prompt 识别前后的一些关键信息记录
    """

    query_answer_data_list = []

    for index in range(len(slot_list_array)):
        query_answer_data = {}
        session_id = get_session_id()
        slot_list = slot_list_array[index]
        conversation = conversation_list_array[index]
        # 请求 prompt_serving 服务的接口
        http_response = query(session_id=session_id,
                              model=model,
                              slot_list=slot_list,
                              conversation=conversation)
        slot_result = http_response.get('result')
        # 调用客悦专业版客服，对比slot_result和expected_response_list[index]，进行相似度计算
        similarity_score = calculate_similarity(str(slot_result), str(expected_response_list[index]))

        # 整理所有关键数据
        query_answer_data['session_id'] = session_id
        query_answer_data['slot_list'] = slot_list
        query_answer_data['conversation'] = conversation
        query_answer_data['expected_response'] = expected_response_list[index]
        query_answer_data['slot_result'] = slot_result
        query_answer_data['similarity_score'] = similarity_score
        query_answer_data['http_response'] = http_response
        query_answer_data_list.append(query_answer_data)
        print("当前prompt请求结果：{}\n".format(query_answer_data))
        # 请求间隔一段时间，避免请求过于频繁
        time.sleep(sleep_time)

    return query_answer_data_list


def generate_html_table(url, query_answer_data_list):
    """
    生成html表格，展示请求数据

    :param url: 请求的url
    :param query_answer_data_list: 每个prompt的关键信息数据列表
    :return:
    """
    # 统计所有prompt请求中，预期实体抽取正确率
    slot_recognition_correct = 0
    total_prompt_num = len(query_answer_data_list)
    for row in query_answer_data_list:
        if row.get('similarity_score') >= compare_words_value_threshold:
            slot_recognition_correct += 1
    slot_recognition_rate = round(slot_recognition_correct / total_prompt_num * 100, 2)

    html = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><title>客悦专业版-实体抽取评测报告</title></head>"
    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0'><tr>\
                <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
                <strong>客悦专业版{version} prompt-serving实体抽取评测报告，直接请求 prompt_serving 服务的接口，模型【{model}】，url为【{url}】<br>\
                共【{total_prompt_num}】条prompt，正确【{slot_recognition_correct}】条，\
                错误【{total_prompt_num - slot_recognition_correct}】条，实体抽取正确率为：\
                【<span style=\"color: red;\">{slot_recognition_rate}%</span>】<br>\
                文本相似度阈值为：{compare_words_value_threshold}<br>\
                query以及结果如下（黑色字体表示抽取正确，<span style=\"color: red;\">红色字体</span>表示调取错误）<br>\
                点击右侧按钮后，可以在 [全部显示]/[只显示抽取错误的内容] 两种方式中进行切换\
                <button id='toggleButton' style='color: blue'>切换显示</button>\
                </strong></td></tr></table>"

    html += f"<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0' id='detail'>\
                <thead><tr bgcolor='yellow'>\
                <td width='100px' align='center'><strong>session id</strong></td>\
                <td width='200px' align='center'><strong>候选实体列表</strong></td>\
                <td width='400px' align='center'><strong>对话记录</strong></td>\
                <td width='120px' align='center'><strong>期望抽取的实体详情</strong></td>\
                <td width='120px' align='center'><strong>实际抽取的实体详情</strong></td>\
                <td width='50px' align='center'><strong>相似度</strong></td>\
                <td width='auto' align='center'><strong>http完整响应</strong></td>\
                </tr></thead>"

    # 打印每个prompt的请求数据详情
    html += "<tbody>"
    for row in query_answer_data_list:
        session_id = row.get('session_id')
        slot_list = row.get('slot_list')
        conversation = row.get('conversation')
        expected_response = row.get('expected_response')
        slot_result = row.get('slot_result')
        similarity_score = row.get('similarity_score')
        http_response = row.get('http_response')

        # html表格的一行开始
        if row.get('similarity_score') >= compare_words_value_threshold:
            html += "<tr>"
        else:
            html += "<tr style='color:red;font-weight:bold'>"

        # 展示sessionId
        html += f"<td>session_id：<br>{session_id}</td>"
        # 展示候选实体列表
        html += f"<td>候选实体列表：<br>"
        for i in range(len(slot_list)):
            html += f"{slot_list[i]}<br>"
        # 展示对话记录
        html += f"<td>对话记录：<br>"
        for i in range(len(conversation)):
            html += f"{conversation[i]}<br>"
        # 展示期望的实体详情
        html += f"<td>期望的实体详情：<br>{expected_response}</td>"
        # 展示实际的实体详情
        html += f"<td>实际的实体详情：<br>{slot_result}</td>"
        # 展示相似度
        html += f"<td>{similarity_score}</td>"
        # 展示响应体 ensure_ascii=False为False，则正常输出中文，否则输出unicode编码
        html += f"<td>{json.dumps(http_response, ensure_ascii=False)}</td>"

        # html表格的一行结束
        html += "</tr>"

    html += "</tbody></table>"
    # 添加一个按钮，点击后可以显示/隐藏所有抽取错误的行，增加对应的js代码
    html += """<script>  
        let isFiltered = false;  

        function toggleTableRows() {  
            const rows = document.querySelectorAll("#detail tbody tr");  
            rows.forEach(row => {  
                if (isFiltered) {  
                    // 如果当前是在“隐藏”模式，显示所有行  
                    row.style.display = "";  
                } else {  
                    // 如果当前是在“显示”模式，只显示带样式的行  
                    if (row.getAttribute("style") === "color:red;font-weight:bold") {  
                        row.style.display = "";  
                    } else {  
                        row.style.display = "none";  
                    }  
                }  
            });  
            isFiltered = !isFiltered; // 切换状态  
        }  

        document.getElementById("toggleButton").addEventListener("click", toggleTableRows);  
    </script>"""
    html += "</body></html>"
    return html


def main():
    """
    主函数
    :return:
    """
    # excel文件列表
    file_name = "online_callback_data_20241125.xlsx"
    # sheet列表
    sheet_name = [
        "slot_filling_test_set"
    ]
    # 原始表格的slot_filling数据
    origin_table = parse_excel(file_name, sheet_name)
    # 原始的prompt，response
    slot_list_array = []
    conversation_list_array = []
    for sheet in sheet_name:
        print(f"开启测试excel文件：{file_name}的sheet：{sheet_name}")
        prompt_list, expected_slot_list, expected_response_list = origin_table[sheet]['prompt'], \
                                                                  origin_table[sheet]['expected_response'], \
                                                                  origin_table[sheet]['expected_response']

        for index in range(len(prompt_list)):
            slot_list, conversation_list = due_prompt(prompt_list[index])
            slot_list_array.append(slot_list)
            conversation_list_array.append(conversation_list)

        # 请求控制函数
        query_answer_data_list = query_control(
            slot_list_array,
            conversation_list_array,
            expected_response_list
        )

        # 生成HTML表格
        html_content = generate_html_table(url, query_answer_data_list)

        # 将HTML代码写入文件
        try:
            with open(f"slot_filling-report.html", "w", encoding='utf-8') as f:
                print("正在保存HTML文件...")
                f.write(html_content)
                print("slot_filling-report.html文件保存成功.")
        except Exception as e:
            print(e)
            print("slot_filling-report.html文件保存失败.")


if __name__ == '__main__':
    main()
