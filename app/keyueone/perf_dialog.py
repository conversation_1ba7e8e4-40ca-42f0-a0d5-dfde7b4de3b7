import time

import requests
import uuid
from datetime import datetime, timedelta
import threading


def query(thread_name, count, fix_time, session_id, query_text):
    """
    请求core接口，产生会话记录
    :param thread_name: 线程名称
    :param count: 请求次数，当前是第几次请求，计数
    :param fix_time: 会话时间
    :param session_id: 会话id
    :param query_text: 请求文本
    :return:
    """
    # url = "https://keyue.cloud.baidu.com/online/core/v5/stream/query" #online
    # url = "https://ky-test.cloud.baidu.com/online/core/v5/stream/query"  # test_env
    url = "https://keyue-test.cloud.baidu.com/online/core/v5/stream/query"  # test_env


    headers = {
        "token": "d26ad902-f8fe-472a-a3fd-0774dd2ad761",  # API key
        "Content-Type": "application/json"
    }
    # params = {
    #     "permCode": "whitelist",
    #     "debug": "true",
    #     "agentId": "5acd0e50-5e65-4019-a599-100fd4f328f4",
    #     "referer": "online/bot",
    #     "t": "1708587755915_310793",
    #     "fixTime": fix_time
    # }
    data = {
        "queryText": query_text,
        "sessionId": session_id
    }
    response = requests.post(url=url, headers=headers, json=data)
    print("Thread name:【{}】-- count = {} -- fixTime:【{}】 -- sessionId:【{}】 -- 响应code:【{}】".format(
        thread_name,
        count,
        fix_time,
        session_id,
        response.status_code)
    )


def get_date_and_session_id(start_year, start_month, start_day, number):
    """
    产生指定年月的时间，格式：2024-02-02 11:00:02
    :param start_year: 开始年份  如：2024
    :param start_month: 开始月份  如：1
    :param start_day: 开始日期  如：1
    :param number: 记录条数
    :return:
    """
    start_datetime = datetime(start_year, start_month, start_day, 0, 0, 0)  # 开始时间，2024年2月1日
    output_format = "%Y-%m-%d %H:%M:%S"  # 输出格式

    formatted_datetimes = []
    session_ids = []
    # 生成不重复的时间并输出
    for i in range(number):
        new_datetime = start_datetime + timedelta(seconds=i)
        formatted_datetime = new_datetime.strftime(output_format)
        session_id = str(uuid.uuid4())
        formatted_datetimes.append(formatted_datetime)
        session_ids.append(session_id)
    return formatted_datetimes, session_ids


def task(thread_name, start_year, start_month, start_day, total_number):
    # 构造 请求时间表、会话id表
    formatted_datetimes, session_ids = get_date_and_session_id(
        start_year=start_year,
        start_month=start_month,
        start_day=start_day,
        number=total_number)

    # 计数器，用于记录本次已经发送的请求数量
    count = 1

    # 发送请求
    for formatted_datetime, session_id in zip(formatted_datetimes, session_ids):
        query(thread_name, count, formatted_datetime, session_id, "打游戏")
        # query(thread_name, count, formatted_datetime, session_id, "天气查询")
        query(thread_name, count, formatted_datetime, session_id, "臭不要脸")
        # query(thread_name, count, formatted_datetime, session_id, "旅游规划")
        query(thread_name, count, formatted_datetime, session_id, "转人工意图")
        # time.sleep(1)
        count += 1

    # for formatted_datetime, session_id in zip(formatted_datetimes, session_ids):
    #     query(thread_name, count, formatted_datetime, session_id, "打游戏")
    #     query(thread_name, count, formatted_datetime, session_id, "天气查询")
    #     query(thread_name, count, formatted_datetime, session_id, "臭不要脸")
    #     query(thread_name, count, formatted_datetime, session_id, "旅游规划")
    #     query(thread_name, count, formatted_datetime, session_id, "转人工意图")
    #     time.sleep(1)
    #     count += 1


def main():
    # 本次起始年月日与构造的数据数量
    start_year = 2023
    start_month = [2, 3, 4, 5, 6, 7, 8, 9, 10, 12]
    # start_day = [3, 8, 9, 10, 11, 15, 16, 22, 26, 27, 28]
    start_day = [1, 2, 3, 4, 5]
    total_number = 1000

    threads = []

    # 2025年1月
    for day in start_day:
        thread = threading.Thread(target=task, args=(f"thread-2025-1-{day}", 2025, 1, day, total_number))
        thread.start()
        threads.append(thread)
        print(f"thread-2024-1-{day} start...")
        # time.sleep(1)

    # # 2025年2月
    # for day in start_day:
    #     thread = threading.Thread(target=task, args=(f"thread-2025-2-{day}", 2025, 2, day, total_number))
    #     thread.start()
    #     threads.append(thread)
    #     print(f"thread-2024-2-{day} start...")
    #     time.sleep(1)

    # # 2025年2月
    # for day in start_day:
    #     thread = threading.Thread(target=task, args=(f"thread-2024-2-{day}", 2024, 2, day, total_number))
    #     thread.start()
    #     threads.append(thread)
    #     print(f"thread-2024-2-{day} start...")
    #     time.sleep(1)

    # 等待所有线程执行完毕
    for thread in threads:
        thread.join()

    print("All threads finished")


if __name__ == '__main__':
    main()