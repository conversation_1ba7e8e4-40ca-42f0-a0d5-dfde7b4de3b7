# -*- coding: utf-8 -*-
"""
@author:dangchujia
"""
from ast import arg
from encodings import utf_8
import numpy as np
from warnings import catch_warnings
import requests
import json
import time
import openpyxl
import re
from datetime import datetime
import argparse

# 创建ArgumentParser对象
parser = argparse.ArgumentParser(description='Process some integers.')

# 添加需要解析的参数
parser.add_argument('userName', help='first argument')
parser.add_argument('agentId', help='second argument')
parser.add_argument('token', help='third argument')
parser.add_argument('host', help='forth argument')
parser.add_argument('typeHost', help='five argument')
parser.add_argument('file_name', help='six argument')
parser.add_argument('sheet_name', help='seven argument')

# 解析命令行参数
args = parser.parse_args()

# python3 unit7CheckIntent.py
# 效果测试车辆小管家
# bf316343-a6df-4140-bd71-09bc07e739d4
# 2630a755-ac92-4191-847e-23468bff400b
# http://***********:8846
# offline
# 行业多轮数据沉淀.xlsx
# 车辆小管家

# 机场
# python3 unit8CheckIntent.py keyueTestQA 2a2e19e6-937b-4e4a-ae10-46b05cc9b3ab ba1bd9ba-6ed3-4790-8f9e-a0607a9e87ad http://keyue-test.cloud.baidu.com offline tableqa_query.xlsx 机场

# 输出解析的参数值
# print("userName:", args.userName)
# print("agentId:", args.agentId)
# print("token:", args.token)
# print("host:", args.host)

# 两个租户下各自的【汽车agent】信息
# base_info = {
#     # 测试环境租户：pnstest5联调测试8.0，agent：自动化回归测试agent   【大模型eb4.0】
#     "pnstest5联调测试8.0": {
#         "agentId": "46709614-066e-47b7-88e5-d89613332af0",
#         "token": "ef806cca-fdc4-4c8b-b4bc-5b0b64d7c302"
#     }
#     # 线上租户：xyd租户40，agent：自动化回归测试agent   【大模型eb4.0】
#     # "xyd租户40": {
#     #     "agentId": "c8caaa09-c38f-49b0-81f0-6c355b2ef141",
#     #     "token": "2483debc-4f82-4ae1-b28d-e70115bef554"
#     # }
# }
base_info = {
    # 测试环境租户：pnstest5联调测试8.0，agent：自动化回归测试agent   【大模型eb4.0】
    args.userName: {
        "agentId": args.agentId,
        "token": args.token
    }
    # 线上租户：xyd租户40，agent：自动化回归测试agent   【大模型eb4.0】
    # "xyd租户40": {
    #     "agentId": "c8caaa09-c38f-49b0-81f0-6c355b2ef141",
    #     "token": "2483debc-4f82-4ae1-b28d-e70115bef554"
    # }
}

#offline_url = "https://keyue.cloud.baidu.com/core/v5/block/query" # 非流式
offline_url = "{}/core/v5/stream/query".format(args.host)  # 流式
#online_url = "https://keyue.cloud.baidu.com/online/core/v5/block/query" # 非流式
online_url = "{}/online/core/v5/stream/query".format(args.host)   # 流式

if args.typeHost == 'offline':
    env_array = [offline_url]
elif args.typeHost == 'online':
    env_array = [online_url]
else:    
    env_array = [offline_url, online_url]

#offline_url = "https://keyue.cloud.baidu.com/core/v5/block/query" # 非流式
# env_offline_url = "https://keyue-test.cloud.baidu.com/core/v5/stream/query"  # 流式
# #online_url = "https://keyue.cloud.baidu.com/online/core/v5/block/query" # 非流式
# env_online_url = "https://keyue-test.cloud.baidu.com/online/core/v5/stream/query"  # 流式

# qps：每秒发送多少个query，设置为1，表示每秒发送一个query，设置为2表示每秒发送2个query
qps = 1
# 控制qps
sleep_time = 1 / qps


def main():
    # 选择要测试的租户
    user_name = args.userName # eb4.0
   
    #print("====== 开始测试租户【{}】下的【多轮+FAQ】corequery ======".format(user_name))
    print("Start")
    # 读取测试表格数据
    file_name = "./{}".format(args.file_name)

    print("运行的case文件{}".format(file_name))

    # 表格中sheet名
    sheet_names = [
        args.sheet_name
    ]

    # 访问 文心的 token
    access_token = getAccessToken()

    # 读取并解析存储query的表格数据
    tables = parse_excel(file_name, sheet_names)
    #print("读取表格数据：【{}】成功。".format(file_name))
    for host_url in env_array:
        for sheet_name in sheet_names:
            #print("开始测试表格：【{}】sheet：【{}】的每条query是否有正常回复".format(file_name, sheet_name).\
            # encode('utf-8', errors='ignore'))
            result_Res = query_control(host_url, tables[sheet_name], access_token, user_name=user_name)
            html_content = generate_html_table(result_Res)
            #print("结束测试表格：【{}】sheet：【{}】的每条query是否有正常回复".format(file_name, sheet_name).\
            # encode('utf-8', errors='ignore'))
            # 将HTML代码写入文件
            try:
                if "online" in host_url:
                    with open( "online-report.html", "w", encoding='utf-8') as f:
                        # f.write(str((html_content.encode('utf_8', errors='ignore')).decode('utf-8', errors='ignore')))
                        f.write(html_content)
                else:
                    with open( "offline-report.html", "w", encoding='utf-8') as f:
                        f.write(html_content)
            except Exception as e:
                print(e)
            # sheet间间隔10秒
            time.sleep(1)

    #print(str("====== 结束测试租户【{}】下【多轮+FAQ】corequery ======".format(user_name)).decode("utf-8"))
    print("End")

def my_request_post(host_url, params, json_data, headers):
    """
    自定义封装请求(requests)函数
    """
    i = 0
    retry_num = 10
    res = None
    while i < retry_num:
        try:
            res = requests.post(url=host_url, params=params, json=json_data, headers=headers, stream=True)
            # print(res.content.decode('utf-8'))
            break
        except requests.ConnectionError as e:
            print(e)
            i += 1
            time.sleep(1)
    return res  

def getAccessToken():
    """
    通过ak、sk获取文心token
    :return: access_token
    """
    url = "https://aip.baidubce.com/oauth/2.0/token?"\
        "grant_type=client_credentials&client_id=hnX8luym9a5HWgLzTqCYXfAg&client_secret"\
            "=gVwfBpGWIp37fkKxkiZcYj4IbGqhIuMg"

    response = requests.request("GET", url, headers="", data="")
    response = response.json()
    return response['access_token']

def getEmbeddingsBetweenTwoWords(access_token, first, second):
    """
    获取两个query之间的向量
    :param access_token: api token
    :param first: 第一个query
    :param second: 第二个query
    :return:
    """
    url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/embedding-v1?"\
        "access_token={}".format(access_token)

    payload = json.dumps({
    "input": [
        first,
        second
    ]
    })
    headers = {
    'Content-Type': 'application/json'
    }
    json_data = requests.request("POST", url, headers=headers, data=payload)
    json_data = json_data.json()

    # 提取向量 A 和 B
    A = np.array(json_data["data"][0]["embedding"])
    B = np.array(json_data["data"][1]["embedding"])

    # 计算向量 A 和 B 的点积
    dot_product = np.dot(A, B)

    # 计算向量 A 和 B 的模
    magnitude_A = np.linalg.norm(A)
    magnitude_B = np.linalg.norm(B)

    # 计算夹角的余弦值
    cos_theta = dot_product / (magnitude_A * magnitude_B)

    # 计算夹角（弧度）
    theta_radians = np.arccos(cos_theta)

    # 将夹角转换为角度
    theta_degrees = np.degrees(theta_radians)

    # 打印结果
    # print("向量 A 和 B 的cos_theta ：", cos_theta)
    return cos_theta

def offline_task_and_faq(host_url, session_array, query_array, answer_array, access_token, user_name):
    """
    offline 测试文档问答，每调用一次，表示一次session
    :param query_array: 一个session的query数组
    :param answer_array: 一个session的query对应的结果
    :param user_name: 租户名称
    :param access_token: 文心token
    :return:
    """
    # 获取agentId和token
    agentId = base_info[user_name]["agentId"]
    token = base_info[user_name]["token"]
    headers = {
        "Accept": "text/event-stream",
        'Agent': agentId,
        'Content-type': 'application/json;charset=UTF-8',
        'Token': token    
        }
    params = {
        "Token": token
    }
    # 随机生成一个sessionId
    # sessionId = "offlineIntent" + str(time.time())
    # print(sessionId)

    # 先发送一个空
    json_data = {
            "channel": "",
            "queryText": "",
            "agentId": agentId,
            "collect": True,
            "variables": {}
        }
    
    # 缓存返回的数据
    http_data1 = b''
    http_post1 = my_request_post(host_url, params, json_data, headers)
    for chunk1 in http_post1.iter_content(chunk_size=1024):
        if chunk1:
            http_data1 += chunk1
    http_response1 = http_data1.decode('utf-8')
    res_text1 = json.loads(re.findall(r'data:(.*)', http_response1)[0])
    sessionId = res_text1['sessionId']
    print(sessionId)

    time.sleep(1)
    # 对比每一轮结果
    result_array = []
    for i in range(len(query_array)):
        session = session_array[i]
        query = query_array[i]
        if query is None:
            break
        exceptAnswer = answer_array[i]
        # exceptReplySource = replySource_array[i]
        # exceptLastIntent = lastIntent_array[i]
        time.sleep(sleep_time)
        json_data = {
            "channel": "",
            "queryText": query,
            "collect": True,
            "sessionId": sessionId,
            "agentId": agentId,
            "variables": {}
        }
        # 重试次数
        retry = 0
        retry_num = 3
        http_post = None
        # 缓存返回的数据
        http_data = b''
        while retry < retry_num:
            try:
                http_post = my_request_post(host_url, params, json_data, headers)
                for chunk in http_post.iter_content(chunk_size=1024):
                    if chunk:
                        http_data += chunk
                break
            except requests.ConnectionError as e:
                print(e)
                retry += 1
                time.sleep(1)

        http_response = http_data.decode('utf-8')
        # 请求失败，重试次数已达上限
        if http_post is None or 200 != http_post.status_code:
            # print("offline场景-多轮-query失败，原始的response的json数据为：{}".format(http_post.content.decode('utf-8')))
            continue
        
        # res_text = json.loads(re.findall(r'data:(.*)', http_post.content.decode('utf-8'))[0])
        # print("实际返回的结果111111===================，{}".format(http_response))

        # res_text = json.loads(re.findall(r'data:(.*)', http_response)[0])

        res_text = json.loads(re.findall(r'data:(.*)', http_response)[0])
        merged_data = []
        all_stream_text_array = re.findall(r'data:(.*)', http_response)
        for all_stream_text_key in all_stream_text_array:
            merged_data.extend(json.loads(all_stream_text_key).get('answer', []))
        
        res_text['answer'] = merged_data
        # print("实际返回的结果2222222===================，{}".format(res_text))
        # print("========")
        # print("{}".format(res_text['answer']))
        if res_text['variables'] is None:
            continue

        try:
            realAnswer = []
            realReplySource = []
            if res_text['answer'] is not None:
                for answerRes in res_text['answer']:
                    # print("拿到的answer后的结果{}".format(answerRes))
                    if answerRes['reply'] is not None:
                        realAnswer.append(answerRes['reply']['text'])
                        # realReplySource.append(answerRes['reply']['replySource'])
                        # print("实际得到的内部答案，{}".format(realAnswer))
                        
                # if realAnswer is None:
                #     realAnswer = res_text['variables']['last_task_prompt']
                #     realReplySource = 'COLLECT_SLOT_ENTITY'

                # print("实际得到的答案，{}".format(realAnswer))
                # print("预期返回的结果，{}".format(realReplySource))
                #realAnswer = res_text["variables"]["last_task_prompt"]
                #res_answer = res_text["answer"][0]["reply"]["text"]
                # 判断返回的结果是否包含在预期答案中
                if realAnswer == '':
                    print("query: {}".format(query))
                    print("exceptAnswer: {}".format(exceptAnswer))
                    print("realAnswer: {}".format(realAnswer))
                    # print("场景-多轮-query失败，session为【{}】,query为【{}】, 预期结果为【{}】,实际response的last_task_prompt数据为：{}"
                    #     .format(session, query, answer, res_answer).encode('utf-8'))
                    session_res = "第【{}】个场景case,session:【{}】".format(session, sessionId)
                    query_res = "{}".format(query)
                    except_answer = "{}".format(exceptAnswer)
                    answer_res = "实际结果和预期结果相似度<0.8:{}".format(realAnswer)
                    temp_result_array = [session_res, query_res, except_answer, answer_res]
                    # 存储结果
                    result_array.append(temp_result_array)
                else:
                    print("query: {}".format(query))
                    print("exceptAnswer: {}".format(exceptAnswer))
                    print("realAnswer: {}".format(realAnswer))
                    realAnswer = ''.join(realAnswer)
                    compareWordsValue = getEmbeddingsBetweenTwoWords(access_token, exceptAnswer, realAnswer)
                    if exceptAnswer not in realAnswer and compareWordsValue < 0.8:
                        # print("场景-多轮-query失败，session为【{}】,query为【{}】, 预期结果为【{}】,\
                        #    实际response的last_task_prompt数据为：{}".format(session, query,
                        #  answer, res_answer).encode('utf-8'))
                        session_res = "第【{}】个场景case,session:【{}】".format(session, sessionId)
                        query_res = "{}".format(query)
                        except_answer = "{}".format(exceptAnswer)
                        answer_res = "实际结果和预期结果相似度<0.8:{}".format(realAnswer)
                        temp_result_array = [session_res, query_res, except_answer, answer_res]
                        # 存储结果
                        result_array.append(temp_result_array)
                    else:
                        # 成功，
                        # print("场景-多轮-query成功，session为【{}】,query为【{}】, 预期结果为【{}】,\
                        #    实际response的last_task_prompt数据为：{}".format(session, query,
                        #  answer, res_answer).encode('utf-8'))
                        session_res = "第【{}】个场景case,session:【{}】".format(session, sessionId)
                        query_res = "{}".format(query)
                        except_answer = "{}".format(exceptAnswer)
                        answer_res = "{}".format(realAnswer)
                        temp_result_array = [session_res, query_res, except_answer, answer_res]
                        # 存储结果
                        result_array.append(temp_result_array)    
        except Exception as e:
            print(e)
        time.sleep(1)  
    return result_array


def parse_excel(excel_name, sheet_names):
    """
    解析excel表格数据，存储为字典+数组
    :param excel_name: excel文件名
    :param sheet_names: sheet名
    :return: tables对象，key为sheet名，value为表格数据
    """
    workbook = openpyxl.load_workbook(excel_name)
    tables = {}
    # 通过工作表名称选择当前活动的工作表
    for sheet_name in sheet_names:
        sheet = workbook[sheet_name]
        table = {}
        for row_index in range(1, sheet.max_row + 1):
            # 拿到表头数据名
            if row_index == 1:
                for col_index in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=1, column=col_index)
                    table[cell.value] = []
                continue
            # 存储第个表头下的所有数据，以表头为key，数据为value数组
            for col_index in range(1, sheet.max_column + 1):
                cell = sheet.cell(row=row_index, column=col_index)
                table[sheet.cell(row=1, column=col_index).value].append(cell.value)

        tables[sheet_name] = table
    return tables


def query_control(host_url, table, access_token, user_name):
    """
    根据表格内容，控制query流程，统计每个session中的query的情况，再进行query测试
    :param table: excel名
    :param user_name: 租户名
    :param access_token: 访问文心的token
    :return: all_cost_times 所有query的应答时间
    """
    all_result_array = []
    row_length = len(table['session'])
    # print("xxxxxx{}xx".format(row_length))
    cur_row_index = 0
    while (cur_row_index < row_length):
        # 统计当前session序号中有多少个query，并把query存入数组中
        last_row_index = cur_row_index
        # 当前session序号
        cur_session_num = table['session'][cur_row_index]
        # 越界检查，下一行的session序号
        if cur_row_index + 1 >= row_length:
            # 如果最后一行的session只有1个query
            cur_session_query_num = 1
            # 用于外围循环判断，防止死循环
            cur_row_index += 1
        else:
            # 下一行的session序号
            cur_row_index += 1
            next_session_num = table['session'][cur_row_index]
            # 如果下一个session序号与当前session序号相同，则认为当前session中query个数为1
            while cur_session_num == next_session_num and cur_row_index < row_length:
                cur_row_index += 1
                # 越界检查，最后一个session中有多个query时
                if cur_row_index >= row_length:
                    break
                next_session_num = table['session'][cur_row_index]
            # 当前session中query个数
            cur_session_query_num = cur_row_index - last_row_index
        query_array = []
        answer_array = []
        session_array = []
        replySource_array = []
        lastIntent_array = []
        for i in range(0, cur_session_query_num):
            session_array.append(table['session'][last_row_index + i])
            query_array.append(table['query'][last_row_index + i])
            answer_array.append(table['answer'][last_row_index + i])
            # replySource_array.append(table['replySource'][last_row_index + i])
            # lastIntent_array.append(table['last_intent'][last_row_index + i])

        # print("当前session【{}】的query列表为：【{}】, answer列表为：【{}】\
        #    ".format(cur_session_num, query_array, answer_array))
        # 开始测试当前session中的query，并判断结果，标注异常值，最后把整体结果输出，
        result_array = offline_task_and_faq(host_url, session_array, query_array, 
                                 answer_array, access_token, user_name=user_name)
        # print("cost_times：{}，单位：ms".format(result_array))
        # print("每个结果=======：result_array{}".format(result_array))
        # 存储当前session中所有query的应答时间至全局数组中
        all_result_array.extend(result_array)

        # session间间隔5秒
        time.sleep(1)

    # print("所有结果：all_result_array{}".format(all_result_array))
    return all_result_array

def generate_html_table(data):
    html = "<html><body>"
    html += "<table width='100%' border='2' bordercolor='black' cellspacing='0' cellpadding='0'><tr>\
                <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
                <strong>客悦多轮自动化case回归场景报告，agent[自动化回归测试agent], 具体case请点击<a href='https://ku.baidu-int.com\
                    /knowledge/HFVrC7hq1Q/q1No3pYP4W/i4bg1ih3DR/_ftfVl1iFSugf5' target='_blank'>一键直达</a></strong></td></tr>\
            <tr>\
                <td width='auto' align='center' colspan='23' bgcolor='yellow'>\
                <strong>预期结果 以及 实际reply的text数据 <a href='https://cloud.baidu.com/doc/WENXINWORKSHOP/s/alj562vvu' target='_blank'>采用千帆接口向量计算</a>, \
                    A⋅B 是向量 A 和 B 的点积，|A| 和 |B| 分别是向量 A 和 B 的模。目前< 0.8判断为不合格 需要人工分析</strong></td></tr>\
            <tr bgcolor='yellow'>\
                <td width='auto' align='center'><strong>测试场景</strong></td>\
                <td width='auto' align='center'><strong>query</strong></td>\
                <td width='auto' align='center'><strong>预期结果</strong></td>\
                <td width='auto' align='center'><strong>实际结果</strong></td>\
                <td width='auto' align='center'><strong>打分结果</strong></td>\
            </tr>"

    current_key = None
    rowspan = 0

    all_line_nums = 0
    all_one_nums = 0
    for i, row in enumerate(data):
        all_line_nums = all_line_nums + 1
        if current_key != row[0]:
            if current_key is not None:
                html += "</tr>"
            current_key = row[0]
            rowspan = 0
            html += "<tr>"
            html += f"<td rowspan='{len([x[0] for x in data if x[0] == current_key])}'>{current_key}</td>"
            if "实际结果和预期结果相似度<0.8:" in str(row[3]):
                html += f"<td style='color:red'>{row[1]}</td>"
                html += f"<td style='color:red'>{row[2]}</td>"
                html += f"<td style='color:red'>{row[3]}</td>"
                html += f"<td style='color:red'>0</td>"
            else:
                all_one_nums = all_one_nums + 1
                html += f"<td>{row[1]}</td>"
                html += f"<td>{row[2]}</td>"
                html += f"<td>{row[3]}</td>"
                html += f"<td>1</td>"
        else:
            html += "<tr>"
            if "实际结果和预期结果相似度<0.8:" in str(row[3]):
                html += f"<td style='color:red'>{row[1]}</td>"
                html += f"<td style='color:red'>{row[2]}</td>"
                html += f"<td style='color:red'>{row[3]}</td>"
                html += f"<td style='color:red'>0</td>"
            else:
                all_one_nums = all_one_nums + 1
                html += f"<td>{row[1]}</td>"
                html += f"<td>{row[2]}</td>"
                html += f"<td>{row[3]}</td>"
                html += f"<td>1</td>"
        html += "</tr>"

    html += "<tr><td width='auto' align='center' colspan='23' bgcolor='yellow'>"
    html += f"<strong>总数量{all_line_nums},得分为1的数量{all_one_nums},\
        正确率{round(all_one_nums/all_line_nums*100, 2)}%</strong></td></tr>"
    html += "</table></body></html>"

    return html

if __name__ == '__main__':
    main()
