# -*- coding: utf-8 -*-
import json
import re

# data = [
#     {
#     "prompt": "你是一位意图识别专家，擅长根据历史对话识别用户当前Query所表达的的真实意图。我将给你一个意图列表，上面记录着意图的名称、解释和例句信息，请你根据这些信息识别用户意图，并按照如下格式输出结果：{“意图”: [], “是否需要澄清”: [], “澄清话术”: “”}\n\n请你遵循下述要求识别用户意图和输出结果：\n1、如果用户的表述准确、无歧义地命中了意图列表中的某个意图，【意图】字段填写对应意图名称，【是否需要澄清】字段填\"false\"\n2、如果用户的表述与意图列表中的某个意图相关，但表述比较模糊，【意图】字段填写对应意图名称，【是否需要澄清】字段填\"true\"\n3、如果用户的表述与意图列表中的意图都无关，但属于业务问题咨询范畴，【意图】字段填写\"问题咨询\"，【是否需要澄清】字段填\"false\"\n4、如果用户的表述与意图列表中的意图都无关，并且不属于业务问题咨询范畴，【意图】字段填写\"不相关\"，【是否需要澄清】字段填\"false\"\n5、若存在需要澄清的意图，【澄清话术】字段填写针对该意图的澄清表述\n6、所有意图名称必须从意图列表中选择，不得随意编造\n\n###意图列表###\n| 意图名称 | 意图解释 | 意图例句 |\n| :–: | :–: | :–: |\n| 汇款手续费咨询 | - | - |\n| 注销服务 | - | - |\n| 个人外汇买卖汇率查询 | - | - |\n| 约定定期功能签约 | - | - |\n| 贷后服务咨询 | - | - |\n| 疫情期间贷款延期还款 | - | - |\n| 第三方存管操作 | - | - |\n| 问题咨询 | 用户的表述与上述意图都无关，但属于业务问题咨询范畴 | - |\n| 不相关 | 用户的表述与上述意图都无关，并且不属于业务问题咨询范畴 | - |\n\n###历史对话###\n空\n\n###用户当前Query###\n用户：请帮我把我的浦发信用卡注销\n\n###输出结果###",
#     "response": [
#     [
#     "{“意图”: [“注销服务”], “是否需要澄清”: [“false”], “澄清话术”: “”}"
#     ]
#     ]
#     }
# ]
data = []
# 读取文件
file_name = "./{}".format("intent_data_010_hangye.test.jsonl")
with open(file_name, 'r') as readFile:
    for promptLine in readFile:
        data = json.loads(promptLine)

        # Extract the prompt from the data
        prompt = data[0]['prompt']
        query_answer_old = str(data[0]['response'][0])
        # print(query_answer_old)
        # query_answer_new = json.loads(query_answer_old)['意图']

        # print(prompt)
        # print(query_answer_old)

        if "###历史对话###\n空\n" in prompt:
            
            # 处理意图
            # Find the starting and ending indices of the intent list
            start_index = prompt.find("| 意图名称 | 意图解释 | 意图例句 |") + len("| :--: | :--: | :--: |")
            end_index = prompt.find("###历史对话###")
            # Extract the intent list from the prompt
            intent_list = prompt[start_index:end_index]
            # Split the intent list into lines
            lines = intent_list.split('\n')
            # Initialize an empty list to hold the intent names
            intent_names = ''
            intent_description = ''
            intent_examples = ''
            intent_list_all = []

            intent_list_dit = {}
            intent_list_arr_data = []
            intent_list_arr = []

            # Iterate over the lines
            for line in lines:
                # Use a regular expression to find the intent name
                match1 = re.search(r'\| (.+?) \|', line)
                match2 = re.search(r'\| (.+?) \| (.+?) \|', line)
                match3 = re.search(r'\| (.+?) \| (.+?) \| (.+?) \|', line)
                
                # If a match was found
                if match1:
                    # Add the intent name to the list
                    intent_names = match1.group(1)
                    # Add the intent name to the list
                    intent_description =  match2.group(2)
                    # Add the intent name to the list
                    intent_examples = match3.group(3)
                    # print("========={}".format(intent_names))
                    # print("========={}".format(intent_description))
                    # print("========={}".format(intent_examples))

                    if intent_names not in (':--:', '不相关', '问题咨询') :
                        intent_list_arr_data1 = {}
                        intent_list_arr_data1['intentName']= intent_names
                        intent_list_arr_data1['intentDescription'] = intent_description
                        intent_list_arr_data1['intentExample'] = intent_examples
                        intent_list_arr_data.append(intent_list_arr_data1)

            intent_list_dit['intent'] = intent_list_arr_data
        
            # 解析query
            start_index_query = prompt.find("###用户当前Query###") + len("###用户当前Query###")
            end_index_query = prompt.find("###输出结果###")
            # Extract the intent list from the prompt
            query_list = prompt[start_index_query:end_index_query]
            # Split the intent list into lines
            query = query_list.split('用户：')  

            intent_list_dit['query'] = query[1]
            first = query_answer_old.split("\"意图\": [\"")
            second = first[1].split("\"], \"是否需要澄清")       
            intent_list_dit['answer'] = second[0]   
            intent_list_arr.append(intent_list_dit)
            # Print the list of intent names
            print(intent_list_arr)