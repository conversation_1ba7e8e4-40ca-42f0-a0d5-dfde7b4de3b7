#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/09/21 17:00:00
"""
import logging

from app.eva_debug import eva_debug

logger = logging.getLogger()


@eva_debug.route('/')
def debug():
    """
    验证联通性
    :return:
    """
    return {'code': 200, 'esg': "开始使用吧"}

