#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
"""
import re
import requests
import json
import subprocess
import copy


request_large_model_error = {
    'gpt_err': u'请求第三方大模型-gpt3.5出错，检查通过api_key是否可正常请求或者该api_key的qps超出限制',
    'other_err': u'请求第三方大模型-other出错，请检查curl命令是否正确或者解析路径是否正确',
    'deepseek_err': u'请求第三方大模型-deepseek出错',
    'claude_err': u'请求第三方大模型-claude出错',
    'gpt-4o_err': u'请求第三方大模型-gpt-4o_出错',
    'other_curl_err': u'请求第三方大模型-other出错，请检查curl命令是否正确',
    'other_route_err': u'请求第三方大模型-other出错，请检查解析路径是否正确'
}


class RequestLargeModelObj:
    """
    请求大模型的对象
    初始化完成后，只需传入prompt_dict就可请求大模型并解析出结果
    """

    def __init__(self, large_model_config):

        self.large_model_type = large_model_config.get('large_model_type')
        self.api_key = large_model_config.get('api_key')
        # self.prompt_template = r"{}".format(large_model_config['prompt_template'])
        self.prompt_template = large_model_config.get('prompt_template')
        # print self.prompt_template
        # self.curl_command_template = r"{}".format(large_model_config['curl_command_template'])
        # self.curl_command_template = large_model_config['curl_command_template'].replace('\\', ' ').replace('\n', ' ')
        self.curl_command_template = large_model_config.get('curl_command_template')
        self.pattern = ur"{}".format(large_model_config.get('pattern').decode('utf-8'))
        self.route_list = large_model_config.get('route_list')

    def test_available(self):
        """
        检查是否大模型可用
        :return:
        """
        try:
            # 测试大模型是否可用
            prompt_dict = {
                "query": "6个免费产业都有啥",
                "reference_answer": "适应产业: 新一代电子信息\n模具和机械制造\n饲料",
                "model_response": "\"新一代电子信息\"，模具和机械炜制造，饲料。\n以上答案由一言生成,来源：《项目建设六项免费服务.docx》、《企业技术改造支持政策.docx》"
            }
            prompt = self.create_prompt(prompt_dict)
            res = self.request_large_model(prompt)
            if res in request_large_model_error.values():
                return False, res
            else:
                print "请求第三方大模型成功"
                print res
                # 在进行一次判断，判断正则表达式是否能进行匹配
                key_info = self.get_key_by_answer_text(res, 2)
                if key_info == [u'正则表达式解析失败', u'正则表达式解析失败']:
                    return False, u'请求大模型成功，解析正则表达式时失败'
                return True, 'succ'
        except Exception as ex:
            print ex
            return False, ex.message

    def create_prompt(self, prompt_dict):
        """
        拼装prompt
        :param prompt_dict:
        :return:
        """
        print "------------这里是在拼接prompt----------------------"
        for k, v in prompt_dict.items():
            print k, v
        prompt = copy.deepcopy(self.prompt_template)
        # print "------------这里是深拷贝：------------" + prompt
        for key, value in prompt_dict.items():
            prompt = prompt.replace("{{{}}}".format(key),
                                    value.replace('\n', '\\n').replace('\"', '\\\"').replace('\'', '\\\''))
        # print prompt
        return prompt

    def get_key_by_answer_text(self, answer_text, key_num=2):
        """
        从答案文本answer_text中，根据正则表达式提取得分和原因
        :param answer_text: 大模型返回的文本
        :param key_num: 需要提取的关键词数量
        :return: [得分, 原因]
        """
        try:
            # 使用正则表达式匹配【得分】和【原因】后面的内容
            # pattern = r"【得分】(.*?)\n【原因】(.*)"
            match = re.search(self.pattern, answer_text)


            if match:
                score = match.group(1).strip()
                reason = match.group(2).strip()
                return [score, reason]
            else:
                return [u'正则表达式解析失败', u'正则表达式解析失败']
        except Exception as ex:
            print(ex)
            return [u'正则表达式解析失败', u'正则表达式解析失败']

    def request_large_model(self, prompt):
        """
        根据prompt，请求大模型，获得大模型的回答
        gpt3.5或其他大模型均可
        :param prompt:
        :return:
        """
        print 'api_key:'
        print self.api_key
        if self.large_model_type == 'gpt3.5':
            if self.api_key.startswith('FC'):
                print '调用非标准gpt3.5'
                url = "https://api.aigcfun.com/api/v1/text?key=" + self.api_key
                payload = json.dumps({
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "model": "gpt-3.5-turbo"
                })
                headers = {
                    'Accept': 'application/json, text/plain, */*',
                    'Content-Type': 'application/json'
                }
            else:
                print '调用标准gpt3.5'
                # 请求gpt3.5
                url = "https://api.openai.com/v1/chat/completions"
                payload = json.dumps({
                    "model": "gpt-3.5-turbo",
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                })
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + self.api_key
                }
            try:
                response = requests.request("POST", url, headers=headers, data=payload)
                print '请求gpt3.5结果为：'
                print response.text
                if response.status_code == 200:
                    res_text = response.json()["choices"][0]["message"]["content"]
                    return res_text
                else:
                    print '请求gpt3.5状态码不对：'
                    return request_large_model_error['gpt_err']
            except Exception as ex:
                print '请求gpt3.5异常：'
                print ex
                msg = request_large_model_error['gpt_err']
                return msg
        elif self.large_model_type == 'claude-3-7-sonnet-20250219':
            print '调用claude-3-7-sonnet-20250219'
            # 请求gpt3.5
            url = "http://llms-se.baidu-int.com:8200/chat/completions"
            payload = json.dumps({
                "model": "claude-3-7-sonnet-20250219",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            })
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + self.api_key
            }
            try:
                response = requests.request("POST", url, headers=headers, data=payload)
                print '请求claude-3-7-sonnet-20250219结果为：'
                print response.text
                if response.status_code == 200:
                    res_text = response.json()["choices"][0]["message"]["content"]
                    return res_text
                else:
                    return request_large_model_error['claude_err']
            except Exception as ex:
                print '请求claude-3-7-sonnet-20250219异常：'
                print ex
                msg = request_large_model_error['claude_err']
                return msg
        elif self.large_model_type == 'deepseek-chat-0324':
            print '调用deepseek-chat-0324'
            # 请求gpt3.5
            url = "http://llms-se.baidu-int.com:8200/chat/completions"
            payload = json.dumps({
                "model": "deepseek-chat-0324",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            })
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + self.api_key
            }
            try:
                response = requests.request("POST", url, headers=headers, data=payload)
                print '请求deepseek-chat-0324结果为：'
                print response.text
                if response.status_code == 200:
                    res_text = response.json()["choices"][0]["message"]["content"]
                    return res_text
                else:
                    return request_large_model_error['deepseek_err']
            except Exception as ex:
                print '请求deepseek-chat-0324异常：'
                print ex
                msg = request_large_model_error['deepseek_err']
                return msg
        elif self.large_model_type == 'gpt-4o':
            print '调用gpt-4o'
            # 请求gpt3.5
            url = "http://llms-se.baidu-int.com:8200/chat/completions"
            payload = json.dumps({
                "model": "gpt-4o",
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            })
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + self.api_key
            }
            try:
                response = requests.request("POST", url, headers=headers, data=payload)
                print '请求gpt-4o结果为：'
                print response.text
                if response.status_code == 200:
                    res_text = response.json()["choices"][0]["message"]["content"]
                    return res_text
                else:
                    return request_large_model_error['gpt-4o_err']
            except Exception as ex:
                print '请求gpt-4o异常：'
                print ex
                msg = request_large_model_error['gpt-4o_err']
                return msg
        elif self.large_model_type == 'other':
            # other，请求其他大模型
            # 1.处理curl命令，将$prompt进行替换
            try:
                curl_command = self.process_curl_command(prompt)
                res_text = self.request_large_request_by_curl(curl_command)
                print '请求第三方大模型成功，返回内容为'
                print res_text
                return res_text
            except Exception as ex:
                print ex
                msg = request_large_model_error['other_err']
                return msg

    def process_curl_command(self, prompt):
        """
        处理curl命令，将${prompt}进行替换
        :param prompt:
        :return:
        """
        # prompt = prompt.replace('\"', '\\\"')
        # prompt = repr(prompt)
        print '替换后的prompt：'
        print prompt
        curl_command_template_copy = copy.deepcopy(self.curl_command_template)
        curl_command_template_copy = curl_command_template_copy.replace('--data-raw', '--data')
        curl_command = curl_command_template_copy.replace("$prompt", '"{}"'.format(prompt))
        return curl_command

    def request_large_request_by_curl(self, curl_command):
        """
        通过curl命令请求大模型，并根据路径进行解析
        :param curl_command: 可以直接请求的curl命令，已经将prompt拼入了
        :return:
        """
        curl_command = r"{}".format(curl_command)
        print curl_command
        process = subprocess.Popen(curl_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        # 转换stdout和stderr的字节码为字符串
        stdout = stdout.decode("utf-8")
        stderr = stderr.decode("utf-8")

        # 打印输出和错误信息
        # print("Output: ", stdout)
        # print("Error: ", stderr)

        # 返回结果是JSON格式，进行解析
        if stdout:
            result = json.loads(stdout)
            print("Parsed result: ", result)
            res_text = parse_json(result, self.route_list)
        else:
            res_text = request_large_model_error['other_curl_err']
        return res_text

    def request_large_model_and_parse_result(self, prompt_dict):
        """
        传入prompt_dict，请求第三方大模型，并返回格式化的结果[得分, 答案解析, 大模型的完整回答]
        :param prompt_dict:
        :return: [得分, 答案解析, 大模型的完整回答]
        """
        # # 1.检查大模型是否可用
        # is_available, msg = self.test_available()
        # if not is_available:
        #     return [u'得分未知', u'答案解析未知', msg]
        # 2.拼装prompt
        prompt = self.create_prompt(prompt_dict)
        print '拼装后的prompt为：' + prompt
        # 3.请求第三方大模型
        res_text = self.request_large_model(prompt)
        if res_text in request_large_model_error.values():
            return [u'得分未知', u'答案解析未知', res_text]
        # 4.解析大模型的结果
        res_list = self.get_key_by_answer_text(res_text)
        res_list.append(res_text)
        print '这里已经解析完大模型解结果'
        return res_list


def parse_json(data, param_list):
    """
    解析json，返回解析后的结果
    :param data:
    :param param_list:
    :return:
    """
    for param in param_list:
        if param.isdigit():
            # 是数字，先转换为int，在进行取
            data = data[int(param)]
        else:
            data = data.get(param)
    if data is None or data == '':
        return request_large_model_error['other_route_err']
    else:
        return data


if __name__ == '__main__':
    # #     # 设置curl命令参数
    #     curl_command_template = """curl --location --request POST 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=24.6318aa27abf18d308f97c06ab6c2f761.2592000.1697461491.282335-37974619' \
    # --header 'Content-Type: application/json' \
    # --data-raw '{
    #     "messages": [
    #         {"role":"user","content":$prompt}
    #     ]
    # }'
    #     """
        prompt_template = """你是专业的评测人员，请根据问题和参考答案对模型输出进行准确性进行打分，分档0、1、2，具体的打分标准请参照分档描述和注意事项，此外你还需要提供打分和对应的评分依据，你的回答请按照
    【得分】xxx
    【原因】xxx
    的格式输出，注意得分和原因之间要进行换行
    下面给出分档描述、问题、参考答案、模型输出以及注意事项
    【分档描述】
    0: 模型输出与参考答案语义和相关数据信息完全不符合
    1:模型输出与参考答案语义和相关数据信息部分符合，但是允许存在不完整、冗余或者部分错误的情况
    2:模型输出与参考答案语义和相关数据洗洗脑完全符合
    【注意事项】
    1.如果模型输出有关于来源的描述，例如如果模型中有"答案由一言生成"、"来源xxx"等内容，请将内容进行忽略
    【问题】{query}
    【参考答案】{reference_answer}
    【模型输出】{model_response}
     """
        large_model_config = {
            'large_model_type': 'gpt3.5',
            'api_key': 'FC29YE0JYPSJUHTKE6',
            'prompt_template': prompt_template,
            # 'curl_command_template': curl_command_template,
            'pattern': "【得分】([012])\n【原因】(.*)",
            'route_list': ['result']
        }
    #
    #
    #
        req_large_model_obj = RequestLargeModelObj(large_model_config)


        prompt_dict = {
            "query": "6个免费产业都有啥",
            "reference_answer": "适应产业: 新一代电子信息,模具和机械制造,饲料",
            "model_response": "新一代电子信息，模具和机械炜制造，饲料。以上答案由一言生成，来源：《项目建设六项免费服务.docx》、《企业技术改造支持政策.docx》"
        }

        res_list = req_large_model_obj.request_large_model_and_parse_result(prompt_dict)
    #     prompt = req_large_model_obj.process_curl_command('你好吗')
        print '这里是res_list'
        print res_list
        for res in res_list:
            print '这里是res_list单条数据'
            print res_list.index(res), res
    # #
    #     print prompt
    #     print req_large_model_obj.curl_command_template
    #  #   print curl_command_template
    #     print large_model_config['pattern']
    # #
    #
    #     curl_test = r"""curl --location --request POST 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=24.6318aa27abf18d308f97c06ab6c2f761.2592000.1697461491.282335-37974619' \
    # --header 'Content-Type: application/json' \
    # --data-raw '{
    #     "messages": [
    #         {"role":"user","content":$prompt}
    #     ]
    # }'
    #     """
    #     unicode_text = unicode(curl_test, "utf-8")
    #     print type(unicode_text)
    #     print unicode_text.replace('\\', ' ').replace('\n', ' ')

    #     prompt = u"""你是专业的评测人员，请根据问题和参考答案对模型输出进行准确性进行打分，分档0、1、2，具体的打分标准请参照分档描述和注意事项，此外你还需要提供打分和对应的评分依据，你的回答请按照
    # 【得分】xxx
    # 【原因】xxx
    # 的格式输出，注意得分和原因之间要进行换行
    # 下面给出分档描述、问题、参考答案、模型输出以及注意事项
    # 【分档描述】
    # 0: 模型输出与参考答案语义和相关数据信息完全不符合
    # 1:模型输出与参考答案语义和相关数据信息部分符合，但是允许存在不完整、冗余或者部分错误的情况
    # 2:模型输出与参考答案语义和相关数据洗洗脑完全符合
    # 【注意事项】
    # 1.如果模型输出有关于来源的描述，例如如果模型中有"答案由一言生成"、"来源xxx"等内容，请将内容进行忽略
    # 【问题】6个免费产业都有啥
    # 【参考答案】适应产业: 新一代电子信息,模具和机械制造,饲料
    # 【模型输出】新一代电子信息，模具和机械炜制造，饲料。以上答案由一言生成，来源：《项目建设六项免费服务.docx》、《企业技术改造支持政策.docx》"""
    #     print '替换前的prompt为：'
    #     print prompt
    #     prompt = prompt.replace('\n', '\\n').replace('\"', '\\\"').replace('\'', '\\\'')
    #     print '替换后的prompt为：'
    #     print prompt

    # large_model_config = {
    #     'curl_command_template': u"""curl --location --request POST 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=24.6318aa27abf18d308f97c06ab6c2f761.2592000.1697461491.282335-37974619' --header 'Content-Type: application/json' --data-raw '{"messages": [{"role":"user","content":$prompt}]}'"""
    # }
    # req_large_obj = RequestLargeModelObj(large_model_config)
    # new_prompt = req_large_obj.process_curl_command(prompt)
    # print new_prompt

    # list1 = [1, 2, 3, 4, 5]
    # print list1.get(0)
