#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2020/09/21 17:00:00
"""
import json
import logging

import requests
from flask import jsonify, request, g, Blueprint

from ..models.models import Env

debugs = Blueprint('debug', __name__)
logger = logging.getLogger()


# @debugs.before_request
# def before_request_callback():
#     """
#     钩子方法
#     Returns:
#
#     """
#     g.corequery_address = request.headers.get('corequery_address')
#     if g.corequery_address == "":
#         return jsonify(status=500, msg="没有配置corequery服务地址，请到<环境信息配置>页面添加。", data={})


@debugs.route('/')
def debug():
    """
    验证联通性
    :return:
    """
    return {'code': 200, 'esg': "开始使用吧"}


@debugs.route('/evaluate/corequery', methods=['POST'])
def corequery():
    """

    :return:
    """
    data = request.form.to_dict()
    project_env = str(data['env'])
    queryText = str(data['queryText'])
    sessionId = str(data['sessionId'])

    envInfo = Env.query.filter(Env.env_name == u"%s" % project_env).first()
    corequery_address = envInfo.core_address
    botToken = envInfo.botToken

    print(botToken)
    print(corequery_address)

    if "NGD" not in botToken:
        botToken = "NGD " + botToken

    json_request = {
        "queryText": queryText,
        "sessionId": sessionId,
        "collect": "false",
        "botId": "4b10f28a-e5f5-4172-9e1e-e782d75e565b",
        "Authorization": botToken,
        "channel": ""
    }

    print("========json_request")
    print(type(json.dumps(json_request)))

    url = "http://%s/core/v3/query?debug=true&explain=true" % corequery_address
    headers = {'Authorization': botToken, 'Content-Type': 'application/json;charset=UTF-8'}
    logger.info("url: %s" % url)
    response_dict = {}

    try:
        response = requests.post(url, json=json_request, headers=headers).json()
        print response
        response_dict["original"] = response

        response_dict["answer"] = response["data"]["answer"]["answerText"]

        response_dict["queryId"] = response["data"]["queryId"]

        response_dict["sessionId"] = response["data"]["sessionId"]

        response_dict["source"] = response["data"]["source"]

        response_dict["setting"] = response["data"]["_explain"]["bot"]["setting"]["confidence"]  # 置信度详情
        list = []
        dict_table = response["data"]["_explain"]["engine"]["table_qa"]
        if "input" in dict_table:
            tableqa_input = response["data"]["_explain"]["engine"]["table_qa"]["input"]
        else:
            tableqa_input = "--"
        if "output" in dict_table:
            tableqa_output = response["data"]["_explain"]["engine"]["table_qa"]["output"]
        else:
            tableqa_output = "--"
        if "spent" in dict_table:
            tableqa_spent = response["data"]["_explain"]["engine"]["table_qa"]["spent"]
        else:
            tableqa_spent = "--"
        dict_task_based = response["data"]["_explain"]["engine"]["task_based"]
        if "input" in dict_task_based:
            task_based_input = response["data"]["_explain"]["engine"]["task_based"]["input"]
        else:
            task_based_input = "--"
        if "output" in dict_task_based:
            task_based_output = response["data"]["_explain"]["engine"]["task_based"]["output"]
        else:
            task_based_output = "--"
        if "spent" in dict_task_based:
            task_based_spent = response["data"]["_explain"]["engine"]["task_based"]["spent"]
        else:
            task_based_spent = "--"
        dict_faq = response["data"]["_explain"]["engine"]["faq"]
        if "input" in dict_faq:
            faq_input = response["data"]["_explain"]["engine"]["faq"]["input"]
        else:
            faq_input = "--"
        if "output" in dict_faq:
            faq_output = response["data"]["_explain"]["engine"]["faq"]["process"]["search"]["output"]
        else:
            faq_output = "--"
        dict_tqa = {}
        dict_tqa["engine"] = "table_qa"
        dict_tqa["input"] = tableqa_input
        dict_tqa["output"] = tableqa_output
        dict_tqa["spent"] = tableqa_spent
        list.append(dict_tqa)

        dict_task_based = {}
        dict_task_based["engine"] = "task_based"
        dict_task_based["input"] = task_based_input
        dict_task_based["output"] = task_based_output
        dict_task_based["spent"] = task_based_spent
        list.append(dict_task_based)

        dict_faq = {}
        dict_faq["engine"] = "FAQ"
        dict_faq["input"] = faq_input
        dict_faq["output"] = faq_output
        dict_faq["spent"] = "——"
        list.append(dict_faq)

        response_dict["list"] = list

        print("=====response: %s" % response_dict)

        return jsonify(status=0, msg="ok", data=response_dict)
    except Exception as e:
        logger.error("corequery请求返回异常")
        logger.error(e)
        return jsonify(status=500, msg="corequery请求返回异常，检查服务是否正常！")
