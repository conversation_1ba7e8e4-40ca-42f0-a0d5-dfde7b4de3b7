#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
This module provide configure file management service in i18n environment.

Authors: <AUTHORS>
"""

import xlrd
import xlwt
import os
import sys
import time
from request_large_model import RequestLargeModelObj, request_large_model_error

# 设置默认编码为utf-8
reload(sys)
sys.setdefaultencoding('utf-8')

def process_excel_file(input_file):
    """
    处理Excel文件，读取数据并调用大模型API进行评分
    :param input_file: Excel文件路径
    :return: None
    """
    try:
        # 打开Excel文件用于读取
        read_workbook = xlrd.open_workbook(input_file)
        
        # 打印所有sheet名称
        print "可用的sheet名称:"
        for sheet_name in read_workbook.sheet_names():
            print "- {}".format(sheet_name)
        
        # 尝试获取sheet
        try:
            read_sheet = read_workbook.sheet_by_name("数据信息")
        except xlrd.biffh.XLRDError:
            # 如果找不到"数据信息"，尝试使用第一个sheet
            print "未找到名为'数据信息'的sheet，将使用第一个sheet"
            read_sheet = read_workbook.sheet_by_index(0)
        
        # 创建新的workbook用于写入
        write_workbook = xlwt.Workbook(encoding='utf-8')
        write_sheet = write_workbook.add_sheet("数据信息")
        
        # 定义列的位置
        QUERY_COL = 1  # 用户query列
        REF_ANSWER_COL = 2  # 标准答案列
        MODEL_RESPONSE_COL = 3  # corequery结果列
        SCORE_COL = 4  # 得分列（E列）
        REASON_COL = 5  # 原因列（F列）
        ERROR_COL = 6  # 第三方评判信息列（G列）
        
        # 复制表头
        for col in range(read_sheet.ncols):
            write_sheet.write(0, col, read_sheet.cell_value(0, col))
        
        # 配置大模型
        large_model_config = {
            # 'large_model_type': 'gpt3.5',
            'large_model_type': 'deepseek-v3',
            # 'api_key': 'FCE6A0BJFBO12KKQRZ',
            'auth': 'Bearer bce-v3/ALTAK-LthBWVbXdEGW4OOjKxXZ0/0b16dadd78b6544ad16001b8f87705fe88709839',

            'prompt_template': """你是专业的对话场景评测人员，请根据问题和参考答案对模型输出从准确性、相关性、完整性这三个维度综合进行打分，分档0、1、2。打分时参考注意事项，具体的打分标准请参照分档描述，此外你必须要提供打分和对应的评分依据。
                                你的回答请按照以下格式输出，注意得分和原因之间要进行换行。
                                【得分】xxx
                                【原因】xxx
                                下面给出注意事项、分档描述、问题、参考答案、模型输出
                                【注意事项】 1.如果模型输出有关于来源的描述，例如如果模型中有"答案由一言生成"、"来源xxx"等内容，请将内容进行忽略  2.问题和参考答案均为中文文档对话数据 3、必须要提供打分和对应的评分依据   4、模型输出包含“当前知识库找不到对应答案”必须为0分
                                【分档描述】
                                0: 模型输出包含“当前知识库找不到对应答案”则为0分。或者三个维度均不达标，即以下三个条件中任一个条件即可打0分。
                                准确性：模型输出与参考答案语义完全错误，虚构文档未提及的信息，或与文档事实矛盾，或内容不真实，不具有科学性，或模型输出知识库找不到对应答案
                                相关性：模型输出与参考答案不相关，表现出模型输出为答非所问，严重跑题、存在大量冗余或存在无关内容超过80%、
                                完整性：模型输出为完全未能覆盖所有参考答案，遗漏所有参考答案信息点模型输出与参考答案语义和相关数据信息完全不符合
                                1: 部分维度符合/存在问题但整体较为符合，满足以下任一个条件即可打1分。
                                准确性：模型输出与参考答案语义基本一致，但存在轻微错误、模糊表述或术语误用
                                相关性：模型输出未能完全围绕与参考答案展开，包含跑题、次要信息、存在冗余或存在无关内容，但跑题、次要信息、冗余或无关内容不超过30%
                                完整性：模型输出为未能覆盖所有参考答案必要信息点，存在一定遗漏，或仅回答部分问题
                                2:所有维度均达标，满足以下所有条件，打2分。
                                准确性：模型输出与参考答案语义完全符合
                                相关性：模型输出完全围绕与参考答案展开，无冗余或无关内容
                                完整性：模型输出覆盖所有参考答案必要信息点，无遗漏
                                【问题】{query}
                                【参考答案】{reference_answer}
                                【模型输出】{model_response}
     """,
            'pattern': ur"【得分】([012])\s*【原因】(.*)",
            # 'route_list': ['result']
        }
        
        # 创建大模型请求对象
        req_large_model_obj = RequestLargeModelObj(large_model_config)
        
        # 处理每一行数据
        for row in range(1, read_sheet.nrows):
            try:
                # 读取数据
                query = read_sheet.cell_value(row, QUERY_COL)
                reference_answer = read_sheet.cell_value(row, REF_ANSWER_COL)
                model_response = read_sheet.cell_value(row, MODEL_RESPONSE_COL)
                
                # 复制原始数据（除了要写入评分的列）
                for col in range(read_sheet.ncols):
                    if col not in [SCORE_COL, REASON_COL, ERROR_COL]:
                        write_sheet.write(row, col, read_sheet.cell_value(row, col))
                
                # 创建prompt字典
                prompt_dict = {
                    "query": query,
                    "reference_answer": reference_answer,
                    "model_response": model_response
                }
                
                # 调用大模型API
                res_list = req_large_model_obj.request_large_model_and_parse_result_qianfan(prompt_dict)
                
                # 写入得分和原因
                if len(res_list) >= 2:
                    write_sheet.write(row, SCORE_COL, res_list[0])
                    write_sheet.write(row, REASON_COL, res_list[1])
                
                # 如果有异常回复，写入第三方评判信息
                if len(res_list) > 2 and res_list[2] in request_large_model_error.values():
                    write_sheet.write(row, ERROR_COL, res_list[2])
                
                # 添加30秒延迟，确保不超过QPS限制
                time.sleep(10)
                
                # 打印大模型返回的原始文本和解析后的结果
                res_text = res_list[2] if len(res_list) > 2 else res_list[1]
                print "大模型返回的原始文本: {}".format(res_text)
                print "解析后的结果: {}".format(res_list)
                
            except Exception as e:
                print "处理第{}行数据时出错: {}".format(row + 1, str(e))
                # 复制原始数据（除了要写入评分的列）
                for col in range(read_sheet.ncols):
                    if col not in [SCORE_COL, REASON_COL, ERROR_COL]:
                        write_sheet.write(row, col, read_sheet.cell_value(row, col))
                # 写入错误信息
                write_sheet.write(row, ERROR_COL, "处理数据时出错: {}".format(str(e)))
                # 即使出错也添加延迟
                time.sleep(10)
        
        # 保存结果
        output_file = os.path.splitext(input_file)[0] + "_result.xls"
        write_workbook.save(output_file)
        print "处理完成，结果已保存到: {}".format(output_file)
        
    except Exception as e:
        print "处理Excel文件时出错: {}".format(str(e))

if __name__ == "__main__":
    # 直接使用文件路径，避免使用raw_input
    input_file = "/Users/<USER>/PycharmProjects/auto-evaluate/evaluate_new/largemodeltest.xlsx"
    process_excel_file(input_file)