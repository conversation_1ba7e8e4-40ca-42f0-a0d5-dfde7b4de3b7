#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
兴业银行
"""
import json
import re
import time

import openpyxl
import requests
from openpyxl import Workbook



# 读取excel获取query
def read_excel():
    """
    读取Excel文件，返回两个列表，第一个是查询语句列表，第二个是结果列表。
    
    Args:
        无参数。
    
    Returns:
        list (tuple): 两个元素为list类型，分别是查询语句列表和结果列表。
            - querys (list[str]): 查询语句列表，每个元素都是一个字符串。
            - result67s (list[str]): 结果列表，每个元素都是一个字符串。
    
    Raises:
        无异常抛出。
    """
    workbook = openpyxl.load_workbook(queryFile, data_only=True)
    # sheet_names = workbook.get_sheet_names()
    sheet_names = workbook.sheetnames

    querys = []
    result67s = []
    for i in range(len(sheet_names)):
        # sheet = workbook.get_sheet_by_name(sheet_names[i])
        sheet = workbook[sheet_names[i]]
        skip_first = True
        for row_i in sheet.rows:
            if skip_first:
                skip_first = False
                continue
            query = row_i[0].value
            reusult67 = row_i[1].value
            querys.append(query)
            result67s.append(reusult67)
    return querys, result67s


# nlu分析
def entity_question(query):
    """
    :param entity_id:
    :param query:
    :return:
    """
    res = nlu_recognize(query)
    # print("nlu结果%s--"%res)
    # print(res)
    data = res['data']
    if data is None or data['entities'] is None:
        return None
    entities = data['entities']
    reslut = []
    for key in entities:
        reslut.append(entities[key][0]["nameZh"])
    reslut = ",".join(reslut)
    return reslut


def nlu_recognize(query):
    """
    @info: 封装get方法
    :param query: nlu分析的query
    :return: response内容或者None
    """
    url = "http://**************:8844"
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': 'NGD e4b69b58-c594-4452-b21d-7b958969e538',
        'Content-Type': 'application/json'
    }
    api = url + "/api/v2/nlu/recognize?agentId=1167a228-05f0-4ca5-ac43-e8f60d48d422"
    json_nlu = {'query': query}
    flag = True
    while flag:
        try:
            # print(api)
            res = post(api, json_nlu, headers)
            flag = False
            return res
        except Exception:
            time.sleep(2)
            print(u"\nlose connection")

    if res['code'] != 200:
        print("\n")
        print(res['code'])
        print(res['msg'])


def post(send_url, content, header):
    """
    @info: 封装post方法
    :param content:
    :param api:
    :return: response内容或者None
    """
    flag = True
    while flag:
        try:
            req = requests.post(send_url, data=json.dumps(content), headers=header)
            res = req.json()
            flag = False
            return res
        except Exception as e:
            time.sleep(2)
            print("出现如下异常%s" % e)


def clean_and_split(s):
    """
    清理并切分字符串，将方括号和多个分隔符（逗号、空格或其组合）分离。
    
    Args:
        s (str): 需要清理和切分的字符串。
    
    Returns:
        list[str]: 清理后的字符串列表，每个元素为一个单词或标点符号。
    """
    # 去除方括号
    s = s.strip('[]')
    # 使用正则表达式分割，考虑逗号后面可能有空格
    return re.split(r'，\s*|\s*,\s*|\s+', s)  # 这里假设逗号、空格或其组合是分隔符


# 结果写入excel
def toExcel():
    """
    将结果导出到Excel文件中，包括查询语句、67结果、NLP分析结果和比较结果。
    
    Args:
        None.
    
    Returns:
        None.
    
    Raises:
        None.
    """
    output_wookbook = Workbook()
    result_sheet = output_wookbook.create_sheet(index=0, title=u'result')
    result_sheet.title = u"测试结果"
    result_sheet.cell(1, 1, "query")
    result_sheet.cell(1, 2, "67结果")
    result_sheet.cell(1, 3, "762结果")
    result_sheet.cell(1, 4, "result")
    row_index = 0

    # 循环并nlu分析
    querys, result67s = read_excel()
    for index, query in enumerate(querys):
        row_index += 1
        print("%s query: %s" % (index, query))
        result = entity_question(query)
        # print("result: %s" % result)
        if result == "":
            result = "null"
        else:
            result = "[" + result + "]"
        result_sheet.cell(row_index + 1, 1, query)
        result_sheet.cell(row_index + 1, 2, result67s[index])
        result_sheet.cell(row_index + 1, 3, result)

        #  对比结果
        # print("----: %s" % clean_and_split(str(result)))
        # print(type(clean_and_split(str(result))))
        set1 = set(clean_and_split(str(result)))

        result67 = str(result67s[index])
        result67 = result67.replace("货币范围实体", "金额范围")
        # result67 = result67.replace("货币实体", "金额")
        result67 = result67.replace("时间范围实体", "时间范围")
        result67 = result67.replace("日期实体", "日期")
        result67 = result67.replace("月实体", "月")
        result67 = result67.replace("年实体", "年")
        result67 = result67.replace("IP实体", "IP")
        result67 = result67.replace("地点位移实体", "地点位移")
        result67 = result67.replace("航班号实体", "航班号")
        result67 = result67.replace("自然数实体", "数字编号")
        result67 = result67.replace("机场实体", "机场")
        result67 = result67.replace("序数实体", "序数")
        result67 = result67.replace("航班座位号实体", "航班座位号")
        result67 = result67.replace("手机号码实体", "手机号码")
        result67 = result67.replace("时间集合实体", "日期集合")
        result67 = result67.replace("URL实体", "URL")
        result67 = result67.replace("机场地点实体", "机场地点")
        result67 = result67.replace("持续时间实体", "持续时间")
        result67 = result67.replace("数字实体", "数值")
        result67 = result67.replace("地点实体", "地点")
        result67 = result67.replace("电子邮箱实体", "电子邮箱")
        result67 = result67.replace("机场航线实体", "机场航线")
        result67 = result67.replace("时间实体", "时间")
        result67 = result67.replace("QQ实体", "QQ")
        result67 = result67.replace("人名实体", "人名")
        result67 = result67.replace("机构名实体", "机构名")
        result67 = result67.replace("身份证号实体", "身份证号")
        result67 = result67.replace("时间位移实体", "时间位移")

        listResults = clean_and_split(result67)
        for i in range(len(listResults)):
            if listResults[i] == "金额":
                listResults[i] = "金额1"
            if listResults[i] == "货币实体":
                listResults[i] = "金额"

        # print(listResults)
        set2 = set(listResults)

        # print("set1: %s" % set1)
        # print("set2: %s" % set2)
        # print(set1)
        # print(set2)

        # 比较集合是否相等，结果写入excel
        if set1 == set2:
            result_sheet.cell(row_index + 1, 4, "一致")
        else:
            result_sheet.cell(row_index + 1, 4, "不一致")

        # 保存excel
        if index == 5000:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            output_wookbook.save("evaluate_result_%s_%s.xlsx" % (index, time_now))
        if index == 10000:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            output_wookbook.save("evaluate_result_%s_%s.xlsx" % (index, time_now))
        if index == 15000:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            output_wookbook.save("evaluate_result_%s_%s.xlsx" % (index, time_now))
        if index == 20000:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            output_wookbook.save("evaluate_result_%s_%s.xlsx" % (index, time_now))
        if index == 25000:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            output_wookbook.save("evaluate_result_%s_%s.xlsx" % (index, time_now))
        if index == 30000:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            output_wookbook.save("evaluate_result_%s_%s.xlsx" % (index, time_now))
        if index == 40000:
            time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
            output_wookbook.save("evaluate_result_%s_%s.xlsx" % (index, time_now))
    time_now = time.strftime("%Y%m%d%H%M%S", time.localtime())
    output_wookbook.save("evaluate_result_total_%s.xlsx" % (time_now))

# 开始执行
queryFile = "entityData3000.xlsx"
time1 = time.time()
toExcel()
time2 = time.time()
print(time2 - time1)
