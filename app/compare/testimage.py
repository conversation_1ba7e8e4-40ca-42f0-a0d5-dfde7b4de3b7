# -*- coding: utf-8 -*-
global db
from ..models.models import OutReport
from app.compare import testimage
import openpyxl
from openpyxl import Workbook
from openpyxl.chart import <PERSON><PERSON><PERSON>, Reference
import sys
import os
import time
from flask import request, jsonify
from app.models.exts import get_db
from app.models.models import TestReport
global db
db = get_db()
session = db.session
reload(sys)
sys.setdefaultencoding('utf8')
def strftime(timestamp, format_string='%Y-%m-%d %H:%M:%S'):
    """
    上传时间
    :return:
    """
    return time.strftime(format_string, time.localtime(timestamp))
@testimage.route('/evaluate/createOutReport', methods=['POST'])
def testfile():
    """
    合并报告
    :return:
    """
    data_form = request.form
    print data_form
    if data_form is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    ids = data_form.get('ids')
    if ids is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    try:
        print type(ids)
        id_list = ids.split(',')
        print id_list
        source= []
        sourceName = []
        for db_id in id_list:
            print db_id
            task = session.query(TestReport).filter(TestReport.id == db_id).first()
            file_path = task.file_path.encode('utf-8')
            if os.path.exists(file_path):
                source.append(file_path)
                sourceName.append(task.report_name.encode('utf-8'))
        workbook = Workbook()
        for i in range(len(source)):
            workbook1 = openpyxl.load_workbook(source[i])
            if (sourceName[i].split("-")[0] == "tableqa_rounds"):
                print sourceName[i].split("-")[0]
                new_workbook = workbook.create_sheet(title=sourceName[i].split("-")[0])
                colnum = 0
                for sheet_name in workbook1.sheetnames:
                    sheet1 = workbook1[sheet_name]
                    for col in sheet1.iter_cols(values_only=True):
                        colnum += 1
                    for row in sheet1.iter_rows(values_only=True):
                        new_workbook.append(row)
                bar_chart = BarChart()
                bar_chart2 = BarChart()

                data = Reference(worksheet=new_workbook, min_row=1, max_row=6, min_col=2, max_col=colnum)
                x_axis = Reference(worksheet=new_workbook, min_col=1, min_row=2, max_row=6, max_col=1)
                bar_chart.add_data(data, titles_from_data=True)
                bar_chart.set_categories(x_axis)
                bar_chart.title = '效果测试数据'.decode('utf-8')
                bar_chart.gapWidth = 50
                bar_chart.width = 20
                bar_chart.height = 10

                data1 = Reference(worksheet=new_workbook, min_row=7, max_row=9, min_col=2, max_col=colnum)
                x_axis1 = Reference(worksheet=new_workbook, min_col=1, min_row=8, max_row=9, max_col=1)
                bar_chart2.add_data(data1, titles_from_data=True)
                bar_chart2.set_categories(x_axis1)
                bar_chart2.title = '效果指标'.decode('utf-8')
                bar_chart2.gapWidth = 300
                bar_chart2.width = 18
                bar_chart2.height = 10

                new_workbook.add_chart(bar_chart, 'A11')
                new_workbook.add_chart(bar_chart2, 'N11')
            elif (sourceName[i].split("-")[0] == "intent"):
                new_workbook = workbook.create_sheet(title=sourceName[i].split("-")[0])
                colnum = 0
                for sheet_name in workbook1.sheetnames:
                    sheet1 = workbook1[sheet_name]
                    for col in sheet1.iter_cols(values_only=True):
                        colnum += 1
                    for row in sheet1.iter_rows(values_only=True):
                        new_workbook.append(row)
                bar_chart = BarChart()
                bar_chart2 = BarChart()

                data = Reference(worksheet=new_workbook, min_row=1, max_row=6, min_col=2, max_col=colnum)
                x_axis = Reference(worksheet=new_workbook, min_col=1, min_row=2, max_row=6, max_col=1)
                bar_chart.add_data(data, titles_from_data=True)
                bar_chart.set_categories(x_axis)
                bar_chart.title = '效果测试数据'.decode('utf-8')
                bar_chart.gapWidth = 50
                bar_chart.width = 20
                bar_chart.height = 10

                data1 = Reference(worksheet=new_workbook, min_row=7, max_row=14, min_col=2, max_col=colnum)
                x_axis1 = Reference(worksheet=new_workbook, min_col=1, min_row=8, max_row=14, max_col=1)
                bar_chart2.add_data(data1, titles_from_data=True)
                bar_chart2.set_categories(x_axis1)
                bar_chart2.title = '效果指标'.decode('utf-8')
                bar_chart2.gapWidth = 300
                bar_chart2.width = 18
                bar_chart2.height = 10
                new_workbook.add_chart(bar_chart, 'A19')
                new_workbook.add_chart(bar_chart2, 'N19')
            elif (sourceName[i].split("-")[0] == "entity"):
                new_workbook = workbook.create_sheet(title=sourceName[i].split("-")[0])
                colnum = 0
                for sheet_name in workbook1.sheetnames:
                    sheet1 = workbook1[sheet_name]
                    for col in sheet1.iter_cols(values_only=True):
                        colnum += 1
                    for row in sheet1.iter_rows(values_only=True):
                        new_workbook.append(row)
                bar_chart = BarChart()
                bar_chart2 = BarChart()

                data = Reference(worksheet=new_workbook, min_row=1, max_row=4, min_col=2, max_col=colnum)
                x_axis = Reference(worksheet=new_workbook, min_col=1, min_row=2, max_row=4, max_col=1)
                bar_chart.add_data(data, titles_from_data=True)
                bar_chart.set_categories(x_axis)
                bar_chart.title = '效果测试数据'.decode('utf-8')
                bar_chart.gapWidth = 50
                bar_chart.width = 20
                bar_chart.height = 10

                data1 = Reference(worksheet=new_workbook, min_row=5, max_row=7, min_col=2, max_col=colnum)
                x_axis1 = Reference(worksheet=new_workbook, min_col=1, min_row=6, max_row=7, max_col=1)
                bar_chart2.add_data(data1, titles_from_data=True)
                bar_chart2.set_categories(x_axis1)
                bar_chart2.title = '效果指标'.decode('utf-8')
                bar_chart2.gapWidth = 300
                bar_chart2.width = 18
                bar_chart2.height = 10
                new_workbook.add_chart(bar_chart, 'A11')
                new_workbook.add_chart(bar_chart2, 'N11')
            elif (sourceName[i].split(".")[0] == "faq"):
                new_workbook = workbook.create_sheet(title=sourceName[i].split("-")[0])
                colnum = 0
                for sheet_name in workbook1.sheetnames:
                    sheet1 = workbook1[sheet_name]
                    for col in sheet1.iter_cols(values_only=True):
                        colnum += 1
                    for row in sheet1.iter_rows(values_only=True):
                        new_workbook.append(row)
                bar_chart = BarChart()
                bar_chart2 = BarChart()

                data = Reference(worksheet=new_workbook, min_row=1, max_row=7, min_col=2, max_col=colnum)
                x_axis = Reference(worksheet=new_workbook, min_col=1, min_row=2, max_row=7, max_col=1)
                bar_chart.add_data(data, titles_from_data=True)
                bar_chart.set_categories(x_axis)
                bar_chart.title = '效果测试数据'.decode('utf-8')
                bar_chart.gapWidth = 50
                bar_chart.width = 20
                bar_chart.height = 10

                data1 = Reference(worksheet=new_workbook, min_row=8, max_row=12, min_col=2, max_col=colnum)
                x_axis1 = Reference(worksheet=new_workbook, min_col=1, min_row=9, max_row=12, max_col=1)
                bar_chart2.add_data(data1, titles_from_data=True)
                bar_chart2.set_categories(x_axis1)
                bar_chart2.title = '效果指标'.decode('utf-8')
                bar_chart2.gapWidth = 300
                bar_chart2.width = 18
                bar_chart2.height = 10
                new_workbook.add_chart(bar_chart, 'A18')
                new_workbook.add_chart(bar_chart2, 'N18')
            elif (sourceName[i].split("-")[0] == "attitude"):
                new_workbook = workbook.create_sheet(title=sourceName[i].split("-")[0])
                colnum = 0
                for sheet_name in workbook1.sheetnames:
                    sheet1 = workbook1[sheet_name]
                    for col in sheet1.iter_cols(values_only=True):
                        colnum += 1
                    for row in sheet1.iter_rows(values_only=True):
                        new_workbook.append(row)
                bar_chart = BarChart()
                bar_chart2 = BarChart()

                data = Reference(worksheet=new_workbook, min_row=1, max_row=4, min_col=2, max_col=colnum)
                x_axis = Reference(worksheet=new_workbook, min_col=1, min_row=2, max_row=4, max_col=1)
                bar_chart.add_data(data, titles_from_data=True)
                bar_chart.set_categories(x_axis)
                bar_chart.title = '效果测试数据'.decode('utf-8')
                bar_chart.gapWidth = 50
                bar_chart.width = 20
                bar_chart.height = 10

                data1 = Reference(worksheet=new_workbook, min_row=5, max_row=7, min_col=2, max_col=colnum)
                x_axis1 = Reference(worksheet=new_workbook, min_col=1, min_row=6, max_row=7, max_col=1)
                bar_chart2.add_data(data1, titles_from_data=True)
                bar_chart2.set_categories(x_axis1)
                bar_chart2.title = '效果指标'.decode('utf-8')
                bar_chart2.gapWidth = 300
                bar_chart2.width = 18
                bar_chart2.height = 10
                new_workbook.add_chart(bar_chart, 'A11')
                new_workbook.add_chart(bar_chart2, 'N11')
        # workbook.save(save_path)
        # # 打开 Excel 文件
        # workbook = openpyxl.load_workbook(path + 'merged_file.xlsx')
        # # 获取所有 sheet 名称
        sheet_names = workbook.sheetnames
        # 获取第一个 sheet
        first_sheet = workbook[sheet_names[0]]
        # 遍历每个 sheet
        b1_value = ""
        name_value = []
        for sheet_name in sheet_names[1:]:
            # 获取当前 sheet
            current_sheet = workbook[sheet_name]
            a11_value = current_sheet['A40'].value
            name_value.append(sheet_name)
            result = ', '.join(name_value)
            # 在第一个 sheet 中的第一列追加 A11 单元格的值
            b1_value = str(current_sheet['B1'].value)
            first_sheet.cell(row=1, column=1).value = b1_value + "效果测试报告"
            first_sheet.cell(row=2, column=1).value = "【评测项目】" + b1_value
            first_sheet.cell(row=3, column=1).value = "【评测模块】" + str(result)
            first_sheet.cell(row=4, column=1).value = "【评测结论】"
            first_sheet.append([a11_value])
        # 保存结果到原始 Excel 文件
        cur_path = os.getcwd()
        path = cur_path + '/outreport_file/'
        if not os.path.exists(path):
            os.makedirs(path)
        str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        up_time = strftime(time.time())
        save_path = (path + b1_value + "出厂报告" + str_time + ".xlsx").decode('utf-8')
        workbook.save(save_path)
        new_report = OutReport(report_name=save_path.split('/')[-1],
                                upload_time=up_time,
                                file_path=save_path
                                )
        db.session.add(new_report)
        db.session.commit()
        res = {
            'code': 200,
            'msg': "OK"
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        session.rollback()
        res = {
            'code': 500,
            'msg': '服务器异常，生成失败'
        }
        return jsonify(res), 500
@testimage.route('/evaluate/getOutReportList', methods=['GET'])
def get_out_report_list():
    """
    获取出厂报告列表
    """
    data_args = request.args
    pn = data_args.get('pn', 1, type=int)
    ps = data_args.get('ps', 20, type=int)
    task_list = []
    # print pn, ps
    try:
        print pn, ps
        tmp_data = session.query(OutReport)
        # 查询数据库中总条数
        total = tmp_data.count()
        task_info_list = tmp_data.order_by(OutReport.id.desc()).offset((pn - 1) * ps).limit(ps).all()
        for task in task_info_list:
            print task.taskToDict()
            task_list.append(task.taskToDict())
        res = {
            'code': 200,
            'data': {
                'pn': pn,
                'ps': ps,
                'total': total,
                'list': task_list
            },
            'msg': 'OK'
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        res = {
            'code': 500,
            'msg': '服务器异常'
        }
        return jsonify(res), 500

@testimage.route('/evaluate/deleteOutReport', methods=['POST'])
def delete_out_report():
    """
    删除出厂报告
    :return:
    """
    data_form = request.form
    print data_form
    if data_form is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    ids = data_form.get('ids')
    if ids is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    try:
        print type(ids)
        id_list = ids.split(',')
        print id_list
        for db_id in id_list:
            print db_id
            task = session.query(OutReport).filter(OutReport.id == db_id).first()
            session.query(OutReport).filter(OutReport.id == db_id).delete()
            file_path = task.file_path.encode('utf-8')
            if os.path.exists(file_path):
                os.remove(file_path)
        session.commit()
        res = {
            'code': 200,
            'msg': "OK"
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        session.rollback()
        res = {
            'code': 500,
            'msg': '服务器异常，删除失败'
        }
        return jsonify(res), 500
# if __name__ == '__main__':
#     source = ['tableqa.xlsx', 'intent.xlsx']
#     testfile(source)