# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2022/12/7 19:00:00
"""
import openpyxl
from openpyxl import Workbook
from openpyxl.chart import Bar<PERSON>hart, Reference
import sys
import os
import sqlite3
import time
from flask import request, Flask, jsonify, send_file
from app.compare import compares
from app.evaluate.up_query import strftime
from app.models.exts import get_db
from app.models.models import TestReport
global db
db = get_db()
session = db.session
reload(sys)
sys.setdefaultencoding('utf8')
# 表格问答
@compares.route('/evaluate/compare', methods=['POST'])
def compare():
    """
    读sqlite生成Excel
    :return:
    """
    # str = args
    list = [['版本', '用例总数', '有返回数据', '无返回数据', '返回正确数据', '返回错误数据', '召回率', '准确率']]
    result = request.form.get('version')
    result1 = result.encode("utf-8")
    strr = tuple(result1.split(","))
    workbook = Workbook()
    sheet = workbook.active
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "./db/evaluates.db")
    conn = sqlite3.connect(spath)
    c = conn.cursor()
    for i in strr:
        mysel = c.execute("select version, testcasecount, response_count, no_response_count, correct_count,"
                          "error_count, call_percent, prec_percent "
                          "from analysis where version_finish_time = ('%s')" % (i))
        for data in mysel:
            last_two_digits = data[-2:]
            # 将数字采用四舍五入方法转换为两位小数
            one = round(last_two_digits[0], 2)
            two = round(last_two_digits[1], 2)
            # 创建一个新的tuple
            new_tuple = data[:-2] + (one, two) + data[-2:]
            list.append(new_tuple)
    # mysel = c.execute("select * from version where version in (%s)" % (strr,))
    # 数据库所有字段名
    # col_name_list = [tuple[0] for tuple in c.description]
    # list.append(col_name_list)
    list1 = []
    for i in range(len(list[0])):
        t = []
        for j in range(len(list)):
            t.append(list[j][i])
        list1.append(t)
    list1.insert(6, list1[0])
    # for data_row in list1:
    #     data_row[6] = "{:.2%}".format(data_row[6])  # 假设 call_percent 在索引 6 处
    #     data_row[7] = "{:.2%}".format(data_row[7])  # 假设 prec_pe
    # 1、把数据写入 sheet
    for row in list1:
        sheet.append(row)

    # 2、实例化一个 BarChart
    bar_chart = BarChart()
    bar_chart2 = BarChart()

    bar_chart.dLbls = openpyxl.chart.label.DataLabelList()
    bar_chart2.dLbls = openpyxl.chart.label.DataLabelList()
    if len(strr) <= 8:
        bar_chart.dLbls.showVal = True
        bar_chart2.dLbls.showVal = True
    else:
        bar_chart.dLbls.showVal = False
        bar_chart2.dLbls.showVal = False

    # 3、指定 BarChart 的数据范围，分类的范围，设置样式、标题等
    data = Reference(worksheet=sheet, min_row=1, max_row=6, min_col=2, max_col=len(strr)+1)
    x_axis = Reference(worksheet=sheet, min_col=1, min_row=2, max_row=6, max_col=1)
    bar_chart.add_data(data, titles_from_data=True)
    bar_chart.set_categories(x_axis)
    bar_chart.title = '效果测试数据'
    bar_chart.gapWidth = 50
    bar_chart.width = 20
    bar_chart.height = 10
    # bar_chart.y_axis.title = '横轴'
    # bar_chart.x_axis.title = '纵轴'
    # bar_chart.style = 10 #设置样式

    data1 = Reference(worksheet=sheet, min_row=7, max_row=9, min_col=2, max_col=len(strr) + 1)
    x_axis1 = Reference(worksheet=sheet, min_col=1, min_row=8, max_row=9, max_col=1)
    bar_chart2.add_data(data1, titles_from_data=True)
    bar_chart2.set_categories(x_axis1)
    bar_chart2.title = '效果指标'
    bar_chart2.gapWidth = 300
    bar_chart2.width = 18
    bar_chart2.height = 10

    # 设置柱状图位置
    sheet.add_chart(bar_chart, 'A13')
    sheet.add_chart(bar_chart2, 'N13')

    # 读输入版本数据库中准确率的最大值和最小值，相差值，并输出到Excel中
    hight = []
    low = []
    mysel1 = c.execute("select version, prec_percent from analysis where version_finish_time = ('%s')" % (strr[0]))
    for data in mysel1:
        hight.append(data)
    mysel2 = c.execute("select version, prec_percent from analysis where version_finish_time = ('%s')" % (strr[1]))
    for i in mysel2:
        low.append(i)
    # mysel3 = c.execute("select max(cast(accuracy as float)) - min(cast(accuracy as float))"
    #                    " from version where version in %s" % (strr,))
    # data = mysel3.fetchone()
    print(hight)
    if float(hight[0][1]) > float(low[0][1]):
        # data = float(hight[0][1].strip('%')) - float(low[0][1].strip('%'))
        data = round(float(hight[0][1]) - float(low[0][1]), 2) * 100
        print(type(str(data)))
        res = "tableqa当前版本:" '%s' "，准确率为:" '%s' "，tableqa上一版本:" '%s' "，准确率为:" '%s'"，准确率上升了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    else:
        data = round(float(low[0][1]) - float(hight[0][1]), 2) * 100
        print(data)
        res = "tableqa当前版本:" '%s' "，准确率为:" '%s' "，tableqa上一版本:" '%s' "，准确率为:" '%s'"，准确率下降了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    sheet['A40'] = res
    # sheet.move_range('A10:B10', rows=16, cols=0)
    # 4、保存
    try:
        cur_path = os.getcwd()
        path = cur_path + '/com_reports/'
        if not os.path.exists(path):
            os.makedirs(path)
        str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        up_time = strftime(time.time())
        save_path = path + "tableqa_rounds-" + str_time + ".xlsx"
        workbook.save(save_path)
        new_report = TestReport(report_name=save_path.split('/')[-1],
                                upload_time=up_time,
                                file_path=save_path
                                )
        db.session.add(new_report)
        db.session.commit()
    except Exception as e:
        print(e)
        return {'code': 500, 'msg': '文件保存失败'}

    return {'code': 200, 'msg': '文件保存成功', 'path': save_path}
@compares.route('/evaluate/select', methods=['GET'])
def select():
    """
    前端下拉列表展示
    :return:
    """
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "db/evaluates.db")
    conn = sqlite3.connect(spath)
    conn.row_factory = lambda cursor, row: row[0]
    c = conn.cursor()
    # mysel = c.execute("select version, finish_time from task where task_id = (select task_id from analysis)")
    mysel = c.execute("select version_finish_time from analysis where version_finish_time IS NOT NULL")
    data = c.fetchall()
    select_result = []
    try:
        for i in data:
            data = i.encode("utf-8")
            if i != '':
                select_result.append(data)
                print(select_result)
        return jsonify(code=200, msg="ok", data=select_result)
    except Exception as e:
        return jsonify(code=500, msg="error")
@compares.route('/evaluate/download', methods=["GET", "POST"])
def download_file():
    """
    下载文件
    :return:
    """
    # 获取文件名
    file_path = request.args.get('fileName')
    file_name = str(file_path).split('/')[-1]

    print str(file_path)
    if file_path:
        # 判断文件存不存在
        if os.path.exists(file_path):
            response = send_file(file_path, as_attachment=True, mimetype='application/octet-stream')
            response.headers['Content-Disposition'] = 'attachment;filename=' + file_name
            return response, 200
        else:
            data = {
                "code": 404,
                "msg": "抱歉，文件未找到!"
            }
            return jsonify(data), 404
    else:
        data = {
            "code": 404,
            "msg": "抱歉，文件未找到!"
        }
        return jsonify(data), 404
@compares.route('/evaluate/intent_select', methods=['GET'])
def intent_select():
    """
    前端下拉列表展示
    :return:
    """
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "db/evaluates.db")
    conn = sqlite3.connect(spath)
    conn.row_factory = lambda cursor, row: row[0]
    c = conn.cursor()
    # mysel = c.execute("select version, finish_time from task where task_id = (select task_id from analysis)")
    mysel = c.execute("select version_finish_time from intents_analysis where version_finish_time IS NOT NULL")
    data = c.fetchall()
    select_result = []
    try:
        for i in data:
            data = i.encode("utf-8")
            if i != '':
                select_result.append(data)
                print(select_result)
        return jsonify(code=200, msg="ok", data=select_result)
    except Exception as e:
        return jsonify(code=500, msg="error")
# 意图
@compares.route('/evaluate/intent_compare', methods=['POST'])
def intent_compare():
    """
    读sqlite生成Excel
    :return:
    """
    # str = args
    list = [['版本', '用例总数', '有返回数据', '有返回数据(澄清)', '有返回数据(非澄清)', '直接命中正确', '澄清命中',
             '召回率(包含澄清)', '召回率(不含澄清)', '准确召回率(包含澄清)', '准确召回率(不含澄清)', '精确率(包含澄清)', '精确率(不含澄清)']]
    result = request.form.get('version')
    result1 = result.encode("utf-8")
    strr = tuple(result1.split(","))
    workbook = Workbook()
    sheet = workbook.active
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "./db/evaluates.db")
    conn = sqlite3.connect(spath)
    c = conn.cursor()
    for i in strr:
        mysel = c.execute("select version, testcasecount, response_count, response_count_claify, "
                          "response_count_not_claify, response_correct_count, clarify_correct_count, "
                          "call_percent_include_clarify, call_percent_not_include_clarify, "
                          "correct_call_percent_include_clarify, correct_call_percent_not_include_clarify, "
                          "prec_percent_include_clarify, prec_percent_not_include_clarify "
                          "from intents_analysis where version_finish_time = ('%s')" % (i))
        for data in mysel:
            last_two_digits = data[-6:]
            # 将数字采用四舍五入方法转换为两位小数
            one = round(last_two_digits[0], 2)
            two = round(last_two_digits[1], 2)
            three = round(last_two_digits[2], 2)
            four = round(last_two_digits[3], 2)
            five = round(last_two_digits[4], 2)
            six = round(last_two_digits[5], 2)
            # 创建一个新的tuple
            new_tuple = data[:-6] + (one, two, three, four, five, six) + data[-6:]
            list.append(new_tuple)
    # mysel = c.execute("select * from version where version in (%s)" % (strr,))
    # 数据库所有字段名
    # col_name_list = [tuple[0] for tuple in c.description]
    # list.append(col_name_list)
    print(list)
    list1 = []
    for i in range(len(list[0])):
        t = []
        for j in range(len(list)):
            t.append(list[j][i])
        list1.append(t)
    list1.insert(6, list1[0])

    # 1、把数据写入 sheet
    for row in list1:
        sheet.append(row)

    # 2、实例化一个 BarChart
    bar_chart = BarChart()
    bar_chart2 = BarChart()

    bar_chart.dLbls = openpyxl.chart.label.DataLabelList()
    bar_chart2.dLbls = openpyxl.chart.label.DataLabelList()
    if len(strr) <= 8:
        bar_chart.dLbls.showVal = True
        bar_chart2.dLbls.showVal = True
    else:
        bar_chart.dLbls.showVal = False
        bar_chart2.dLbls.showVal = False

    # 3、指定 BarChart 的数据范围，分类的范围，设置样式、标题等
    data = Reference(worksheet=sheet, min_row=1, max_row=6, min_col=2, max_col=len(strr)+1)
    x_axis = Reference(worksheet=sheet, min_col=1, min_row=2, max_row=6, max_col=1)
    bar_chart.add_data(data, titles_from_data=True)
    bar_chart.set_categories(x_axis)
    bar_chart.title = '效果测试数据'
    bar_chart.gapWidth = 50
    bar_chart.width = 20
    bar_chart.height = 10
    # bar_chart.y_axis.title = '横轴'
    # bar_chart.x_axis.title = '纵轴'
    # bar_chart.style = 10 #设置样式

    data1 = Reference(worksheet=sheet, min_row=7, max_row=14, min_col=2, max_col=len(strr) + 1)
    x_axis1 = Reference(worksheet=sheet, min_col=1, min_row=8, max_row=14, max_col=1)
    bar_chart2.add_data(data1, titles_from_data=True)
    bar_chart2.set_categories(x_axis1)
    bar_chart2.title = '效果指标'
    bar_chart2.gapWidth = 300
    bar_chart2.width = 18
    bar_chart2.height = 10

    # 设置柱状图位置
    sheet.add_chart(bar_chart, 'A19')
    sheet.add_chart(bar_chart2, 'N19')

    # 读输入版本数据库中准确率的最大值和最小值，相差值，并输出到Excel中
    hight = []
    low = []
    mysel1 = c.execute("select version, correct_call_percent_include_clarify, correct_call_percent_not_include_clarify from intents_analysis where version_finish_time = ('%s')" % (strr[0]))
    for data in mysel1:
        hight.append(data)
    mysel2 = c.execute("select version, correct_call_percent_include_clarify, correct_call_percent_not_include_clarify from intents_analysis where version_finish_time = ('%s')" % (strr[1]))
    for i in mysel2:
        low.append(i)
    # mysel3 = c.execute("select max(cast(accuracy as float)) - min(cast(accuracy as float))"
    #                    " from version where version in %s" % (strr,))
    # data = mysel3.fetchone()
    # print(hight)
    if float(hight[0][1]) > float(low[0][1]):
        # data = float(hight[0][1].strip('%')) - float(low[0][1].strip('%'))
        data = round(float(hight[0][1]) - float(low[0][1]), 2) * 100
        print(type(str(data)))
        res = "intent当前版本:" '%s' "，准确召回率(包含澄清)为:" '%s' "，intent上一版本:" '%s' "，" \
              "准确召回率(包含澄清)为:" '%s'"，准确召回率(包含澄清)上升了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    else:
        data = round(float(low[0][1]) - float(hight[0][1]), 2) * 100
        print(data)
        res = "intent当前版本:" '%s' "，准确召回率(包含澄清)为:" '%s' "，intent上一版本:" '%s' "，" \
              "准确召回率(包含澄清)为:" '%s'"，准确召回率(包含澄清)下降了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    sheet['A40'] = res
    if float(hight[0][2]) > float(low[0][2]):
        # data = float(hight[0][1].strip('%')) - float(low[0][1].strip('%'))
        data = round(float(hight[0][2]) - float(low[0][2]), 2) * 100
        print(type(str(data)))
        res2 = "intent当前版本:" '%s' "，准确召回率(不含澄清)为:" '%s' "，intent上一版本:" '%s' "，" \
               "准确召回率(不含澄清)为:" '%s'"，" \
               "准确召回率(不含澄清)上升了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][2], 2), low[0][0], round(low[0][2], 2), data)
    else:
        data = round(float(low[0][2]) - float(hight[0][2]), 2) * 100
        print(data)
        res2 = "intent当前版本:" '%s' "，准确召回率(不含澄清)为:" '%s' "，intent上一版本:" '%s' "，" \
               "准确召回率(不含澄清)为:" '%s'"，" \
               "准确召回率(不含澄清)下降了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][2], 2), low[0][0], round(low[0][2], 2), data)
    sheet['A40'] = res2
    # sheet.move_range('A10:B10', rows=16, cols=0)
    # 4、保存
    try:
        cur_path = os.getcwd()
        path = cur_path + '/com_reports/'
        if not os.path.exists(path):
            os.makedirs(path)
        str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        up_time = strftime(time.time())
        save_path = path + "intent-" + str_time + ".xlsx"
        workbook.save(save_path)
        new_report = TestReport(report_name=save_path.split('/')[-1],
                                upload_time=up_time,
                                file_path=save_path
                                )
        db.session.add(new_report)
        db.session.commit()
    except Exception as e:
        print(e)
        return {'code': 500, 'msg': '文件保存失败'}

    return {'code': 200, 'msg': '文件保存成功', 'path': save_path}
# 实体
@compares.route('/evaluate/entity_select', methods=['GET'])
def entity_select():
    """
    前端下拉列表展示
    :return:
    """
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "db/evaluates.db")
    conn = sqlite3.connect(spath)
    conn.row_factory = lambda cursor, row: row[0]
    c = conn.cursor()
    # mysel = c.execute("select version, finish_time from task where task_id = (select task_id from analysis)")
    mysel = c.execute("select version_finish_time from entitys_analysis where version_finish_time IS NOT NULL")
    data = c.fetchall()
    select_result = []
    try:
        for i in data:
            data = i.encode("utf-8")
            if i != '':
                select_result.append(data)
                print(select_result)
        return jsonify(code=200, msg="ok", data=select_result)
    except Exception as e:
        return jsonify(code=500, msg="error")
@compares.route('/evaluate/entity_compare', methods=['POST'])
def entity_compare():
    """
    读sqlite生成Excel
    :return:
    """
    # str = args
    list = [['版本', '用例总数', '识别正确', '识别错误', '正确率', '错误率']]
    result = request.form.get('version')
    result1 = result.encode("utf-8")
    strr = tuple(result1.split(","))
    workbook = Workbook()
    sheet = workbook.active
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "./db/evaluates.db")
    conn = sqlite3.connect(spath)
    c = conn.cursor()
    for i in strr:
        mysel = c.execute("select version, testcasecount, correct_count, error_count, correct_percent, error_percent "
                          "from entitys_analysis where version_finish_time = ('%s')" % (i))
        for data in mysel:
            last_two_digits = data[-2:]
            # 将数字采用四舍五入方法转换为两位小数
            one = round(last_two_digits[0], 2)
            two = round(last_two_digits[1], 2)
            # 创建一个新的tuple
            new_tuple = data[:-2] + (one, two) + data[-2:]
            list.append(new_tuple)
    # mysel = c.execute("select * from version where version in (%s)" % (strr,))
    # 数据库所有字段名
    # col_name_list = [tuple[0] for tuple in c.description]
    # list.append(col_name_list)
    print(list)
    list1 = []
    for i in range(len(list[0])):
        t = []
        for j in range(len(list)):
            t.append(list[j][i])
        list1.append(t)
    list1.insert(4, list1[0])

    # 1、把数据写入 sheet
    for row in list1:
        sheet.append(row)

    # 2、实例化一个 BarChart
    bar_chart = BarChart()
    bar_chart2 = BarChart()

    bar_chart.dLbls = openpyxl.chart.label.DataLabelList()
    bar_chart2.dLbls = openpyxl.chart.label.DataLabelList()
    if len(strr) <= 8:
        bar_chart.dLbls.showVal = True
        bar_chart2.dLbls.showVal = True
    else:
        bar_chart.dLbls.showVal = False
        bar_chart2.dLbls.showVal = False

    # 3、指定 BarChart 的数据范围，分类的范围，设置样式、标题等
    data = Reference(worksheet=sheet, min_row=1, max_row=4, min_col=2, max_col=len(strr)+1)
    x_axis = Reference(worksheet=sheet, min_col=1, min_row=2, max_row=4, max_col=1)
    bar_chart.add_data(data, titles_from_data=True)
    bar_chart.set_categories(x_axis)
    bar_chart.title = '效果测试数据'
    bar_chart.gapWidth = 50
    bar_chart.width = 20
    bar_chart.height = 10
    # bar_chart.y_axis.title = '横轴'
    # bar_chart.x_axis.title = '纵轴'
    # bar_chart.style = 10 #设置样式

    data1 = Reference(worksheet=sheet, min_row=5, max_row=7, min_col=2, max_col=len(strr) + 1)
    x_axis1 = Reference(worksheet=sheet, min_col=1, min_row=6, max_row=7, max_col=1)
    bar_chart2.add_data(data1, titles_from_data=True)
    bar_chart2.set_categories(x_axis1)
    bar_chart2.title = '效果指标'
    bar_chart2.gapWidth = 300
    bar_chart2.width = 18
    bar_chart2.height = 10

    # 设置柱状图位置
    sheet.add_chart(bar_chart, 'A11')
    sheet.add_chart(bar_chart2, 'N11')

    # 读输入版本数据库中准确率的最大值和最小值，相差值，并输出到Excel中
    hight = []
    low = []
    mysel1 = c.execute("select version, correct_percent from entitys_analysis where version_finish_time = ('%s')" % (strr[0]))
    for data in mysel1:
        hight.append(data)
    mysel2 = c.execute("select version, correct_percent from entitys_analysis where version_finish_time = ('%s')" % (strr[1]))
    for i in mysel2:
        low.append(i)
    print(hight)
    if float(hight[0][1]) > float(low[0][1]):
        # data = float(hight[0][1].strip('%')) - float(low[0][1].strip('%'))
        data = round(float(hight[0][1]) - float(low[0][1]), 2) * 100
        print(type(str(data)))
        res = "entity当前版本:" '%s' "，正确率为:" '%s' "，entity上一版本:" '%s' "，正确率为:" '%s'"，正确率上升了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    else:
        data =round(float(low[0][1]) - float(hight[0][1]), 2) * 100
        print(data)
        res = "entity当前版本:" '%s' "，正确率为:" '%s' "，entity上一版本:" '%s' "，正确率为:" '%s'"，正确率下降了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    sheet['A40'] = res
    # sheet.move_range('A10:B10', rows=16, cols=0)
    # 4、保存
    try:
        cur_path = os.getcwd()
        path = cur_path + '/com_reports/'
        if not os.path.exists(path):
            os.makedirs(path)
        str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        up_time = strftime(time.time())
        save_path = path + "entity-" + str_time + ".xlsx"
        workbook.save(save_path)
        new_report = TestReport(report_name=save_path.split('/')[-1],
                                upload_time=up_time,
                                file_path=save_path
                                )
        db.session.add(new_report)
        db.session.commit()
    except Exception as e:
        print(e)
        return {'code': 500, 'msg': '文件保存失败'}

    return {'code': 200, 'msg': '文件保存成功', 'path': save_path}
# 问答
@compares.route('/evaluate/faq_select', methods=['GET'])
def faq_select():
    """
    前端下拉列表展示
    :return:
    """
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "db/evaluates.db")
    conn = sqlite3.connect(spath)
    conn.row_factory = lambda cursor, row: row[0]
    c = conn.cursor()
    # mysel = c.execute("select version, finish_time from task where task_id = (select task_id from analysis)")
    mysel = c.execute("select version_finish_time from faq_analysis where version_finish_time IS NOT NULL")
    data = c.fetchall()
    select_result = []
    try:
        for i in data:
            data = i.encode("utf-8")
            if i != '':
                select_result.append(data)
                print(select_result)
        return jsonify(code=200, msg="ok", data=select_result)
    except Exception as e:
        return jsonify(code=500, msg="error")

@compares.route('/evaluate/faq_compare', methods=['POST'])
def faq_compare():
    """
    读sqlite生成Excel
    :return:
    """
    # str = args
    list = [['版本', '用例总数', '直接命中+澄清命中正确', '直接命中正确', '澄清命中', '有返回数据', '无返回数据',
             '召回率', '准确召回率（包含澄清）', '准确召回率（不含澄清）', '精确率（包含澄清）']]
    result = request.form.get('version')
    result1 = result.encode("utf-8")
    strr = tuple(result1.split(","))
    workbook = Workbook()
    sheet = workbook.active
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "./db/evaluates.db")
    conn = sqlite3.connect(spath)
    c = conn.cursor()
    for i in strr:
        mysel = c.execute("select version, testcasecount, hit_clarify_count,"
                          " hit_count, clarify_count, response_count,"
                          " no_response_count, call_percent,"
                          " correct_call_percent_include_clarify,"
                          " correct_call_percent_not_include_clarify, prec_percent_include_clarify"
                          " from faq_analysis where version_finish_time = ('%s')" % (i))
        for data in mysel:
            last_two_digits = data[-4:]
            # 将数字采用四舍五入方法转换为两位小数
            one = round(last_two_digits[0], 2)
            two = round(last_two_digits[1], 2)
            three = round(last_two_digits[2], 2)
            four = round(last_two_digits[3], 2)
            # 创建一个新的tuple
            new_tuple = data[:-4] + (one, two, three, four) + data[-4:]
            list.append(new_tuple)
    # mysel = c.execute("select * from version where version in (%s)" % (strr,))
    # 数据库所有字段名
    # col_name_list = [tuple[0] for tuple in c.description]
    # list.append(col_name_list)
    print(list)
    list1 = []
    for i in range(len(list[0])):
        t = []
        for j in range(len(list)):
            t.append(list[j][i])
        list1.append(t)
    list1.insert(7, list1[0])

    # 1、把数据写入 sheet
    for row in list1:
        sheet.append(row)

    # 2、实例化一个 BarChart
    bar_chart = BarChart()
    bar_chart2 = BarChart()

    bar_chart.dLbls = openpyxl.chart.label.DataLabelList()
    bar_chart2.dLbls = openpyxl.chart.label.DataLabelList()
    if len(strr) <= 8:
        bar_chart.dLbls.showVal = True
        bar_chart2.dLbls.showVal = True
    else:
        bar_chart.dLbls.showVal = False
        bar_chart2.dLbls.showVal = False

    # 3、指定 BarChart 的数据范围，分类的范围，设置样式、标题等
    data = Reference(worksheet=sheet, min_row=1, max_row=7, min_col=2, max_col=len(strr) + 1)
    x_axis = Reference(worksheet=sheet, min_col=1, min_row=2, max_row=7, max_col=1)
    bar_chart.add_data(data, titles_from_data=True)
    bar_chart.set_categories(x_axis)
    bar_chart.title = '效果测试数据'
    bar_chart.gapWidth = 50
    bar_chart.width = 20
    bar_chart.height = 10
    # bar_chart.y_axis.title = '横轴'
    # bar_chart.x_axis.title = '纵轴'
    # bar_chart.style = 10 #设置样式

    data1 = Reference(worksheet=sheet, min_row=8, max_row=12, min_col=2, max_col=len(strr) + 1)
    x_axis1 = Reference(worksheet=sheet, min_col=1, min_row=9, max_row=12, max_col=1)
    bar_chart2.add_data(data1, titles_from_data=True)
    bar_chart2.set_categories(x_axis1)
    bar_chart2.title = '效果指标'
    bar_chart2.gapWidth = 300
    bar_chart2.width = 18
    bar_chart2.height = 10

    # 设置柱状图位置
    sheet.add_chart(bar_chart, 'A18')
    sheet.add_chart(bar_chart2, 'N18')

    # 读输入版本数据库中准确率的最大值和最小值，相差值，并输出到Excel中
    hight = []
    low = []
    mysel1 = c.execute(
        "select version, correct_call_percent_include_clarify,"
        " correct_call_percent_not_include_clarify from faq_analysis where version_finish_time = ('%s')" % (
        strr[0]))
    for data in mysel1:
        hight.append(data)
    mysel2 = c.execute(
        "select version, correct_call_percent_include_clarify,"
        " correct_call_percent_not_include_clarify from faq_analysis where version_finish_time = ('%s')" % (
        strr[1]))
    for i in mysel2:
        low.append(i)
    print(hight)
    if float(hight[0][1]) > float(low[0][1]):
        # data = float(hight[0][1].strip('%')) - float(low[0][1].strip('%'))
        data = round(float(hight[0][1]) - float(low[0][1]), 2) * 100
        print(type(str(data)))
        res = "faq当前版本:" '%s' "，准确召回率(包含澄清)为:" '%s' "，faq上一版本:" '%s' "，准确召回率(包含澄清)为:" '%s'"，准确召回率(包含澄清)上升了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    else:
        data = round(float(low[0][1]) - float(hight[0][1]), 2) * 100
        print(data)
        res = "faq当前版本:" '%s' "，准确召回率(包含澄清)为:" '%s' "，faq上一版本:" '%s' "，准确召回率(包含澄清)为:" '%s'"，准确召回率(包含澄清)下降了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    sheet['A35'] = res
    if float(hight[0][2]) > float(low[0][2]):
        # data = float(hight[0][1].strip('%')) - float(low[0][1].strip('%'))
        data = round(float(hight[0][2]) - float(low[0][2]), 2) * 100
        print(type(str(data)))
        res2 = "当前版本:" '%s' "，准确召回率(不含澄清)为:" '%s' "，上一版本:" '%s' "，准确召回率(不含澄清)为:" '%s'"，准确召回率(不含澄清)上升了:" '%s' "个百分点" \
               % (hight[0][0], round(hight[0][2], 2), low[0][0], round(low[0][2], 2), data)
    else:
        data = round(float(low[0][2]) - float(hight[0][2]), 2) * 100
        print(data)
        res2 = "当前版本:" '%s' "，准确召回率(不含澄清)为:" '%s' "，上一版本:" '%s' "，准确召回率(不含澄清)为:" '%s'"，准确召回率(不含澄清)下降了:" '%s' "个百分点" \
               % (hight[0][0], round(hight[0][2], 2), low[0][0], round(low[0][2], 2), data)
    sheet['A40'] = res2
    try:
        cur_path = os.getcwd()
        path = cur_path + '/com_reports/'
        if not os.path.exists(path):
            os.makedirs(path)
        str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        up_time = strftime(time.time())
        save_path = path + "faq-" + str_time + ".xlsx"
        workbook.save(save_path)
        new_report = TestReport(report_name=save_path.split('/')[-1],
                                upload_time=up_time,
                                file_path=save_path
                                )
        db.session.add(new_report)
        db.session.commit()
    except Exception as e:
        print(e)
        return {'code': 500, 'msg': '文件保存失败'}

    return {'code': 200, 'msg': '文件保存成功', 'path': save_path}

# 态度
@compares.route('/evaluate/attitude_select', methods=['GET'])
def attitude_select():
    """
    前端下拉列表展示
    :return:
    """
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "db/evaluates.db")
    conn = sqlite3.connect(spath)
    conn.row_factory = lambda cursor, row: row[0]
    c = conn.cursor()
    # mysel = c.execute("select version, finish_time from task where task_id = (select task_id from analysis)")
    mysel = c.execute("select version_finish_time from attitude_analysis where version_finish_time IS NOT NULL")
    data = c.fetchall()
    select_result = []
    try:
        for i in data:
            data = i.encode("utf-8")
            if i != '':
                select_result.append(data)
                print(select_result)
        return jsonify(code=200, msg="ok", data=select_result)
    except Exception as e:
        return jsonify(code=500, msg="error")

@compares.route('/evaluate/attitude_compare', methods=['POST'])
def attitude_compare():
    """
    读sqlite生成Excel
    :return:
    """
    # str = args
    list = [['版本', '用例总数', '识别正确', '识别错误', '正确率', '错误率']]
    result = request.form.get('version')
    result1 = result.encode("utf-8")
    strr = tuple(result1.split(","))
    workbook = Workbook()
    sheet = workbook.active
    spath = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    spath = os.path.join(spath, "./db/evaluates.db")
    conn = sqlite3.connect(spath)
    c = conn.cursor()
    for i in strr:
        mysel = c.execute(
            "select version, testcasecount, correct_count, error_count, correct_percent, error_percent "
            "from attitude_analysis where version_finish_time = ('%s')" % (i))
        for data in mysel:
            last_two_digits = data[-2:]
            # 将数字采用四舍五入方法转换为两位小数
            one = round(last_two_digits[0], 2)
            two = round(last_two_digits[1], 2)
            # 创建一个新的tuple
            new_tuple = data[:-2] + (one, two) + data[-2:]
            list.append(new_tuple)
    # mysel = c.execute("select * from version where version in (%s)" % (strr,))
    # 数据库所有字段名
    # col_name_list = [tuple[0] for tuple in c.description]
    # list.append(col_name_list)
    print(list)
    list1 = []
    for i in range(len(list[0])):
        t = []
        for j in range(len(list)):
            t.append(list[j][i])
        list1.append(t)
    list1.insert(4, list1[0])

    # 1、把数据写入 sheet
    for row in list1:
        sheet.append(row)

    # 2、实例化一个 BarChart
    bar_chart = BarChart()
    bar_chart2 = BarChart()

    bar_chart.dLbls = openpyxl.chart.label.DataLabelList()
    bar_chart2.dLbls = openpyxl.chart.label.DataLabelList()
    if len(strr) <= 8:
        bar_chart.dLbls.showVal = True
        bar_chart2.dLbls.showVal = True
    else:
        bar_chart.dLbls.showVal = False
        bar_chart2.dLbls.showVal = False

    # 3、指定 BarChart 的数据范围，分类的范围，设置样式、标题等
    data = Reference(worksheet=sheet, min_row=1, max_row=4, min_col=2, max_col=len(strr) + 1)
    x_axis = Reference(worksheet=sheet, min_col=1, min_row=2, max_row=4, max_col=1)
    bar_chart.add_data(data, titles_from_data=True)
    bar_chart.set_categories(x_axis)
    bar_chart.title = '效果测试数据'
    bar_chart.gapWidth = 50
    bar_chart.width = 20
    bar_chart.height = 10
    # bar_chart.y_axis.title = '横轴'
    # bar_chart.x_axis.title = '纵轴'
    # bar_chart.style = 10 #设置样式

    data1 = Reference(worksheet=sheet, min_row=5, max_row=7, min_col=2, max_col=len(strr) + 1)
    x_axis1 = Reference(worksheet=sheet, min_col=1, min_row=6, max_row=7, max_col=1)
    bar_chart2.add_data(data1, titles_from_data=True)
    bar_chart2.set_categories(x_axis1)
    bar_chart2.title = '效果指标'
    bar_chart2.gapWidth = 300
    bar_chart2.width = 18
    bar_chart2.height = 10

    # 设置柱状图位置
    sheet.add_chart(bar_chart, 'A11')
    sheet.add_chart(bar_chart2, 'N11')

    # 读输入版本数据库中准确率的最大值和最小值，相差值，并输出到Excel中
    hight = []
    low = []
    mysel1 = c.execute(
        "select version, correct_percent"
        " from attitude_analysis where version_finish_time = ('%s')" % (strr[0]))
    for data in mysel1:
        hight.append(data)
    mysel2 = c.execute(
        "select version, correct_percent"
        " from attitude_analysis where version_finish_time = ('%s')" % (strr[1]))
    for i in mysel2:
        low.append(i)
    print(hight)
    if float(hight[0][1]) > float(low[0][1]):
        # data = float(hight[0][1].strip('%')) - float(low[0][1].strip('%'))
        data = round(float(hight[0][1]) - float(low[0][1]), 2) * 100
        print(type(str(data)))
        res = "attitude当前版本:" '%s' "，正确率为:" '%s' "，attitude上一版本:" '%s' "，正确率为:" '%s'"，正确率上升了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    else:
        data = round(float(low[0][1]) - float(hight[0][1]), 2) * 100
        print(data)
        res = "attitude当前版本:" '%s' "，正确率为:" '%s' "，attitude上一版本:" '%s' "，正确率为:" '%s'"，正确率下降了:" '%s' "个百分点" \
              % (hight[0][0], round(hight[0][1], 2), low[0][0], round(low[0][1], 2), data)
    sheet['A40'] = res
    # sheet.move_range('A10:B10', rows=16, cols=0)
    # 4、保存
    try:
        cur_path = os.getcwd()
        path = cur_path + '/com_reports/'
        if not os.path.exists(path):
            os.makedirs(path)
        str_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        up_time = strftime(time.time())
        save_path = path + "attitude-" + str_time + ".xlsx"
        workbook.save(save_path)
        new_report = TestReport(report_name=save_path.split('/')[-1],
                                upload_time=up_time,
                                file_path=save_path
                                )
        db.session.add(new_report)
        db.session.commit()
    except Exception as e:
        print(e)
        return {'code': 500, 'msg': '文件保存失败'}

    return {'code': 200, 'msg': '文件保存成功', 'path': save_path}

