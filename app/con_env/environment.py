# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2023/1/17 21:00:00
"""
import os
from string import capitalize

from flask import request, Blueprint
import time
from sqlalchemy import and_

from ..models.exts import get_db
from ..models.models import Env

global db
db = get_db()
environment = Blueprint('environment', __name__)
@environment.route('/evaluate/envdelete', methods=['POST'])
def env_delete():
    """
    删除数据库对象
    """
    name = request.form.get('env_name')
    try:
        Env.query.filter_by(env_name=name).delete()
        db.session.commit()
    except Exception as e:
        print(e)
    return {'code': 200, 'msg': '配置信息删除成功'}


@environment.route('/evaluate/envlist', methods=['GET'])
def env_list():
    """
        获取query列表，返回列表
    """
    res1 = []

    try:
        # 查询数据库
        envs = Env.query.order_by(Env.id.desc()).all()
        for env in envs:
            res1.append({
                "env_name": env.env_name,
                "version": env.version,
                "backend_address": env.backend_address,
                "core_address": env.core_address,
                "knowledge_address": env.knowledge_address,
                "username": env.username,
                "uid": env.uid,
                "account_type": env.account_type,
                "agentId": env.agentId,
                "tenantId": env.tenantId,
                "no_answer": env.no_answer,
                "large_model_no_answer": env.large_model_no_answer,
                "clarify_answer": env.clarify_answer,
                "bot_name": env.bot_name,
                "bot_id": env.bot_id,
                "botToken": env.botToken,
                "authorization": env.authorization,
                "nlu_enable": env.nlu_enable,
                "thread_num": env.thread_num,
                "user_name": env.user_name
            })
    except Exception as e:
        print(e)
        db.session.rollback()
    return {'code': 200, 'data': res1}

@environment.route('/evaluate/envedit', methods=['POST'])
def env_edit():
    """
    编辑数据库对象
    """
    name = request.form.get('env_sign')
    env_name = request.form.get('env_name')
    version = request.form.get('version')
    backend_address = request.form.get('backend_address')
    core_address = request.form.get('core_address')
    knowledge_address = request.form.get('knowledge_address')
    username = request.form.get('username')
    uid = request.form.get('uid')
    account_type = request.form.get('account_type')
    agentId = request.form.get('agentId')
    tenantId = request.form.get('tenantId')
    botToken = request.form.get('botToken')
    thread_num = request.form.get('thread_num')
    bot_name = request.form.get('bot_name')
    no_answer = request.form.get('no_answer')
    large_model_no_answer = request.form.get('large_model_no_answer')
    authorization = request.form.get('authorization')
    nlu_enable = request.form.get('nlu_enable')
    clarify_answer = request.form.get('clarify_answer')
    user_name = request.form.get('user_name')
    if capitalize(str(nlu_enable)) == 'True':
        nlu_enable = True
    else:
        nlu_enable = False
    try:
        envs = Env.query.all()
        for i in envs:
            if (i.env_name == env_name and i.env_name != name):
                return {'code': 201, 'msg': '环境名称已存在,请更改环境名称', 'status': 1}
        env = Env.query.filter_by(env_name=name).first()
        env.env_name = env_name
        env.version = version
        env.backend_address = backend_address
        env.core_address = core_address
        env.knowledge_address = knowledge_address
        env.username = username
        env.uid = uid
        env.account_type = account_type
        env.agentId = agentId
        env.tenantId = tenantId
        env.no_answer = no_answer
        env.large_model_no_answer = large_model_no_answer
        env.clarify_answer = clarify_answer
        env.bot_name = bot_name
        env.botToken = botToken
        env.authorization = authorization
        env.nlu_enable = nlu_enable
        env.thread_num = thread_num
        env.user_name = user_name
        db.session.commit()
    except Exception as e:
        print(e)
    return {'code': 200, 'msg': '配置信息编辑成功'}