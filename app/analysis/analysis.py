#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2022/11/10 15:39:00
"""
import os
import uuid

from flask import request, send_file, jsonify

from app.analysis import analysis
import hashlib
import logging
import traceback

from app.analysis.report_analysis import report_compare_diff_by_tqa_v2, report_compare_diff_by_intent_v2, \
    report_compare_diff_by_entity_v2, report_compare_diff_by_attitude_v2, report_compare_diff_by_faq_v2, \
    report_compare_diff_by_end2end_in_v2
import sys

reload(sys)
sys.setdefaultencoding('utf8')

# 日志相关
logger = logging.getLogger("report")
logger.setLevel(logging.DEBUG)
ch = logging.StreamHandler()
ch.setLevel(logging.DEBUG)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(lineno)d - %(message)s")
ch.setFormatter(formatter)
logger.addHandler(ch)


# db = './app/models/env.db'


def get_file_md5(file):
    """
    获取文件md5编码
    """
    f_md5 = hashlib.md5(file.read())
    return f_md5.hexdigest()


@analysis.route('upload', methods=['POST'])
def upload_report():
    """
    上传文件
    :return:
    """
    file = request.files['file']
    print(type(file))
    print(file)
    # TODO 判断文件是否已经存在，根据md5码
    md5 = get_file_md5(file)
    # 判断目录是否存在，不存在则创建
    path = './app/models/result/'
    if not os.path.exists(path):
        os.makedirs(path)
    file.save(path + file.filename)
    return {'code': 200, 'msg': '文件上传成功'}


@analysis.route('contrast', methods=['POST'])
def contrast():
    """
    对比效果文件
    :return:
    """
    old_file = request.files.get('old_file')
    new_file = request.files.get('new_file')
    file_type = request.form.get('fileType')
    if old_file is None or new_file is None or file_type is None:
        return {'code': 400, 'msg': '请上传文件'}, 400

    if not old_file.filename.endswith('.xlsx') or not new_file.filename.endswith('.xlsx'):
        return {'code': 400, 'msg': '请上传xlsx类型的文件'}, 400
    try:
        # /Users/<USER>/Desktop/xiaoguohd/baidu/kefu-qa/auto-evaluate
        cur_path = os.getcwd()
        # print cur_path
        path = cur_path + '/reports/'
        if not os.path.exists(path):
            os.makedirs(path)
        old_file_uuid = str(uuid.uuid4())
        old_file_path = path + old_file_uuid + '/'
        os.makedirs(old_file_path)
        old_file_all = (old_file_path + old_file.filename).encode('utf-8')
        old_file.save(old_file_all)

        new_file_uuid = str(uuid.uuid4())
        new_file_path = path + new_file_uuid + '/'
        os.makedirs(new_file_path)
        new_file_all = (new_file_path + new_file.filename).encode('utf-8')
        new_file.save(new_file_all)
        logger.info("old_file_all:" + old_file_all)
        logger.info("new_file_all:" + new_file_all)
        logger.info("path:" + path)

        if file_type == 'tqa':
            res_path = report_compare_diff_by_tqa_v2(old_file_all, new_file_all, path)
        if file_type == 'intent':
            res_path = report_compare_diff_by_intent_v2(old_file_all, new_file_all, path)
        if file_type == 'entity':
            res_path = report_compare_diff_by_entity_v2(old_file_all, new_file_all, path)
        if file_type == 'attitude':
            res_path = report_compare_diff_by_attitude_v2(old_file_all, new_file_all, path)
        if file_type == 'faq':
            res_path = report_compare_diff_by_faq_v2(old_file_all, new_file_all, path)
        if file_type == 'end2end_in':
            res_path = report_compare_diff_by_end2end_in_v2(old_file, new_file, path)

    except Exception as e:
        s = traceback.format_exc()
        logging.error(s)
        return {'code': 500, 'msg': '文件保存失败'}, 500

    if res_path == "ERROR":
        return {'code': 500, 'msg': '文件保存失败'}, 500
    logger.info("保存文件成功，文件地址为:" + res_path)
    return {'code': 200, 'msg': '文件保存成功',
            'data': {'old_file': old_file_all, 'new_file': new_file_all, 'res_path': res_path}
            }, 200


@analysis.route('/diffOnline', methods=["GET", "POST"])
def diff_online():
    """
    在线对比效果文件
    :return:
    """

    try:
        data_form = request.form
        logger.info("参数：" + str(data_form))
        new_file = str(data_form.get('newFileDir')).encode('utf-8')
        old_file = str(data_form.get('oldFileDir')).encode('utf-8')
        # new_file = data_form.get('newFileDir')
        # old_file = data_form.get('oldFileDir')
        engine = data_form.get('engine')
        cur_path = os.getcwd()
        path = cur_path + '/reports/'
        if not os.path.exists(path):
            os.makedirs(path)

        if engine == 'tableqa_rounds':
            res_path = report_compare_diff_by_tqa_v2(old_file, new_file, path)
        if engine == 'intents' or engine == 'sys_intents':
            res_path = report_compare_diff_by_intent_v2(old_file, new_file, path)
        if engine == 'sys_entity':
            res_path = report_compare_diff_by_entity_v2(old_file, new_file, path)
        if engine == 'attitude':
            res_path = report_compare_diff_by_attitude_v2(old_file, new_file, path)
        if engine == 'faq':
            res_path = report_compare_diff_by_faq_v2(old_file, new_file, path)
        if engine == 'end2end_in':
            res_path = report_compare_diff_by_end2end_in_v2(old_file, new_file, path)

    except Exception as e:
        s = traceback.format_exc()
        logging.error(s)
        return {'code': 500, 'msg': '文件保存失败'}, 500

    if res_path == "ERROR":
        return {'code': 500, 'msg': '文件保存失败'}, 500
    logger.info("保存文件成功，文件地址为:" + res_path)
    return {'code': 200, 'msg': '文件保存成功',
            'data': {'old_file': old_file, 'new_file': new_file, 'res_path': res_path}
            }, 200


@analysis.route('/download', methods=["GET", "POST"])
def download_file():
    """
    效果对比文件下载
    :return:
    """

    # 获取文件名
    print "downloadReport"
    file_path = str(request.args.get('fileName')).encode('utf-8')
    file_name = str(file_path).split('/')[-1].encode('utf-8')

    print str(file_path)
    if file_path:
        # response = send_file(file_path, as_attachment=True)
        # response.headers["Access-Control-Expose-Headers"] = "Content-disposition"
        # print type(response)
        # 判断文件存不存在
        if os.path.exists(file_path):
            response = send_file(file_path, as_attachment=True, mimetype='application/octet-stream')
            response.headers['Content-Disposition'] = 'attachment;filename=' + file_name
            return response, 200
        else:
            data = {
                "code": 400,
                "msg": "抱歉，文件未找到!"
            }
            return jsonify(data), 200
    else:
        data = {
            "code": 400,
            "msg": "抱歉，文件未找到!"
        }
        return jsonify(data), 200
