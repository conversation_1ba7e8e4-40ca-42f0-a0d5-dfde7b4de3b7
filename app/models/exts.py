#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
创建数据库db
"""

from flask import g, current_app
from flask_sqlalchemy import SQLAlchemy
import os, sys

dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(dir)

# from flask_sqlalchemy import SQLAlchemy
# db = SQLAlchemy()

db = None

def get_db():
    """

    Returns:

    """
    global db
    if not db:
        db = SQLAlchemy(current_app)
        # print current_app.config
        # print db
    return db
