{"agentType": 1, "sign": "454deafa00243e53fb812f5eb129f1f6", "exportData": {"dialog": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "dialogNodes": [], "dialogProcess": []}, "chitchat": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "chitchat": []}, "bot": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "bot": []}, "faq": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "faq": [], "faqNoResponse": []}, "skill": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "skill": [], "skillNoResponses": []}, "basicResources": {"voca": [], "agent": {"id": "d35534fc-cddb-4309-93ea-425f1d005009", "name": "tableqa", "description": "", "isPublic": 0, "developerAccessToken": "37ef81f2-f8dc-44cf-b204-e8e7da065e2f", "createdUserId": "*********", "createdUsername": "ok千层锦", "lastEditUserId": "*********", "lastEditUsername": "ok千层锦", "industry": "ed0165dc-d0eb-46d4-adf7-4c26b05db167", "type": 1, "status": 0, "online": 1, "delete": 0, "created": 1626077883000, "updated": 1626077885000, "kgProjectId": "", "kgToken": "", "auditLevel": 3, "effect": 0, "language": "zh", "requestFrom": "NGD", "engine": null, "toolResource": null, "qps": null, "settingVersion": null}, "webhook": null, "channelManage": {"channelDimension": [], "channelSpecialValue": [], "channelDict": {}}, "thirdEngine": {"thirdEngineConfList": [], "thirdEngineNoResponseList": []}, "settingAgent": {"_effect": 0, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "a9d7ad5b-3d18-4f12-9223-41bfdaabc4e1", "name": "tableqa", "description": "", "config": "{\"engine\": {\"kg\": 1, \"faq\": 0, \"task\": 0, \"tableQA\": 0, \"chitchat\": 0}, \"confidence\": {\"faqThreshold\": 0.8, \"queryCorrect\": {\"enable\": false}, \"chatThreshold\": 0.85, \"tableQaThreshold\": 0.8, \"faqClarifyThreshold\": 0.6, \"intentModelThreshold\": 0.8, \"intentClarifyThreshold\": 0.6, \"intentExampleThreshold\": 0.9, \"tableQaClarifyThreshold\": 0.6, \"intentMinDifferenceThreshold\": 0}, \"queryCorrect\": {\"enable\": false}, \"toolResource\": {\"skill\": 1}, \"multiIntentRec\": {\"enable\": false}, \"contextUnderstand\": {\"enable\": false}}", "version": 1, "created": 1626077883000, "updated": 1626077883000, "limitConfig": null, "language": "zh", "labelNoticeSetting": null, "systemResource": null}, "sensitive": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "repositories": [], "words": []}, "dirList": [], "dialogVariable": [], "instructionManage": {"instruction": [], "instructionVariables": []}}, "intent": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "importData": {"NO_RESP": [], "USER_INTENT": [], "SYS_INTENT": [{"id": "sys_intent_8"}, {"id": "sys_intent_14"}, {"id": "sys_intent_5"}, {"id": "sys_intent_9"}, {"id": "sys_intent_17"}, {"id": "sys_intent_6"}, {"id": "sys_intent_7"}, {"id": "sys_intent_12"}, {"id": "sys_intent_16"}, {"id": "sys_intent_15"}, {"id": "sys_intent_13"}, {"id": "sys_intent_10"}, {"id": "sys_intent_4"}, {"id": "sys_intent_11"}], "EXAMPLE": [], "TEMPLATE": []}}, "version": "v6.7", "entity": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "entity": []}, "tableQA": {"categories": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "b035ef50-082f-4468-9b2a-326935f7a30d", "categoryName": "航空", "description": null, "version": 1, "created": "2021-07-12 16:18:04", "updated": "2021-07-12 16:18:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "interrupted": false}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "categoryName": "汽车", "description": null, "version": 1, "created": "2021-07-12 16:18:29", "updated": "2021-07-12 16:18:29", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "interrupted": true}], "categorySkillRels": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "a9377067-435f-4f94-a416-7753b70fb9b9", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "skillId": 2129, "skillName": "NGD_航空_lqp8o_kzvvf", "offlineResourceIds": "", "onlineResourceIds": "", "created": "2021-07-12 16:18:04", "updated": "2021-07-12 16:18:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "e112eb78-0f7c-447e-afb9-5b87f9085521", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "skillId": 2145, "skillName": "NGD_汽车_vi6w4", "offlineResourceIds": "", "onlineResourceIds": "", "created": "2021-07-12 16:18:29", "updated": "2021-07-12 16:18:29", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null}], "properties": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "eda7169a-b081-4a24-83ac-f280c65af9b7", "propertyName": "上市日期", "propertyEnName": "Listingdate", "propertyType": "date", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "d6bc5a7c-ca42-431d-92b6-01a15b86d55c", "propertyName": "价格", "propertyEnName": "price", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "928958de-803f-49b0-b037-83256bfc6946", "propertyName": "全景天窗", "propertyEnName": "skylight", "propertyType": "bool", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "dedd0eef-b411-4afb-84a3-0991b1f01ca1", "propertyName": "出发楼层", "propertyEnName": "leavefloor", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "500f4497-5600-4427-8b5f-75dda86a9e41", "propertyName": "出发航站楼", "propertyEnName": "leavebuilding", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "00841368-3987-482c-bc79-435a4eb081a2", "propertyName": "到达楼层", "propertyEnName": "arrivefloor", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "83b4392c-2b38-4dd6-83da-da15a85d0271", "propertyName": "到达航站楼", "propertyEnName": "arrivebuilding", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22", "propertyName": "厂商", "propertyEnName": "com<PERSON>y", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "3d840bf3-5924-4e0d-8e23-830d6d1e6d91", "propertyName": "变速箱", "propertyEnName": "Gearbox", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "b8613701-9ae4-409c-bbc2-19d427d2cf61", "propertyName": "吞吐量", "propertyEnName": "amount", "propertyType": "num", "synonym": "", "reply": "|name|有|amount|亿人次", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "fe79ea7e-f46c-4c39-97a7-94927aa10055", "propertyName": "启用日期", "propertyEnName": "date", "propertyType": "date", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "e8b89ac9-7842-4231-bfeb-a12dedafd463", "propertyName": "国别", "propertyEnName": "country", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "84655a5a-4083-4e9a-afd4-97584762df8c", "propertyName": "座位数", "propertyEnName": "seat", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "196f077b-addf-4ea3-9736-f23d8fed08d4", "propertyName": "所在城市", "propertyEnName": "city", "propertyType": "string", "synonym": "城市", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": ["城市"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "7f377b5f-0984-4096-bbaa-87d3857bb6e7", "propertyName": "排放", "propertyEnName": "emission", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "0cdbbe84-3846-408a-92c4-989e0d00ab15", "propertyName": "排量", "propertyEnName": "pailiang", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a", "propertyName": "是否可以使用电子登机牌", "propertyEnName": "eleboardpass", "propertyType": "bool", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "ffbef38e-2dd8-4c95-97df-80b601d77e0c", "propertyName": "服务区域", "propertyEnName": "area", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "c431d5dd-6dda-4ae4-90c9-781916664561", "propertyName": "机场安保人数", "propertyEnName": "worker", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1", "propertyName": "汽车", "propertyEnName": "car", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "3243209f-b21a-483b-b5c7-7f64b7b490bc", "propertyName": "状态", "propertyEnName": "status", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "dadb0c8d-6a2c-4567-8547-b756893f90fe", "propertyName": "电子商务柜台", "propertyEnName": "businesscounter", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "cf3278a6-d94f-456b-9213-8d4330f6b2cd", "propertyName": "税费", "propertyEnName": "Taxes", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3", "propertyName": "级别", "propertyEnName": "level", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8", "propertyName": "能源", "propertyEnName": "energy", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "7f1e2f21-555f-415c-9078-affbc1e34e59", "propertyName": "贵宾休息室", "propertyEnName": "VIProom", "propertyType": "bool", "synonym": "贵宾区", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": ["贵宾区"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "b2415e83-fd4c-4afd-9ad2-aa2621638aac", "propertyName": "车身", "propertyEnName": "body", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "f10705f3-29a2-4bfe-ad3a-cff811cf363e", "propertyName": "飞行区等级", "propertyEnName": "rank", "propertyType": "num", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "46669de0-c895-4610-978b-5b204144623a", "propertyName": "驱动", "propertyEnName": "driver", "propertyType": "string", "synonym": "", "reply": "", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "synonymList": []}], "synonyms": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "339a6495-935d-48c5-bc01-53d2a1532314", "normalizationAlias": "多少钱", "normalization": "价格", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "normalizationAliasList": ["多少钱"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "1db029eb-bf5c-4862-8407-ad943ac95717", "normalizationAlias": "大兴机场", "normalization": "北京大兴机场", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "normalizationAliasList": ["大兴机场"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "bdacf01c-2c3a-4a91-a73b-6edbea59c3c3", "normalizationAlias": "津沽", "normalization": "天津", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "normalizationAliasList": ["津沽"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "2510bf99-a118-483f-9ac5-773846497357", "normalizationAlias": "pasta", "normalization": "帕萨特", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "normalizationAliasList": ["pasta"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "211a2291-7ef5-40fb-80b8-8ea9eb00dacf", "normalizationAlias": "吞吐量", "normalization": "服务人数", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "normalizationAliasList": ["吞吐量"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "0044167b-89e8-46c2-b46c-dd764e1a6104", "normalizationAlias": "覆盖区域", "normalization": "服务区域", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "normalizationAliasList": ["覆盖区域"]}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "c0b2e7f8-b411-4ba8-b076-48ee404a77c8", "normalizationAlias": "电子商务柜台", "normalization": "电子柜台", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "normalizationAliasList": ["电子商务柜台"]}], "tables": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableName": "车", "tableEnName": "type", "tableAlias": "alias", "description": null, "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["alias"], "categoryName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableName": "机场", "tableEnName": "airport", "tableAlias": "空港", "description": null, "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["空港"], "categoryName": null}], "tableSynonymRelations": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "08d72ccc-0230-4f54-8e57-ec647717646d", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableName": null, "tableEnName": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "synonymId": "2510bf99-a118-483f-9ac5-773846497357", "normalization": "帕萨特", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "1b84241a-48ff-438e-ae19-47387616d228", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableName": null, "tableEnName": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "synonymId": "1db029eb-bf5c-4862-8407-ad943ac95717", "normalization": "北京大兴机场", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "2315f341-dfcf-446d-8db0-bb08ab49983e", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableName": null, "tableEnName": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "synonymId": "c0b2e7f8-b411-4ba8-b076-48ee404a77c8", "normalization": "电子柜台", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "402f7e8c-7754-4059-b175-be83b405fbf8", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableName": null, "tableEnName": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "synonymId": "339a6495-935d-48c5-bc01-53d2a1532314", "normalization": "价格", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "d76d3f4f-febe-45fe-8f3b-ad7226a2758b", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableName": null, "tableEnName": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "synonymId": "bdacf01c-2c3a-4a91-a73b-6edbea59c3c3", "normalization": "天津", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "ef13c9c8-e318-44aa-9143-95696f1d64e5", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableName": null, "tableEnName": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "synonymId": "1db029eb-bf5c-4862-8407-ad943ac95717", "normalization": "北京大兴机场", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "f7bb601b-3219-424f-a8be-d5f284186357", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableName": null, "tableEnName": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "synonymId": "211a2291-7ef5-40fb-80b8-8ea9eb00dacf", "normalization": "服务人数", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "fac1a4bb-9a6f-417a-8365-52f2bbac9ac5", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableName": null, "tableEnName": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "synonymId": "0044167b-89e8-46c2-b46c-dd764e1a6104", "normalization": "服务区域", "normalizationAlias": "", "version": null, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": []}], "tablePropertyRelations": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "07f02473-0a02-4fa5-859a-9df627f7e3dd", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "fe79ea7e-f46c-4c39-97a7-94927aa10055", "sort": 14, "tableName": null, "tableEnName": null, "propertyName": "启用日期", "propertyEnName": "date", "propertyType": "date", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "0a9867f0-13f4-4619-a67c-0eae11076c9d", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "928958de-803f-49b0-b037-83256bfc6946", "sort": 14, "tableName": null, "tableEnName": null, "propertyName": "全景天窗", "propertyEnName": "skylight", "propertyType": "bool", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "1240e31a-c336-4f6a-881e-733cc3d910af", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "7f377b5f-0984-4096-bbaa-87d3857bb6e7", "sort": 12, "tableName": null, "tableEnName": null, "propertyName": "排放", "propertyEnName": "emission", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "1b0c75e1-e4db-44b1-9d7f-c5941d1c45ab", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "196f077b-addf-4ea3-9736-f23d8fed08d4", "sort": 1, "tableName": null, "tableEnName": null, "propertyName": "所在城市", "propertyEnName": "city", "propertyType": "string", "synonym": "城市", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "1fce9a50-660c-458b-900c-d103bbb9dac6", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "cf3278a6-d94f-456b-9213-8d4330f6b2cd", "sort": 3, "tableName": null, "tableEnName": null, "propertyName": "税费", "propertyEnName": "Taxes", "propertyType": "num", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "245ac2a4-6984-42d3-8b7d-b40a791cda2a", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "0cdbbe84-3846-408a-92c4-989e0d00ab15", "sort": 10, "tableName": null, "tableEnName": null, "propertyName": "排量", "propertyEnName": "pailiang", "propertyType": "num", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "320663a6-c136-4e7d-84d0-4af3e67da1e2", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "b8613701-9ae4-409c-bbc2-19d427d2cf61", "sort": 13, "tableName": null, "tableEnName": null, "propertyName": "吞吐量", "propertyEnName": "amount", "propertyType": "num", "synonym": "", "reply": "|name|有|amount|亿人次", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "32c192ea-70c7-441f-b251-832e93aa51c0", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "e8b89ac9-7842-4231-bfeb-a12dedafd463", "sort": 8, "tableName": null, "tableEnName": null, "propertyName": "国别", "propertyEnName": "country", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "46675fc3-ded0-4704-b01f-9cbfd043d637", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8", "sort": 6, "tableName": null, "tableEnName": null, "propertyName": "能源", "propertyEnName": "energy", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "4894a725-b6c4-4f9d-a2b1-7021ef880eba", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "00841368-3987-482c-bc79-435a4eb081a2", "sort": 5, "tableName": null, "tableEnName": null, "propertyName": "到达楼层", "propertyEnName": "arrivefloor", "propertyType": "num", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "4afd72d9-6332-4874-9cf0-3f9fd7975409", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "7f1e2f21-555f-415c-9078-affbc1e34e59", "sort": 6, "tableName": null, "tableEnName": null, "propertyName": "贵宾休息室", "propertyEnName": "VIProom", "propertyType": "bool", "synonym": "贵宾区", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "710ff0e4-bc43-4455-8246-f4e37e437e20", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "84655a5a-4083-4e9a-afd4-97584762df8c", "sort": 13, "tableName": null, "tableEnName": null, "propertyName": "座位数", "propertyEnName": "seat", "propertyType": "num", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "75286f38-355a-4b04-bb3a-32e7a862e9c3", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "3d840bf3-5924-4e0d-8e23-830d6d1e6d91", "sort": 9, "tableName": null, "tableEnName": null, "propertyName": "变速箱", "propertyEnName": "Gearbox", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "75dc4635-f15d-4f57-88b6-59c33c18530c", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "83b4392c-2b38-4dd6-83da-da15a85d0271", "sort": 4, "tableName": null, "tableEnName": null, "propertyName": "到达航站楼", "propertyEnName": "arrivebuilding", "propertyType": "string", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "79613c64-1fbd-420b-9209-c55836cb7f71", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "b2415e83-fd4c-4afd-9ad2-aa2621638aac", "sort": 5, "tableName": null, "tableEnName": null, "propertyName": "车身", "propertyEnName": "body", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "892591d0-3b94-4fed-847f-270b5d181e2f", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "46669de0-c895-4610-978b-5b204144623a", "sort": 11, "tableName": null, "tableEnName": null, "propertyName": "驱动", "propertyEnName": "driver", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "94cc10a8-0164-4bf8-af7f-6dc10fb25aa3", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "ffbef38e-2dd8-4c95-97df-80b601d77e0c", "sort": 9, "tableName": null, "tableEnName": null, "propertyName": "服务区域", "propertyEnName": "area", "propertyType": "string", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "a0904a4e-a212-44d9-a77a-889713d0b176", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3", "sort": 4, "tableName": null, "tableEnName": null, "propertyName": "级别", "propertyEnName": "level", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "a0907593-8e23-44be-a6dc-87d4ec8aad6e", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "500f4497-5600-4427-8b5f-75dda86a9e41", "sort": 2, "tableName": null, "tableEnName": null, "propertyName": "出发航站楼", "propertyEnName": "leavebuilding", "propertyType": "string", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "a20f8abd-114b-426e-80f9-2bf482714804", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "f10705f3-29a2-4bfe-ad3a-cff811cf363e", "sort": 8, "tableName": null, "tableEnName": null, "propertyName": "飞行区等级", "propertyEnName": "rank", "propertyType": "num", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "abaa7fa3-544a-4915-8aba-41142122cd62", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22", "sort": 7, "tableName": null, "tableEnName": null, "propertyName": "厂商", "propertyEnName": "com<PERSON>y", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "af2d306e-e642-4abb-a6e5-45134ce5b68e", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "3243209f-b21a-483b-b5c7-7f64b7b490bc", "sort": 12, "tableName": null, "tableEnName": null, "propertyName": "状态", "propertyEnName": "status", "propertyType": "string", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "bdd24269-04a0-4bce-8f4c-dd0a43a577a4", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a", "sort": 11, "tableName": null, "tableEnName": null, "propertyName": "是否可以使用电子登机牌", "propertyEnName": "eleboardpass", "propertyType": "bool", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "c95dd54c-9c29-4cef-a061-4bf39ac0277a", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "dadb0c8d-6a2c-4567-8547-b756893f90fe", "sort": 7, "tableName": null, "tableEnName": null, "propertyName": "电子商务柜台", "propertyEnName": "businesscounter", "propertyType": "string", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "d62b22c7-671b-4e54-aff3-08340c1b80ea", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1", "sort": 1, "tableName": null, "tableEnName": null, "propertyName": "汽车", "propertyEnName": "car", "propertyType": "string", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "df13e2c1-820b-41d3-8a00-b20ed62deb5d", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "d6bc5a7c-ca42-431d-92b6-01a15b86d55c", "sort": 2, "tableName": null, "tableEnName": null, "propertyName": "价格", "propertyEnName": "price", "propertyType": "num", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "e708a703-e2b8-40b5-b352-beee7ed361f4", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "c431d5dd-6dda-4ae4-90c9-781916664561", "sort": 10, "tableName": null, "tableEnName": null, "propertyName": "机场安保人数", "propertyEnName": "worker", "propertyType": "num", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "f3d54f36-702c-4caf-8a90-b80f2dfa6f6b", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "propertyId": "dedd0eef-b411-4afb-84a3-0991b1f01ca1", "sort": 3, "tableName": null, "tableEnName": null, "propertyName": "出发楼层", "propertyEnName": "leavefloor", "propertyType": "num", "synonym": "", "reply": "", "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "fa75439e-ff65-40f2-85a4-4f1de1e65ae2", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "propertyId": "eda7169a-b081-4a24-83ac-f280c65af9b7", "sort": 15, "tableName": null, "tableEnName": null, "propertyName": "上市日期", "propertyEnName": "Listingdate", "propertyType": "date", "synonym": "", "reply": null, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "dynamic": false, "instructionId": null, "instructionMark": null}], "tableRecords": [{"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "205a7a9a-29c4-44dd-aa2a-b2e719349584", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "北京大兴机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"2\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"北京市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"北京\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T1\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T4\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"T1航站楼C12柜台\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"2167\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"4\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"2\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"false\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"1.34\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2021-01-01\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"false\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "2", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "北京市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "北京", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T1", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T4", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "T1航站楼C12柜台", "c431d5dd-6dda-4ae4-90c9-781916664561": "2167", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "4", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "2", "7f1e2f21-555f-415c-9078-affbc1e34e59": "false", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "1.34", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2021-01-01", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "false"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "36d1061a-f0bb-4002-b56a-9453a623817e", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "S级", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"三厢\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"进口\",\"46669de0-c895-4610-978b-5b204144623a\":\"后轮驱动\",\"928958de-803f-49b0-b037-83256bfc6946\":\"true\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"1730000\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"3\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国六\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"1730000\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"9档 手自一体\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"1984-05-12\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"德系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"豪华型\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"奔驰\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "三厢", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "进口", "46669de0-c895-4610-978b-5b204144623a": "后轮驱动", "928958de-803f-49b0-b037-83256bfc6946": "true", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "1730000", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "3", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国六", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "1730000", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "9档 手自一体", "eda7169a-b081-4a24-83ac-f280c65af9b7": "1984-05-12", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "德系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "豪华型", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "奔驰"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "4433dbfa-7fa4-486a-9936-4deea2482958", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "上海浦东机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"3\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"上海市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"上海\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T2\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T4\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"L岛29号对面\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"1355\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"4\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"3\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"true\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"1.22\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2020-01-17\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"true\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "3", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "上海市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "上海", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T2", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T4", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "L岛29号对面", "c431d5dd-6dda-4ae4-90c9-781916664561": "1355", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "4", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "3", "7f1e2f21-555f-415c-9078-affbc1e34e59": "true", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "1.22", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2020-01-17", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "true"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "4dcadaef-9e4e-4b35-877f-c17cf8620a1b", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "北京首都机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"1\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"北京市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"北京\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T2\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T2\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"T2航站楼F岛岛头\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"1320\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"4\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"2\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"true\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"1.43\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2020-12-14\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"true\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "1", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "北京市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "北京", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T2", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T2", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "T2航站楼F岛岛头", "c431d5dd-6dda-4ae4-90c9-781916664561": "1320", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "4", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "2", "7f1e2f21-555f-415c-9078-affbc1e34e59": "true", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "1.43", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2020-12-14", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "true"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "4eba8da1-f809-47f3-b9f5-90bd40dbe6e9", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "广州白云机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"2\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"广州市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"广州\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T2\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T3\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"T2航站楼G02柜台\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"1230\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"4\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"2\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"true\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"0.99\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2021-01-01\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"true\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "2", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "广州市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "广州", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T2", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T3", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "T2航站楼G02柜台", "c431d5dd-6dda-4ae4-90c9-781916664561": "1230", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "4", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "2", "7f1e2f21-555f-415c-9078-affbc1e34e59": "true", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "0.99", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2021-01-01", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "true"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "57778f1c-8831-4806-b0fd-cb5f8737b32d", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "A4L", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"三厢\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"合资\",\"46669de0-c895-4610-978b-5b204144623a\":\"前轮驱动\",\"928958de-803f-49b0-b037-83256bfc6946\":\"false\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"349400\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"1.4\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国六\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"349400\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"7档 双离合\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"2008-02-03\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"德系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"中型车\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"奥迪\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "三厢", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "合资", "46669de0-c895-4610-978b-5b204144623a": "前轮驱动", "928958de-803f-49b0-b037-83256bfc6946": "false", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "349400", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "1.4", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国六", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "349400", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "7档 双离合", "eda7169a-b081-4a24-83ac-f280c65af9b7": "2008-02-03", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "德系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "中型车", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "奥迪"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "589d5cb6-c135-409b-9337-824745e18158", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "太原武宿机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"4\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"太原市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"太原\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T1\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T2\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"航站楼82号柜台\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"422\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"3\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"5\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"false\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"0.45\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2021-03-18\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"true\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "4", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "太原市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "太原", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T1", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T2", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "航站楼82号柜台", "c431d5dd-6dda-4ae4-90c9-781916664561": "422", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "3", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "5", "7f1e2f21-555f-415c-9078-affbc1e34e59": "false", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "0.45", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2021-03-18", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "true"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "61525a35-20f7-4f3e-9aa0-74f84a818ecd", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "科迈罗", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"跑车\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"进口\",\"46669de0-c895-4610-978b-5b204144623a\":\"后轮驱动\",\"928958de-803f-49b0-b037-83256bfc6946\":\"true\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"399900\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"2\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国五\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"399900\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"8档 手自一体\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"2020-10-08\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"美系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"中型跑车\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"雪佛兰\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "跑车", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "进口", "46669de0-c895-4610-978b-5b204144623a": "后轮驱动", "928958de-803f-49b0-b037-83256bfc6946": "true", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "399900", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "2", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国五", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "399900", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "8档 手自一体", "eda7169a-b081-4a24-83ac-f280c65af9b7": "2020-10-08", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "美系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "中型跑车", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "雪佛兰"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "704c7a44-9068-4e70-b31e-327049ccfa59", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "大连金州湾机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"4\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"大连市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"大连\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"建设中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T1\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T5\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"航站楼2号柜台\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"312\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"3\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"1\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"false\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"0.31\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2014-01-22\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"true\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "4", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "大连市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "大连", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "建设中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T1", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T5", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "航站楼2号柜台", "c431d5dd-6dda-4ae4-90c9-781916664561": "312", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "3", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "1", "7f1e2f21-555f-415c-9078-affbc1e34e59": "false", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "0.31", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2014-01-22", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "true"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "8773a8df-0f14-4ea9-8f4e-f152177f4954", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "途胜", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"SUV\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"合资\",\"46669de0-c895-4610-978b-5b204144623a\":\"前轮驱动\",\"928958de-803f-49b0-b037-83256bfc6946\":\"true\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"239900\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"1.6\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国五\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"239900\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"7档 双离合\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"2014-05-08\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"韩系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"紧凑型SUV\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"现代\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "SUV", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "合资", "46669de0-c895-4610-978b-5b204144623a": "前轮驱动", "928958de-803f-49b0-b037-83256bfc6946": "true", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "239900", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "1.6", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国五", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "239900", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "7档 双离合", "eda7169a-b081-4a24-83ac-f280c65af9b7": "2014-05-08", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "韩系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "紧凑型SUV", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "现代"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "8b16dded-148c-4ff5-b90c-bfe0019502e1", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "石家庄正定机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"1\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"石家庄市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"石家庄\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T2\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T2\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"航站楼3层2号柜台\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"562\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"3\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"3\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"false\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"0.34\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2016-01-13\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"false\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "1", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "石家庄市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "石家庄", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T2", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T2", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "航站楼3层2号柜台", "c431d5dd-6dda-4ae4-90c9-781916664561": "562", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "3", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "3", "7f1e2f21-555f-415c-9078-affbc1e34e59": "false", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "0.34", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2016-01-13", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "false"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "91f0f190-d38a-41e0-840c-dac1995a4df8", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "天津滨海机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"3\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"天津市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"天津\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T1\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T3\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"T1航站楼B2柜台\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"1233\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"0\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"2\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"true\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"0.98\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2019-11-02\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"true\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "3", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "天津市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "天津", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T1", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T3", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "T1航站楼B2柜台", "c431d5dd-6dda-4ae4-90c9-781916664561": "1233", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "0", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "2", "7f1e2f21-555f-415c-9078-affbc1e34e59": "true", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "0.98", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2019-11-02", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "true"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "9b9d936a-9d22-47cf-b3c7-34c5165cc9da", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "途锐", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"SUV\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"进口\",\"46669de0-c895-4610-978b-5b204144623a\":\"全时四驱\",\"928958de-803f-49b0-b037-83256bfc6946\":\"true\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"835800\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"3\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国五\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"835800\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"8档 手自一体\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"2006-08-03\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"德系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"中大型SUV\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"大众\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "SUV", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "进口", "46669de0-c895-4610-978b-5b204144623a": "全时四驱", "928958de-803f-49b0-b037-83256bfc6946": "true", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "835800", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "3", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国五", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "835800", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "8档 手自一体", "eda7169a-b081-4a24-83ac-f280c65af9b7": "2006-08-03", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "德系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "中大型SUV", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "大众"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "b2fef2ee-2105-41e4-9cda-9a9c20d43c49", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "长白山机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"3\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"白山市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"白山\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T3\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T1\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"F岛72号\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"367\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"4\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"4\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"false\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"0.54\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2005-06-21\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"false\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "3", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "白山市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "白山", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T3", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T1", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "F岛72号", "c431d5dd-6dda-4ae4-90c9-781916664561": "367", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "4", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "4", "7f1e2f21-555f-415c-9078-affbc1e34e59": "false", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "0.54", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2005-06-21", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "false"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "ca1a63ec-d0ff-49a6-8e54-362e02ac2bf3", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "哈弗H6", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"SUV\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"自主\",\"46669de0-c895-4610-978b-5b204144623a\":\"前轮驱动\",\"928958de-803f-49b0-b037-83256bfc6946\":\"true\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"134000\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"1.5\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国五\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"134000\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"7档 双离合\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"2019-08-10\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"国产\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"紧凑型SUV\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"长城\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "SUV", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "自主", "46669de0-c895-4610-978b-5b204144623a": "前轮驱动", "928958de-803f-49b0-b037-83256bfc6946": "true", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "134000", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "1.5", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国五", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "134000", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "7档 双离合", "eda7169a-b081-4a24-83ac-f280c65af9b7": "2019-08-10", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "国产", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "紧凑型SUV", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "长城"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "d0c090ad-56a8-4287-8d48-62ba92f3072a", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "5系", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"三厢\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"合资\",\"46669de0-c895-4610-978b-5b204144623a\":\"全时四驱\",\"928958de-803f-49b0-b037-83256bfc6946\":\"false\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"659900\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"3\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国六\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"659900\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"8档 手自一体\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"2019-09-06\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"德系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"中大型车\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"宝马\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "三厢", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "合资", "46669de0-c895-4610-978b-5b204144623a": "全时四驱", "928958de-803f-49b0-b037-83256bfc6946": "false", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "659900", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "3", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国六", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "659900", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "8档 手自一体", "eda7169a-b081-4a24-83ac-f280c65af9b7": "2019-09-06", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "德系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "中大型车", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "宝马"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "e9c0b3e2-4272-4afa-92bc-2e46e140652e", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "帕萨特", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"三厢\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"合资\",\"46669de0-c895-4610-978b-5b204144623a\":\"前轮驱动\",\"928958de-803f-49b0-b037-83256bfc6946\":\"true\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"288900\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"2\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国五\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"288900\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"7档 双离合\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"2012-01-01\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"德系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"中型车\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"大众\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "三厢", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "合资", "46669de0-c895-4610-978b-5b204144623a": "前轮驱动", "928958de-803f-49b0-b037-83256bfc6946": "true", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "288900", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "2", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国五", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "288900", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "7档 双离合", "eda7169a-b081-4a24-83ac-f280c65af9b7": "2012-01-01", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "德系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "中型车", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "大众"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "eb56430b-32ac-49bd-a858-016b254780f3", "categoryId": "b035ef50-082f-4468-9b2a-326935f7a30d", "recordName": "上海虹桥机场", "recordValue": "{\"00841368-3987-482c-bc79-435a4eb081a2\":\"5\",\"ffbef38e-2dd8-4c95-97df-80b601d77e0c\":\"上海市\",\"196f077b-addf-4ea3-9736-f23d8fed08d4\":\"上海\",\"3243209f-b21a-483b-b5c7-7f64b7b490bc\":\"运营中\",\"500f4497-5600-4427-8b5f-75dda86a9e41\":\"T2\",\"83b4392c-2b38-4dd6-83da-da15a85d0271\":\"T5\",\"dadb0c8d-6a2c-4567-8547-b756893f90fe\":\"虹桥T2航站楼4号门内\",\"c431d5dd-6dda-4ae4-90c9-781916664561\":\"1933\",\"f10705f3-29a2-4bfe-ad3a-cff811cf363e\":\"4\",\"dedd0eef-b411-4afb-84a3-0991b1f01ca1\":\"3\",\"7f1e2f21-555f-415c-9078-affbc1e34e59\":\"true\",\"b8613701-9ae4-409c-bbc2-19d427d2cf61\":\"1.31\",\"fe79ea7e-f46c-4c39-97a7-94927aa10055\":\"2021-01-01\",\"7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a\":\"true\"}", "values": {"00841368-3987-482c-bc79-435a4eb081a2": "5", "ffbef38e-2dd8-4c95-97df-80b601d77e0c": "上海市", "196f077b-addf-4ea3-9736-f23d8fed08d4": "上海", "3243209f-b21a-483b-b5c7-7f64b7b490bc": "运营中", "500f4497-5600-4427-8b5f-75dda86a9e41": "T2", "83b4392c-2b38-4dd6-83da-da15a85d0271": "T5", "dadb0c8d-6a2c-4567-8547-b756893f90fe": "虹桥T2航站楼4号门内", "c431d5dd-6dda-4ae4-90c9-781916664561": "1933", "f10705f3-29a2-4bfe-ad3a-cff811cf363e": "4", "dedd0eef-b411-4afb-84a3-0991b1f01ca1": "3", "7f1e2f21-555f-415c-9078-affbc1e34e59": "true", "b8613701-9ae4-409c-bbc2-19d427d2cf61": "1.31", "fe79ea7e-f46c-4c39-97a7-94927aa10055": "2021-01-01", "7fd5bc1f-9150-4e4c-a4d1-a7b719aca58a": "true"}, "nameValues": null, "tableId": "e787ef3c-bec5-4305-8b1e-d9f6212b4865", "version": 1, "created": "2021-07-12 16:18:05", "updated": "2021-07-12 16:18:05", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "f4fb3a2b-fbb4-4378-aa77-8947769c8386", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "CR-V", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"SUV\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"合资\",\"46669de0-c895-4610-978b-5b204144623a\":\"全时四驱\",\"928958de-803f-49b0-b037-83256bfc6946\":\"true\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"154800\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"1.5\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国五\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"154800\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"6档 手动\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"1989-12-01\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"日系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"紧凑型SUV\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"本田\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "SUV", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "合资", "46669de0-c895-4610-978b-5b204144623a": "全时四驱", "928958de-803f-49b0-b037-83256bfc6946": "true", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "154800", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "1.5", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国五", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "154800", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "6档 手动", "eda7169a-b081-4a24-83ac-f280c65af9b7": "1989-12-01", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "日系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "紧凑型SUV", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "本田"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "d35534fc-cddb-4309-93ea-425f1d005009", "id": "fc31e6ad-574e-4224-9cab-6b8ff3f0f737", "categoryId": "03a7fa14-5c11-4350-9845-7eeed24d71f7", "recordName": "X5", "recordValue": "{\"b2415e83-fd4c-4afd-9ad2-aa2621638aac\":\"SUV\",\"f6de9453-d27b-4f0c-afc8-c1bfb69c8d22\":\"进口\",\"46669de0-c895-4610-978b-5b204144623a\":\"全时四驱\",\"928958de-803f-49b0-b037-83256bfc6946\":\"false\",\"84655a5a-4083-4e9a-afd4-97584762df8c\":\"5\",\"2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8\":\"汽油\",\"cf3278a6-d94f-456b-9213-8d4330f6b2cd\":\"994900\",\"0cdbbe84-3846-408a-92c4-989e0d00ab15\":\"3\",\"7f377b5f-0984-4096-bbaa-87d3857bb6e7\":\"国六\",\"d6bc5a7c-ca42-431d-92b6-01a15b86d55c\":\"994900\",\"3d840bf3-5924-4e0d-8e23-830d6d1e6d91\":\"8档 手自一体\",\"eda7169a-b081-4a24-83ac-f280c65af9b7\":\"1999-09-09\",\"e8b89ac9-7842-4231-bfeb-a12dedafd463\":\"德系\",\"ca81f707-2c31-4b0e-8e88-a7d5cc22caa3\":\"中大型SUV\",\"590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1\":\"宝马\"}", "values": {"b2415e83-fd4c-4afd-9ad2-aa2621638aac": "SUV", "f6de9453-d27b-4f0c-afc8-c1bfb69c8d22": "进口", "46669de0-c895-4610-978b-5b204144623a": "全时四驱", "928958de-803f-49b0-b037-83256bfc6946": "false", "84655a5a-4083-4e9a-afd4-97584762df8c": "5", "2fdf4014-3ce9-4d8e-9de5-05bd54e5bea8": "汽油", "cf3278a6-d94f-456b-9213-8d4330f6b2cd": "994900", "0cdbbe84-3846-408a-92c4-989e0d00ab15": "3", "7f377b5f-0984-4096-bbaa-87d3857bb6e7": "国六", "d6bc5a7c-ca42-431d-92b6-01a15b86d55c": "994900", "3d840bf3-5924-4e0d-8e23-830d6d1e6d91": "8档 手自一体", "eda7169a-b081-4a24-83ac-f280c65af9b7": "1999-09-09", "e8b89ac9-7842-4231-bfeb-a12dedafd463": "德系", "ca81f707-2c31-4b0e-8e88-a7d5cc22caa3": "中大型SUV", "590004e0-d95a-4e45-9ed9-f7d3e1d3c7b1": "宝马"}, "nameValues": null, "tableId": "9138dc30-6e5e-449a-a845-878d5f3d9beb", "version": 1, "created": "2021-07-12 16:19:04", "updated": "2021-07-12 16:19:04", "lastEditUsername": "ok千层锦", "lastEditUserId": "*********", "createdUserId": "*********", "createdUsername": "ok千层锦", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}], "tableQaNoResponses": []}}}