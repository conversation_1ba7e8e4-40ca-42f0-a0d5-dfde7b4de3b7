{"agentType": 1, "sign": "453db6ecf87c5d2bf97b2a9d73a57676", "exportData": {"dialog": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "dialogNodes": [], "dialogProcess": [{"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "26c54a30-0cd3-4c01-967a-424251fe0555", "name": "aa", "description": "", "createdUsername": "user1", "createdUserId": "t1000000001", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "created": "2022-07-04 18:49:48", "updated": "2022-07-04 18:49:52", "status": 0, "version": 1, "type": 0, "weakGuide": false, "weakGuideContent": "null", "jointAtCompleted": false}]}, "chitchat": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "chitchat": []}, "bot": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "bot": [{"id": "d49e25d2-7a7c-43ad-85e5-08a5240c199c", "name": "我是自动测评要用的bot", "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "createdUserId": "t1000000001", "createdUserName": "user1", "lastEditUserId": "t1000000001", "lastEditUserName": "user1", "auditStatus": "UNVERIFIED", "publishStatus": "OFFLINE", "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "botType": 0, "source": 0, "botSettings": {"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "42a11679-5155-4b2d-ab75-26c6b8f7ed40", "botId": "d49e25d2-7a7c-43ad-85e5-08a5240c199c", "config": "{\"silent\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"kgEnable\":false,\"qaRecommendList\":{\"source\":1,\"list\":[],\"introduction\":\"您是不是要咨询以下问题？请点击确认\"},\"tableQaDefaultClarifySetting\":{\"voice\":{\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"hangup\":{\"action\":\"\",\"isWebhook\":false,\"text\":\"抱歉,我不太理解您的意思\",\"type\":1},\"relatedQuestion\":{\"autoEnable\":false,\"manualEnable\":false,\"autoCount\":5},\"clarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选问题}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选问题}还是{候选问题}呢?\",\"templateMany\":\"请问您想咨询的是{候选问题}还是{候选问题}还是{候选问题},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":0,\"reply\":\"\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"tableQaClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选关键词}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"webhookGlobalValue\":\"暂时无法获取到返回结果\",\"unMatch\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"unMatched\":\"抱歉,我不太理解您的意思\",\"defaultReply\":{\"text\":\"抱歉,我不太理解您的意思\",\"type\":1}},\"entityClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选实体}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选实体}还是{候选实体}呢?\",\"templateMany\":\"请问您想咨询的是{候选实体}还是{候选实体}还是{候选实体},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"repeat\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"interrupt\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"faqConcatEnable\":true,\"gossipEnable\":false}", "sensitiveStrategy": "{\"list\":[{\"name\":\"未命名策略bcf\",\"show\":true,\"repositories\":[],\"strategy\":{\"unMatched\":null,\"defaultReply\":null,\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]},\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]}}}]}", "personalChat": "{\"enable\":false,\"commonInfo\":{\"name\":\"\",\"sex\":0,\"birthday\":\"2022-07-01\",\"workUnit\":\"\"}}", "entityRecommend": "{\"guidance\":\"为您推荐以下选项\",\"enable\":false,\"num\":5,\"model\":0}", "priority": "{\"type\":\"system\",\"config\":[{\"name\":\"模板澄清\",\"description\":\"同一模板同时命中多个意图或faq，则澄清这些意图和faq\",\"condition\":\"templateClarify.isEndProcess()\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"templateClarify\",\"sourceName\":\"模板澄清\",\"sort\":1,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话命中模板\",\"description\":\"同一模板只命中意图，且多轮有答案\",\"condition\":\"taskbased.isSolved() && taskbased.getModel() == \\\"template\\\" \",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":10,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"FAQ问答命中模板\",\"description\":\"同一模板只命中faq，且faq有答案\",\"condition\":\"faq.isSolved() && faq.getModel() == \\\"template\\\"\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":20,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"表格问答命中模板\",\"description\":\"同一模板只命中表格问答，且表格问答有答案\",\"condition\":\"tableQa.isSolved() && tableQa.getModel() == \\\"template\\\"\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"tableQa\",\"sourceName\":\"表格问答\",\"sort\":25,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度任务式会话\",\"description\":\"多轮有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"taskbased.isSolved() && taskbased.getModel() == \\\"knn\\\" && taskbased.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":30,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度表格问答\",\"description\":\"表格问答有答案、置信度大于阈值，属于高置信度\",\"condition\":\"tableQa.isSolved() && tableQa.getConfidence() > knnConfidenceThreshold && !tableQa.isClarify()\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"tableQa\",\"sourceName\":\"表格问答\",\"sort\":45,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度FAQ问答\",\"description\":\"faq有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"faq.isSolved() && faq.getModel() == \\\"knn\\\" && faq.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":50,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度闲聊\",\"description\":\"chitchat有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"chitchat.isSolved() && chitchat.getModel() == \\\"knn\\\" && chitchat.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"chitchat\",\"sourceName\":\"闲聊\",\"sort\":60,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话有答案\",\"description\":\"多轮有答案，不属于子节点anythingElse、顶层节点anythingElse、会话开始条件\",\"condition\":\"taskbased.isSolved() && !taskbased.isAnythingElse() && !taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":70,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"表格问答有答案\",\"description\":\"表格问答有答案\",\"condition\":\"tableQa.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"tableQa\",\"sourceName\":\"表格问答\",\"sort\":81,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"FAQ问答高置信度澄清\",\"description\":\"faq高置信度澄清，同时命中多个faq标准问且相似度差小于0.05\",\"condition\":\"faqHighConfidenceClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"faqHighConfidenceClarify\",\"sourceName\":\"faq高置信度答案\",\"sort\":90,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"FAQ问答有答案\",\"description\":\"faq有答案\",\"condition\":\"faq.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":100,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"退出策略\",\"description\":\"命中系统退出策略\",\"condition\":\"reset.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"reset\",\"sourceName\":\"系统退出策略\",\"sort\":115,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"重述策略\",\"description\":\"命中系统重述策略\",\"condition\":\"repeat.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"repeat\",\"sourceName\":\"系统重述策略\",\"sort\":120,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"实体澄清\",\"description\":\"实体澄清\",\"condition\":\"entityClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityClarify\",\"sourceName\":\"实体澄清\",\"sort\":130,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"实体自动填充\",\"description\":\"复合实体自动填充\",\"condition\":\"entityAutoFill.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityAutoFill\",\"sourceName\":\"实体填充\",\"sort\":140,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话子节点AnythingElse\",\"description\":\"多轮有答案、命中子节点anythingElse条件\",\"condition\":\"taskbased.isSolved() && taskbased.isAnythingElse() && !taskbased.isTopAnythingElse() && !taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":150,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"意图、问答澄清\",\"description\":\"意图、faq澄清\",\"condition\":\"intentFaqClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"intentFaqClarify\",\"sourceName\":\"意图、faq澄清\",\"sort\":160,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"表格问答澄清\",\"description\":\"表格问答澄清\",\"condition\":\"tableQaClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"澄清\",\"source\":\"tableQaClarify\",\"sourceName\":\"表格问答\",\"sort\":171,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"敏感词\",\"description\":\"敏感词\",\"condition\":\"sensitive.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"sensitive\",\"sourceName\":\"系统敏感词策略\",\"sort\":180,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"闲聊有答案\",\"description\":\"闲聊有答案\",\"condition\":\"chitchat.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"chitchat\",\"sourceName\":\"闲聊\",\"sort\":190,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话命中会话开始\",\"description\":\"多轮、命中会话开始条件\",\"condition\":\"taskbased.isSolved() && taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":200,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"静默策略\",\"description\":\"命中系统静默策略\",\"condition\":\"silent.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"silent\",\"sourceName\":\"系统静默策略\",\"sort\":210,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"打断策略\",\"description\":\"命中系统打断策略\",\"condition\":\"interrupt.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"interrupt\",\"sourceName\":\"系统打断策略\",\"sort\":220,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"实体换一换\",\"description\":\"实体推荐、换一换\",\"condition\":\"entityAskReplaceAns.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityAskReplaceAns\",\"sourceName\":\"实体推荐、换一换\",\"sort\":225,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"场景栈引导\",\"description\":\"主动引导、尝试多轮当前场景栈顶流程进入\",\"condition\":\"activeGuide.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"activeGuide\",\"sourceName\":\"系统引导\",\"sort\":230,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"多次未匹配\",\"description\":\"多次未匹配回复\",\"condition\":\"multiNoMatch.isEndProcess()\",\"category\":\"noMatch\",\"categoryName\":\"无答案\",\"source\":\"multiNoMatch\",\"sourceName\":\"系统多次未匹配策略\",\"sort\":240,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话命中顶层AnythingElse\",\"description\":\"多轮有答案、命中顶层anythingElse条件\",\"condition\":\"taskbased.isTopAnythingElse()\",\"category\":\"noMatch\",\"categoryName\":\"无答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":250,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null}],\"auditStatus\":null,\"version\":1,\"agentType\":0,\"agentId\":null,\"botId\":null,\"importConf\":false,\"systemConf\":true}", "event": null, "intervention": null, "richWelcome": "{\"text\":\"您好，很高兴为您服务！\",\"type\":1}", "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "configAll": "{\"entityRecommend\":{\"guidance\":\"为您推荐以下选项\",\"enable\":false,\"num\":5,\"model\":0},\"silent\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"kgEnable\":false,\"richWelcome\":{\"text\":\"您好，很高兴为您服务！\",\"type\":1},\"qaRecommendList\":{\"source\":1,\"list\":[],\"introduction\":\"您是不是要咨询以下问题？请点击确认\"},\"tableQaDefaultClarifySetting\":{\"voice\":{\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"sensitive\":{\"list\":[{\"name\":\"未命名策略bcf\",\"show\":true,\"repositories\":[],\"strategy\":{\"unMatched\":null,\"defaultReply\":null,\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]},\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]}}}]},\"priority\":\"{\\\"type\\\":\\\"system\\\",\\\"config\\\":[{\\\"name\\\":\\\"模板澄清\\\",\\\"description\\\":\\\"同一模板同时命中多个意图或faq，则澄清这些意图和faq\\\",\\\"condition\\\":\\\"templateClarify.isEndProcess()\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"templateClarify\\\",\\\"sourceName\\\":\\\"模板澄清\\\",\\\"sort\\\":1,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话命中模板\\\",\\\"description\\\":\\\"同一模板只命中意图，且多轮有答案\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.getModel() == \\\\\\\"template\\\\\\\" \\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":10,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"FAQ问答命中模板\\\",\\\"description\\\":\\\"同一模板只命中faq，且faq有答案\\\",\\\"condition\\\":\\\"faq.isSolved() && faq.getModel() == \\\\\\\"template\\\\\\\"\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":20,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"表格问答命中模板\\\",\\\"description\\\":\\\"同一模板只命中表格问答，且表格问答有答案\\\",\\\"condition\\\":\\\"tableQa.isSolved() && tableQa.getModel() == \\\\\\\"template\\\\\\\"\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"tableQa\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":25,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度任务式会话\\\",\\\"description\\\":\\\"多轮有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.getModel() == \\\\\\\"knn\\\\\\\" && taskbased.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":30,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度表格问答\\\",\\\"description\\\":\\\"表格问答有答案、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"tableQa.isSolved() && tableQa.getConfidence() > knnConfidenceThreshold && !tableQa.isClarify()\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"tableQa\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":45,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度FAQ问答\\\",\\\"description\\\":\\\"faq有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"faq.isSolved() && faq.getModel() == \\\\\\\"knn\\\\\\\" && faq.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":50,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度闲聊\\\",\\\"description\\\":\\\"chitchat有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"chitchat.isSolved() && chitchat.getModel() == \\\\\\\"knn\\\\\\\" && chitchat.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"chitchat\\\",\\\"sourceName\\\":\\\"闲聊\\\",\\\"sort\\\":60,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话有答案\\\",\\\"description\\\":\\\"多轮有答案，不属于子节点anythingElse、顶层节点anythingElse、会话开始条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && !taskbased.isAnythingElse() && !taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":70,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"表格问答有答案\\\",\\\"description\\\":\\\"表格问答有答案\\\",\\\"condition\\\":\\\"tableQa.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"tableQa\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":81,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"FAQ问答高置信度澄清\\\",\\\"description\\\":\\\"faq高置信度澄清，同时命中多个faq标准问且相似度差小于0.05\\\",\\\"condition\\\":\\\"faqHighConfidenceClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"faqHighConfidenceClarify\\\",\\\"sourceName\\\":\\\"faq高置信度答案\\\",\\\"sort\\\":90,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"FAQ问答有答案\\\",\\\"description\\\":\\\"faq有答案\\\",\\\"condition\\\":\\\"faq.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":100,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"退出策略\\\",\\\"description\\\":\\\"命中系统退出策略\\\",\\\"condition\\\":\\\"reset.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"reset\\\",\\\"sourceName\\\":\\\"系统退出策略\\\",\\\"sort\\\":115,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"重述策略\\\",\\\"description\\\":\\\"命中系统重述策略\\\",\\\"condition\\\":\\\"repeat.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"repeat\\\",\\\"sourceName\\\":\\\"系统重述策略\\\",\\\"sort\\\":120,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"实体澄清\\\",\\\"description\\\":\\\"实体澄清\\\",\\\"condition\\\":\\\"entityClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityClarify\\\",\\\"sourceName\\\":\\\"实体澄清\\\",\\\"sort\\\":130,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"实体自动填充\\\",\\\"description\\\":\\\"复合实体自动填充\\\",\\\"condition\\\":\\\"entityAutoFill.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityAutoFill\\\",\\\"sourceName\\\":\\\"实体填充\\\",\\\"sort\\\":140,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话子节点AnythingElse\\\",\\\"description\\\":\\\"多轮有答案、命中子节点anythingElse条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.isAnythingElse() && !taskbased.isTopAnythingElse() && !taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":150,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"意图、问答澄清\\\",\\\"description\\\":\\\"意图、faq澄清\\\",\\\"condition\\\":\\\"intentFaqClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"intentFaqClarify\\\",\\\"sourceName\\\":\\\"意图、faq澄清\\\",\\\"sort\\\":160,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"表格问答澄清\\\",\\\"description\\\":\\\"表格问答澄清\\\",\\\"condition\\\":\\\"tableQaClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"澄清\\\",\\\"source\\\":\\\"tableQaClarify\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":171,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"敏感词\\\",\\\"description\\\":\\\"敏感词\\\",\\\"condition\\\":\\\"sensitive.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"sensitive\\\",\\\"sourceName\\\":\\\"系统敏感词策略\\\",\\\"sort\\\":180,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"闲聊有答案\\\",\\\"description\\\":\\\"闲聊有答案\\\",\\\"condition\\\":\\\"chitchat.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"chitchat\\\",\\\"sourceName\\\":\\\"闲聊\\\",\\\"sort\\\":190,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话命中会话开始\\\",\\\"description\\\":\\\"多轮、命中会话开始条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":200,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"静默策略\\\",\\\"description\\\":\\\"命中系统静默策略\\\",\\\"condition\\\":\\\"silent.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"silent\\\",\\\"sourceName\\\":\\\"系统静默策略\\\",\\\"sort\\\":210,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"打断策略\\\",\\\"description\\\":\\\"命中系统打断策略\\\",\\\"condition\\\":\\\"interrupt.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"interrupt\\\",\\\"sourceName\\\":\\\"系统打断策略\\\",\\\"sort\\\":220,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"实体换一换\\\",\\\"description\\\":\\\"实体推荐、换一换\\\",\\\"condition\\\":\\\"entityAskReplaceAns.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityAskReplaceAns\\\",\\\"sourceName\\\":\\\"实体推荐、换一换\\\",\\\"sort\\\":225,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"场景栈引导\\\",\\\"description\\\":\\\"主动引导、尝试多轮当前场景栈顶流程进入\\\",\\\"condition\\\":\\\"activeGuide.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"activeGuide\\\",\\\"sourceName\\\":\\\"系统引导\\\",\\\"sort\\\":230,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"多次未匹配\\\",\\\"description\\\":\\\"多次未匹配回复\\\",\\\"condition\\\":\\\"multiNoMatch.isEndProcess()\\\",\\\"category\\\":\\\"noMatch\\\",\\\"categoryName\\\":\\\"无答案\\\",\\\"source\\\":\\\"multiNoMatch\\\",\\\"sourceName\\\":\\\"系统多次未匹配策略\\\",\\\"sort\\\":240,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话命中顶层AnythingElse\\\",\\\"description\\\":\\\"多轮有答案、命中顶层anythingElse条件\\\",\\\"condition\\\":\\\"taskbased.isTopAnythingElse()\\\",\\\"category\\\":\\\"noMatch\\\",\\\"categoryName\\\":\\\"无答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":250,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null}],\\\"auditStatus\\\":null,\\\"version\\\":1,\\\"agentType\\\":0,\\\"agentId\\\":null,\\\"botId\\\":null,\\\"importConf\\\":false,\\\"systemConf\\\":true}\",\"hangup\":{\"action\":\"\",\"isWebhook\":false,\"text\":\"抱歉,我不太理解您的意思\",\"type\":1},\"relatedQuestion\":{\"autoEnable\":false,\"manualEnable\":false,\"autoCount\":5},\"clarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选问题}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选问题}还是{候选问题}呢?\",\"templateMany\":\"请问您想咨询的是{候选问题}还是{候选问题}还是{候选问题},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":0,\"reply\":\"\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"tableQaClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选关键词}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"webhookGlobalValue\":\"暂时无法获取到返回结果\",\"unMatch\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"unMatched\":\"抱歉,我不太理解您的意思\",\"defaultReply\":{\"text\":\"抱歉,我不太理解您的意思\",\"type\":1}},\"personalChat\":{\"enable\":false,\"commonInfo\":{\"name\":\"\",\"sex\":0,\"birthday\":\"2022-07-01\",\"workUnit\":\"\"}},\"entityClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选实体}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选实体}还是{候选实体}呢?\",\"templateMany\":\"请问您想咨询的是{候选实体}还是{候选实体}还是{候选实体},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"repeat\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"interrupt\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"faqConcatEnable\":true,\"gossipEnable\":false}"}, "botProcess": [], "botFaq": [{"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "331b4df7-67be-41c3-8185-21658f632bb0", "botId": "d49e25d2-7a7c-43ad-85e5-08a5240c199c", "dirId": "0", "dirName": "默认"}], "botTableQa": [{"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "54b2799b-e343-4ae0-82ab-bedbaac6300d", "botId": "d49e25d2-7a7c-43ad-85e5-08a5240c199c", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "skillId": "19953", "botName": null, "tableList": "[{\"_effect\":1,\"agentId\":\"2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9\",\"id\":\"9c4c122a-47c0-4897-9b06-f1f97412f123\",\"categoryId\":\"f5b2688d-999e-45c3-b6b3-b3d2da69ff45\",\"tableName\":\"机场\",\"tableEnName\":\"airport\",\"tableAlias\":\"空港\",\"description\":null,\"version\":1,\"created\":\"2022-07-01 16:04:47\",\"updated\":\"2022-07-01 16:04:47\",\"lastEditUsername\":\"user1\",\"lastEditUserId\":\"t1000000001\",\"createdUserId\":\"t1000000001\",\"createdUsername\":\"user1\",\"userId\":null,\"userName\":null,\"hasActiveCopy\":false,\"tableAliasList\":[\"空港\"],\"categoryName\":null,\"importantLimit\":false,\"importantPropertyNum\":0,\"importantRecordNum\":0,\"fuzzySupport\":false,\"fuzzyRange\":0.0,\"optimizeBind\":false}]", "sort": 1, "tables": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "9c4c122a-47c0-4897-9b06-f1f97412f123", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableAlias": "空港", "description": null, "version": 1, "created": "2022-07-01 16:04:47", "updated": "2022-07-01 16:04:47", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["空港"], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": false, "fuzzyRange": 0.0, "optimizeBind": false}]}, {"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "f99b74b8-44ac-498f-8e49-a98ee8b5115d", "botId": "d49e25d2-7a7c-43ad-85e5-08a5240c199c", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "skillId": "19969", "botName": null, "tableList": "[{\"_effect\":1,\"agentId\":\"2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9\",\"id\":\"671cab76-8cde-4e4e-b1dc-76524dc37321\",\"categoryId\":\"b925d8f9-51f1-4bff-a040-938874c9127a\",\"tableName\":\"车\",\"tableEnName\":\"type\",\"tableAlias\":\"alias\",\"description\":null,\"version\":1,\"created\":\"2022-07-01 16:04:47\",\"updated\":\"2022-07-01 16:04:47\",\"lastEditUsername\":\"user1\",\"lastEditUserId\":\"t1000000001\",\"createdUserId\":\"t1000000001\",\"createdUsername\":\"user1\",\"userId\":null,\"userName\":null,\"hasActiveCopy\":false,\"tableAliasList\":[\"alias\"],\"categoryName\":null,\"importantLimit\":false,\"importantPropertyNum\":0,\"importantRecordNum\":0,\"fuzzySupport\":false,\"fuzzyRange\":0.0,\"optimizeBind\":false}]", "sort": 2, "tables": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "671cab76-8cde-4e4e-b1dc-76524dc37321", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableName": "车", "tableEnName": "type", "tableAlias": "alias", "description": null, "version": 1, "created": "2022-07-01 16:04:47", "updated": "2022-07-01 16:04:47", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["alias"], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": false, "fuzzyRange": 0.0, "optimizeBind": false}]}]}, {"id": "33a020ef-1ad7-4106-8487-58db23a35daf", "name": "表格问答bot", "created": "2022-07-01 17:14:51", "updated": "2022-07-04 20:11:39", "createdUserId": "t1000000001", "createdUserName": "user1", "lastEditUserId": "t1000000001", "lastEditUserName": "user1", "auditStatus": "VERIFIED", "publishStatus": "OFFLINE", "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "botType": 0, "source": 0, "botSettings": {"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "d7e3437b-aaca-426f-aac3-4388690e1fb2", "botId": "33a020ef-1ad7-4106-8487-58db23a35daf", "config": "{\"silent\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"kgEnable\":false,\"qaRecommendList\":{\"source\":1,\"list\":[],\"introduction\":\"您是不是要咨询以下问题？请点击确认\"},\"tableQaDefaultClarifySetting\":{\"voice\":{\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"hangup\":{\"action\":\"\",\"isWebhook\":false,\"text\":\"抱歉,我不太理解您的意思\",\"type\":1},\"relatedQuestion\":{\"autoEnable\":false,\"manualEnable\":false,\"autoCount\":5},\"clarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选问题}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选问题}还是{候选问题}呢?\",\"templateMany\":\"请问您想咨询的是{候选问题}还是{候选问题}还是{候选问题},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":0,\"reply\":\"\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"tableQaClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选关键词}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"webhookGlobalValue\":\"暂时无法获取到返回结果\",\"unMatch\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"unMatched\":\"抱歉,我不太理解您的意思\",\"defaultReply\":{\"text\":\"抱歉,我不太理解您的意思\",\"type\":1}},\"entityClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选实体}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选实体}还是{候选实体}呢?\",\"templateMany\":\"请问您想咨询的是{候选实体}还是{候选实体}还是{候选实体},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"repeat\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"interrupt\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"faqConcatEnable\":true,\"gossipEnable\":false}", "sensitiveStrategy": "{\"list\":[{\"name\":\"未命名策略bcf\",\"show\":true,\"repositories\":[],\"strategy\":{\"unMatched\":null,\"defaultReply\":null,\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]},\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]}}}]}", "personalChat": "{\"enable\":false,\"commonInfo\":{\"name\":\"\",\"sex\":0,\"birthday\":\"2022-07-01\",\"workUnit\":\"\"}}", "entityRecommend": "{\"guidance\":\"为您推荐以下选项\",\"enable\":false,\"num\":5,\"model\":0}", "priority": "{\"type\":\"system\",\"config\":[{\"name\":\"模板澄清\",\"description\":\"同一模板同时命中多个意图或faq，则澄清这些意图和faq\",\"condition\":\"templateClarify.isEndProcess()\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"templateClarify\",\"sourceName\":\"模板澄清\",\"sort\":1,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话命中模板\",\"description\":\"同一模板只命中意图，且多轮有答案\",\"condition\":\"taskbased.isSolved() && taskbased.getModel() == \\\"template\\\" \",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":10,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"FAQ问答命中模板\",\"description\":\"同一模板只命中faq，且faq有答案\",\"condition\":\"faq.isSolved() && faq.getModel() == \\\"template\\\"\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":20,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"表格问答命中模板\",\"description\":\"同一模板只命中表格问答，且表格问答有答案\",\"condition\":\"tableQa.isSolved() && tableQa.getModel() == \\\"template\\\"\",\"category\":\"template\",\"categoryName\":\"模板\",\"source\":\"tableQa\",\"sourceName\":\"表格问答\",\"sort\":25,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度任务式会话\",\"description\":\"多轮有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"taskbased.isSolved() && taskbased.getModel() == \\\"knn\\\" && taskbased.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":30,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度表格问答\",\"description\":\"表格问答有答案、置信度大于阈值，属于高置信度\",\"condition\":\"tableQa.isSolved() && tableQa.getConfidence() > knnConfidenceThreshold && !tableQa.isClarify()\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"tableQa\",\"sourceName\":\"表格问答\",\"sort\":45,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度FAQ问答\",\"description\":\"faq有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"faq.isSolved() && faq.getModel() == \\\"knn\\\" && faq.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":50,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"高置信度闲聊\",\"description\":\"chitchat有答案、命中knn、置信度大于阈值，属于高置信度\",\"condition\":\"chitchat.isSolved() && chitchat.getModel() == \\\"knn\\\" && chitchat.getConfidence() > knnConfidenceThreshold\",\"category\":\"highConfidence\",\"categoryName\":\"高置信度\",\"source\":\"chitchat\",\"sourceName\":\"闲聊\",\"sort\":60,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话有答案\",\"description\":\"多轮有答案，不属于子节点anythingElse、顶层节点anythingElse、会话开始条件\",\"condition\":\"taskbased.isSolved() && !taskbased.isAnythingElse() && !taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":70,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"表格问答有答案\",\"description\":\"表格问答有答案\",\"condition\":\"tableQa.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"tableQa\",\"sourceName\":\"表格问答\",\"sort\":81,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"FAQ问答高置信度澄清\",\"description\":\"faq高置信度澄清，同时命中多个faq标准问且相似度差小于0.05\",\"condition\":\"faqHighConfidenceClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"faqHighConfidenceClarify\",\"sourceName\":\"faq高置信度答案\",\"sort\":90,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"FAQ问答有答案\",\"description\":\"faq有答案\",\"condition\":\"faq.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"faq\",\"sourceName\":\"问答\",\"sort\":100,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"退出策略\",\"description\":\"命中系统退出策略\",\"condition\":\"reset.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"reset\",\"sourceName\":\"系统退出策略\",\"sort\":115,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"重述策略\",\"description\":\"命中系统重述策略\",\"condition\":\"repeat.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"repeat\",\"sourceName\":\"系统重述策略\",\"sort\":120,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"实体澄清\",\"description\":\"实体澄清\",\"condition\":\"entityClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityClarify\",\"sourceName\":\"实体澄清\",\"sort\":130,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"实体自动填充\",\"description\":\"复合实体自动填充\",\"condition\":\"entityAutoFill.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityAutoFill\",\"sourceName\":\"实体填充\",\"sort\":140,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话子节点AnythingElse\",\"description\":\"多轮有答案、命中子节点anythingElse条件\",\"condition\":\"taskbased.isSolved() && taskbased.isAnythingElse() && !taskbased.isTopAnythingElse() && !taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":150,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"意图、问答澄清\",\"description\":\"意图、faq澄清\",\"condition\":\"intentFaqClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"intentFaqClarify\",\"sourceName\":\"意图、faq澄清\",\"sort\":160,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"表格问答澄清\",\"description\":\"表格问答澄清\",\"condition\":\"tableQaClarify.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"澄清\",\"source\":\"tableQaClarify\",\"sourceName\":\"表格问答\",\"sort\":171,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"敏感词\",\"description\":\"敏感词\",\"condition\":\"sensitive.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"sensitive\",\"sourceName\":\"系统敏感词策略\",\"sort\":180,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"闲聊有答案\",\"description\":\"闲聊有答案\",\"condition\":\"chitchat.isSolved()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"chitchat\",\"sourceName\":\"闲聊\",\"sort\":190,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话命中会话开始\",\"description\":\"多轮、命中会话开始条件\",\"condition\":\"taskbased.isSolved() && taskbased.isConversationStart()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":200,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"静默策略\",\"description\":\"命中系统静默策略\",\"condition\":\"silent.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"silent\",\"sourceName\":\"系统静默策略\",\"sort\":210,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"打断策略\",\"description\":\"命中系统打断策略\",\"condition\":\"interrupt.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"interrupt\",\"sourceName\":\"系统打断策略\",\"sort\":220,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"实体换一换\",\"description\":\"实体推荐、换一换\",\"condition\":\"entityAskReplaceAns.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"entityAskReplaceAns\",\"sourceName\":\"实体推荐、换一换\",\"sort\":225,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"场景栈引导\",\"description\":\"主动引导、尝试多轮当前场景栈顶流程进入\",\"condition\":\"activeGuide.isEndProcess()\",\"category\":\"resolved\",\"categoryName\":\"有答案\",\"source\":\"activeGuide\",\"sourceName\":\"系统引导\",\"sort\":230,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"多次未匹配\",\"description\":\"多次未匹配回复\",\"condition\":\"multiNoMatch.isEndProcess()\",\"category\":\"noMatch\",\"categoryName\":\"无答案\",\"source\":\"multiNoMatch\",\"sourceName\":\"系统多次未匹配策略\",\"sort\":240,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null},{\"name\":\"任务式会话命中顶层AnythingElse\",\"description\":\"多轮有答案、命中顶层anythingElse条件\",\"condition\":\"taskbased.isTopAnythingElse()\",\"category\":\"noMatch\",\"categoryName\":\"无答案\",\"source\":\"taskbased\",\"sourceName\":\"任务式会话\",\"sort\":250,\"enable\":true,\"system\":true,\"normalCondition\":null,\"conditionType\":0,\"eventId\":null,\"eventName\":null,\"sourceType\":null,\"skillAgentId\":null}],\"auditStatus\":null,\"version\":1,\"agentType\":0,\"agentId\":null,\"botId\":null,\"importConf\":false,\"systemConf\":true}", "event": null, "intervention": null, "richWelcome": "{\"text\":\"您好，很高兴为您服务！\",\"type\":1}", "created": "2022-07-01 17:14:51", "updated": "2022-07-01 17:14:51", "configAll": "{\"entityRecommend\":{\"guidance\":\"为您推荐以下选项\",\"enable\":false,\"num\":5,\"model\":0},\"silent\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"kgEnable\":false,\"richWelcome\":{\"text\":\"您好，很高兴为您服务！\",\"type\":1},\"qaRecommendList\":{\"source\":1,\"list\":[],\"introduction\":\"您是不是要咨询以下问题？请点击确认\"},\"tableQaDefaultClarifySetting\":{\"voice\":{\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"sensitive\":{\"list\":[{\"name\":\"未命名策略bcf\",\"show\":true,\"repositories\":[],\"strategy\":{\"unMatched\":null,\"defaultReply\":null,\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]},\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"type\":1,\"text\":\"\",\"audio\":null,\"isWebhook\":false,\"action\":\"\",\"actionName\":null}]}}}]},\"priority\":\"{\\\"type\\\":\\\"system\\\",\\\"config\\\":[{\\\"name\\\":\\\"模板澄清\\\",\\\"description\\\":\\\"同一模板同时命中多个意图或faq，则澄清这些意图和faq\\\",\\\"condition\\\":\\\"templateClarify.isEndProcess()\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"templateClarify\\\",\\\"sourceName\\\":\\\"模板澄清\\\",\\\"sort\\\":1,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话命中模板\\\",\\\"description\\\":\\\"同一模板只命中意图，且多轮有答案\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.getModel() == \\\\\\\"template\\\\\\\" \\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":10,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"FAQ问答命中模板\\\",\\\"description\\\":\\\"同一模板只命中faq，且faq有答案\\\",\\\"condition\\\":\\\"faq.isSolved() && faq.getModel() == \\\\\\\"template\\\\\\\"\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":20,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"表格问答命中模板\\\",\\\"description\\\":\\\"同一模板只命中表格问答，且表格问答有答案\\\",\\\"condition\\\":\\\"tableQa.isSolved() && tableQa.getModel() == \\\\\\\"template\\\\\\\"\\\",\\\"category\\\":\\\"template\\\",\\\"categoryName\\\":\\\"模板\\\",\\\"source\\\":\\\"tableQa\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":25,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度任务式会话\\\",\\\"description\\\":\\\"多轮有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.getModel() == \\\\\\\"knn\\\\\\\" && taskbased.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":30,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度表格问答\\\",\\\"description\\\":\\\"表格问答有答案、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"tableQa.isSolved() && tableQa.getConfidence() > knnConfidenceThreshold && !tableQa.isClarify()\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"tableQa\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":45,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度FAQ问答\\\",\\\"description\\\":\\\"faq有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"faq.isSolved() && faq.getModel() == \\\\\\\"knn\\\\\\\" && faq.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":50,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"高置信度闲聊\\\",\\\"description\\\":\\\"chitchat有答案、命中knn、置信度大于阈值，属于高置信度\\\",\\\"condition\\\":\\\"chitchat.isSolved() && chitchat.getModel() == \\\\\\\"knn\\\\\\\" && chitchat.getConfidence() > knnConfidenceThreshold\\\",\\\"category\\\":\\\"highConfidence\\\",\\\"categoryName\\\":\\\"高置信度\\\",\\\"source\\\":\\\"chitchat\\\",\\\"sourceName\\\":\\\"闲聊\\\",\\\"sort\\\":60,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话有答案\\\",\\\"description\\\":\\\"多轮有答案，不属于子节点anythingElse、顶层节点anythingElse、会话开始条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && !taskbased.isAnythingElse() && !taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":70,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"表格问答有答案\\\",\\\"description\\\":\\\"表格问答有答案\\\",\\\"condition\\\":\\\"tableQa.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"tableQa\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":81,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"FAQ问答高置信度澄清\\\",\\\"description\\\":\\\"faq高置信度澄清，同时命中多个faq标准问且相似度差小于0.05\\\",\\\"condition\\\":\\\"faqHighConfidenceClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"faqHighConfidenceClarify\\\",\\\"sourceName\\\":\\\"faq高置信度答案\\\",\\\"sort\\\":90,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"FAQ问答有答案\\\",\\\"description\\\":\\\"faq有答案\\\",\\\"condition\\\":\\\"faq.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"faq\\\",\\\"sourceName\\\":\\\"问答\\\",\\\"sort\\\":100,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"退出策略\\\",\\\"description\\\":\\\"命中系统退出策略\\\",\\\"condition\\\":\\\"reset.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"reset\\\",\\\"sourceName\\\":\\\"系统退出策略\\\",\\\"sort\\\":115,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"重述策略\\\",\\\"description\\\":\\\"命中系统重述策略\\\",\\\"condition\\\":\\\"repeat.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"repeat\\\",\\\"sourceName\\\":\\\"系统重述策略\\\",\\\"sort\\\":120,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"实体澄清\\\",\\\"description\\\":\\\"实体澄清\\\",\\\"condition\\\":\\\"entityClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityClarify\\\",\\\"sourceName\\\":\\\"实体澄清\\\",\\\"sort\\\":130,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"实体自动填充\\\",\\\"description\\\":\\\"复合实体自动填充\\\",\\\"condition\\\":\\\"entityAutoFill.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityAutoFill\\\",\\\"sourceName\\\":\\\"实体填充\\\",\\\"sort\\\":140,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话子节点AnythingElse\\\",\\\"description\\\":\\\"多轮有答案、命中子节点anythingElse条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.isAnythingElse() && !taskbased.isTopAnythingElse() && !taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":150,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"意图、问答澄清\\\",\\\"description\\\":\\\"意图、faq澄清\\\",\\\"condition\\\":\\\"intentFaqClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"intentFaqClarify\\\",\\\"sourceName\\\":\\\"意图、faq澄清\\\",\\\"sort\\\":160,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"表格问答澄清\\\",\\\"description\\\":\\\"表格问答澄清\\\",\\\"condition\\\":\\\"tableQaClarify.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"澄清\\\",\\\"source\\\":\\\"tableQaClarify\\\",\\\"sourceName\\\":\\\"表格问答\\\",\\\"sort\\\":171,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"敏感词\\\",\\\"description\\\":\\\"敏感词\\\",\\\"condition\\\":\\\"sensitive.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"sensitive\\\",\\\"sourceName\\\":\\\"系统敏感词策略\\\",\\\"sort\\\":180,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"闲聊有答案\\\",\\\"description\\\":\\\"闲聊有答案\\\",\\\"condition\\\":\\\"chitchat.isSolved()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"chitchat\\\",\\\"sourceName\\\":\\\"闲聊\\\",\\\"sort\\\":190,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话命中会话开始\\\",\\\"description\\\":\\\"多轮、命中会话开始条件\\\",\\\"condition\\\":\\\"taskbased.isSolved() && taskbased.isConversationStart()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":200,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"静默策略\\\",\\\"description\\\":\\\"命中系统静默策略\\\",\\\"condition\\\":\\\"silent.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"silent\\\",\\\"sourceName\\\":\\\"系统静默策略\\\",\\\"sort\\\":210,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"打断策略\\\",\\\"description\\\":\\\"命中系统打断策略\\\",\\\"condition\\\":\\\"interrupt.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"interrupt\\\",\\\"sourceName\\\":\\\"系统打断策略\\\",\\\"sort\\\":220,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"实体换一换\\\",\\\"description\\\":\\\"实体推荐、换一换\\\",\\\"condition\\\":\\\"entityAskReplaceAns.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"entityAskReplaceAns\\\",\\\"sourceName\\\":\\\"实体推荐、换一换\\\",\\\"sort\\\":225,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"场景栈引导\\\",\\\"description\\\":\\\"主动引导、尝试多轮当前场景栈顶流程进入\\\",\\\"condition\\\":\\\"activeGuide.isEndProcess()\\\",\\\"category\\\":\\\"resolved\\\",\\\"categoryName\\\":\\\"有答案\\\",\\\"source\\\":\\\"activeGuide\\\",\\\"sourceName\\\":\\\"系统引导\\\",\\\"sort\\\":230,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"多次未匹配\\\",\\\"description\\\":\\\"多次未匹配回复\\\",\\\"condition\\\":\\\"multiNoMatch.isEndProcess()\\\",\\\"category\\\":\\\"noMatch\\\",\\\"categoryName\\\":\\\"无答案\\\",\\\"source\\\":\\\"multiNoMatch\\\",\\\"sourceName\\\":\\\"系统多次未匹配策略\\\",\\\"sort\\\":240,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null},{\\\"name\\\":\\\"任务式会话命中顶层AnythingElse\\\",\\\"description\\\":\\\"多轮有答案、命中顶层anythingElse条件\\\",\\\"condition\\\":\\\"taskbased.isTopAnythingElse()\\\",\\\"category\\\":\\\"noMatch\\\",\\\"categoryName\\\":\\\"无答案\\\",\\\"source\\\":\\\"taskbased\\\",\\\"sourceName\\\":\\\"任务式会话\\\",\\\"sort\\\":250,\\\"enable\\\":true,\\\"system\\\":true,\\\"normalCondition\\\":null,\\\"conditionType\\\":0,\\\"eventId\\\":null,\\\"eventName\\\":null,\\\"sourceType\\\":null,\\\"skillAgentId\\\":null}],\\\"auditStatus\\\":null,\\\"version\\\":1,\\\"agentType\\\":0,\\\"agentId\\\":null,\\\"botId\\\":null,\\\"importConf\\\":false,\\\"systemConf\\\":true}\",\"hangup\":{\"action\":\"\",\"isWebhook\":false,\"text\":\"抱歉,我不太理解您的意思\",\"type\":1},\"relatedQuestion\":{\"autoEnable\":false,\"manualEnable\":false,\"autoCount\":5},\"clarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选问题}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选问题}还是{候选问题}呢?\",\"templateMany\":\"请问您想咨询的是{候选问题}还是{候选问题}还是{候选问题},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":0,\"reply\":\"\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"tableQaClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选关键词}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选关键词}还是{候选关键词}呢?\",\"templateMany\":\"请问您想咨询的是{候选关键词}还是{候选关键词}还是{候选关键词},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"webhookGlobalValue\":\"暂时无法获取到返回结果\",\"unMatch\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"unMatched\":\"抱歉,我不太理解您的意思\",\"defaultReply\":{\"text\":\"抱歉,我不太理解您的意思\",\"type\":1}},\"personalChat\":{\"enable\":false,\"commonInfo\":{\"name\":\"\",\"sex\":0,\"birthday\":\"2022-07-01\",\"workUnit\":\"\"}},\"entityClarifySetting\":{\"voice\":{\"templateOne\":\"请问您想问的是{候选实体}吗?\",\"count\":3,\"templateTwo\":\"请问您想咨询的是{候选实体}还是{候选实体}呢?\",\"templateMany\":\"请问您想咨询的是{候选实体}还是{候选实体}还是{候选实体},请问您想咨询第几个?\"},\"enableText\":true,\"unMatchReply\":{\"type\":1,\"reply\":\"不好意思，您能说的具体一点吗？\",\"nodeId\":\"\"},\"enableVoice\":true,\"text\":{\"count\":5,\"templateMany\":\"请问您想咨询的是?\"}},\"repeat\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"interrupt\":{\"acc\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]},\"con\":{\"enable\":false,\"replys\":[{\"symbol\":\"=\",\"count\":1,\"isWebhook\":false,\"type\":1,\"text\":\"\",\"action\":\"\"}]}},\"faqConcatEnable\":true,\"gossipEnable\":false}"}, "botProcess": [], "botFaq": [{"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "3e921ac3-f3ae-4237-b55d-44f445d96b04", "botId": "33a020ef-1ad7-4106-8487-58db23a35daf", "dirId": "0", "dirName": "默认"}], "botTableQa": [{"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "233d4bd0-e465-4ba2-bcc1-186f6e641418", "botId": "33a020ef-1ad7-4106-8487-58db23a35daf", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "skillId": "19969", "botName": null, "tableList": "[{\"_effect\":1,\"agentId\":\"2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9\",\"id\":\"671cab76-8cde-4e4e-b1dc-76524dc37321\",\"categoryId\":\"b925d8f9-51f1-4bff-a040-938874c9127a\",\"tableName\":\"车\",\"tableEnName\":\"type\",\"tableAlias\":\"alias\",\"description\":null,\"version\":1,\"created\":\"2022-07-01 17:14:43\",\"updated\":\"2022-07-01 17:14:43\",\"lastEditUsername\":\"user1\",\"lastEditUserId\":\"t1000000001\",\"createdUserId\":\"t1000000001\",\"createdUsername\":\"user1\",\"userId\":null,\"userName\":null,\"hasActiveCopy\":false,\"tableAliasList\":[\"alias\"],\"categoryName\":null,\"importantLimit\":false,\"importantPropertyNum\":0,\"importantRecordNum\":0,\"fuzzySupport\":false,\"fuzzyRange\":0.0,\"optimizeBind\":false}]", "sort": 1, "tables": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "671cab76-8cde-4e4e-b1dc-76524dc37321", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableName": "车", "tableEnName": "type", "tableAlias": "alias", "description": null, "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["alias"], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": false, "fuzzyRange": 0.0, "optimizeBind": false}]}, {"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "596bc284-5820-4d54-9daf-672bd1008f5e", "botId": "33a020ef-1ad7-4106-8487-58db23a35daf", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "skillId": "19953", "botName": null, "tableList": "[{\"_effect\":1,\"agentId\":\"2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9\",\"id\":\"9c4c122a-47c0-4897-9b06-f1f97412f123\",\"categoryId\":\"f5b2688d-999e-45c3-b6b3-b3d2da69ff45\",\"tableName\":\"机场\",\"tableEnName\":\"airport\",\"tableAlias\":\"空港\",\"description\":null,\"version\":1,\"created\":\"2022-07-01 17:14:43\",\"updated\":\"2022-07-01 17:14:43\",\"lastEditUsername\":\"user1\",\"lastEditUserId\":\"t1000000001\",\"createdUserId\":\"t1000000001\",\"createdUsername\":\"user1\",\"userId\":null,\"userName\":null,\"hasActiveCopy\":false,\"tableAliasList\":[\"空港\"],\"categoryName\":null,\"importantLimit\":false,\"importantPropertyNum\":0,\"importantRecordNum\":0,\"fuzzySupport\":false,\"fuzzyRange\":0.0,\"optimizeBind\":false}]", "sort": 2, "tables": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "9c4c122a-47c0-4897-9b06-f1f97412f123", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableAlias": "空港", "description": null, "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["空港"], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": false, "fuzzyRange": 0.0, "optimizeBind": false}]}, {"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "b2a7206b-5590-4888-ad95-2c75df80e4a6", "botId": "33a020ef-1ad7-4106-8487-58db23a35daf", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "skillId": "20993", "botName": null, "tableList": "[{\"_effect\":1,\"agentId\":\"2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9\",\"id\":\"dd5f89b5-25c5-451c-9edf-28ecd1c361c7\",\"categoryId\":\"a79069b5-88e1-4c9d-a5f0-273adb96afef\",\"tableName\":\"理财产品\",\"tableEnName\":\"finance\",\"tableAlias\":\"\",\"description\":null,\"version\":4,\"created\":\"2022-07-04 20:05:44\",\"updated\":\"2022-07-04 20:05:45\",\"lastEditUsername\":\"user1\",\"lastEditUserId\":\"t1000000001\",\"createdUserId\":\"t1000000001\",\"createdUsername\":\"user1\",\"userId\":null,\"userName\":null,\"hasActiveCopy\":false,\"tableAliasList\":[],\"categoryName\":null,\"importantLimit\":false,\"importantPropertyNum\":0,\"importantRecordNum\":0,\"fuzzySupport\":true,\"fuzzyRange\":0.1,\"optimizeBind\":false}]", "sort": 3, "tables": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableAlias": "", "description": null, "version": 4, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": [], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": true, "fuzzyRange": 0.1, "optimizeBind": false}]}]}]}, "faq": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "faq": [], "faqNoResponse": []}, "skill": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "skill": [], "skillNoResponses": []}, "basicResources": {"voca": [], "agent": {"id": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "name": "tableqa_agent", "description": "", "isPublic": 0, "developerAccessToken": "0be265a6-fbf2-4468-acb1-84327a86401e", "createdUserId": "t1000000001", "createdUsername": "user1", "lastEditUserId": "t1000000001", "lastEditUsername": "user1", "industry": "ed0165dc-d0eb-46d4-adf7-4c26b05db167", "type": 1, "status": 0, "online": 1, "delete": 0, "created": "2022-07-01 17:14:40", "updated": "2022-07-01 17:14:43", "kgProjectId": "", "kgToken": "", "auditLevel": 3, "effect": 0, "language": "zh", "requestFrom": "NGD", "engine": null, "toolResource": null, "engineAbility": null, "qps": null, "settingVersion": null}, "webhook": null, "channelManage": {"channelDimension": [], "channelSpecialValue": [], "channelDict": {}}, "thirdEngine": {"thirdEngineConfList": [], "thirdEngineNoResponseList": []}, "settingAgent": {"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "1d4249eb-1d16-467a-86df-66289c88844d", "name": "tableqa_agent", "description": "", "config": "{\"multiIntentRec\":{\"enable\":false},\"toolResource\":{\"skill\":1},\"engine\":{\"kg\":1,\"faq\":0,\"task\":0,\"tableQA\":0,\"chitchat\":0},\"confidence\":{\"faqClarifyThreshold\":0.6,\"intentMinDifferenceThreshold\":0,\"tableQaKeywordClarifyThreshold\":0.85,\"tableQaThreshold\":0.8,\"tableQaClarifyThreshold\":0.6,\"queryCorrect\":{\"enable\":false},\"chatThreshold\":0.85,\"intentClarifyThreshold\":0.6,\"intentExampleThreshold\":0.9,\"faqThreshold\":0.8,\"intentModelThreshold\":0.8,\"tableQaKeywordMinDifferenceThreshold\":0.1},\"tableQaClarify\":{\"enable\":true},\"queryCorrect\":{\"enable\":false},\"contextUnderstand\":{\"enable\":false}}", "version": 1, "created": "2022-07-01 17:14:41", "updated": "2022-07-01 17:14:41", "limitConfig": null, "language": "zh", "labelNoticeSetting": null, "systemResource": null}, "stopword": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "repositories": [], "words": []}, "sensitive": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "repositories": [], "words": []}, "dirList": [], "dialogVariable": [], "instructionManage": {"instruction": [], "instructionVariables": []}}, "intent": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "importData": {"EXAMPLE": [], "USER_INTENT": [], "NO_RESP": [], "TEMPLATE": [], "SYS_INTENT": [{"id": "sys_intent_8"}, {"id": "sys_intent_14"}, {"id": "sys_intent_5"}, {"id": "sys_intent_9"}, {"id": "sys_intent_17"}, {"id": "sys_intent_6"}, {"id": "sys_intent_7"}, {"id": "sys_intent_12"}, {"id": "sys_intent_16"}, {"id": "sys_intent_15"}, {"id": "sys_intent_13"}, {"id": "sys_intent_10"}, {"id": "sys_intent_4"}, {"id": "sys_intent_11"}]}}, "version": "v700", "entity": {"version": null, "agentType": null, "agentId": null, "userId": null, "username": null, "importItem": null, "existAgent": false, "entity": []}, "tableQA": {"categories": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "b925d8f9-51f1-4bff-a040-938874c9127a", "categoryName": "汽车", "description": null, "version": 1, "created": "2022-07-01 17:14:41", "updated": "2022-07-01 17:14:41", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "interrupted": true}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "categoryName": "航空", "description": null, "version": 1, "created": "2022-07-01 17:14:41", "updated": "2022-07-01 17:14:41", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "interrupted": false}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "categoryName": "理财", "description": null, "version": 1, "created": "2022-07-04 20:05:33", "updated": "2022-07-04 20:05:33", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "interrupted": true}], "categorySkillRels": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "7242cd04-acba-4aa6-9f5e-4c41b0f3e127", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "skillId": 19953, "skillName": "NGD_航空_lqp8o_kz_rio6n", "offlineResourceIds": "UQS7yshI", "onlineResourceIds": "", "created": "2022-07-01 17:14:41", "updated": "2022-07-01 17:15:23", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "9d81426c-f55f-4e8d-a51f-5d7c22fe25a1", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "skillId": 20993, "skillName": "NGD_理财_wtz01", "offlineResourceIds": "HsK9B5WP", "onlineResourceIds": "", "created": "2022-07-04 20:05:33", "updated": "2022-07-04 20:06:00", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "df2cf32a-59b6-46d2-8a3c-200d55dff4cf", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "skillId": 19969, "skillName": "NGD_汽车_vi6w4_pp_rze69", "offlineResourceIds": "Q0XobqBE", "onlineResourceIds": "", "created": "2022-07-01 17:14:41", "updated": "2022-07-01 17:14:56", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null}], "properties": null, "synonyms": null, "tables": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableAlias": "", "description": null, "version": 4, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": [], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": true, "fuzzyRange": 0.1, "optimizeBind": false}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "671cab76-8cde-4e4e-b1dc-76524dc37321", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableName": "车", "tableEnName": "type", "tableAlias": "alias", "description": null, "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["alias"], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": false, "fuzzyRange": 0.0, "optimizeBind": false}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "9c4c122a-47c0-4897-9b06-f1f97412f123", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableAlias": "空港", "description": null, "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "tableAliasList": ["空港"], "categoryName": null, "importantLimit": false, "importantPropertyNum": 0, "importantRecordNum": 0, "fuzzySupport": false, "fuzzyRange": 0.0, "optimizeBind": false}], "tableSynonymRelations": [{"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "synonymId": "084997f0-9d29-4ced-bc8e-a41514fa8b95", "normalization": "价格", "normalizationAlias": "多贵", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["多贵"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "synonymId": "36149f4e-6b92-4f3a-8eb5-a3d5fee6e762", "normalization": "近一周收益率", "normalizationAlias": "近1周收益率", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["近1周收益率"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "synonymId": "380c30aa-2321-4bfb-8ed2-020d05c67873", "normalization": "帕萨特", "normalizationAlias": "小萨|小罗", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["小萨", "小罗"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "synonymId": "72a8a6f8-4ced-4e8f-b248-d1c39f19fb8e", "normalization": "奔驰", "normalizationAlias": "大奔", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["大奔"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "synonymId": "77dd101a-763e-4fac-9387-799ea9330ea0", "normalization": "国别", "normalizationAlias": "哪国生产", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["哪国生产"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "synonymId": "d76b164d-9777-4905-a0ac-fe0b42acee66", "normalization": "近一个月收益率", "normalizationAlias": "近1个月收益|近1个月收益率", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["近1个月收益", "近1个月收益率"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableName": "理财产品", "tableEnName": "finance", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "synonymId": "dbf88ef9-dfb8-4bfc-8c12-c0c3d9794540", "normalization": "科迈罗", "normalizationAlias": "小罗", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["小罗"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableName": "车", "tableEnName": "type", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "synonymId": "4dd5cad7-aea7-4ea8-a4f8-e62d1639a196", "normalization": "价格", "normalizationAlias": "多少钱", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["多少钱"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableName": "车", "tableEnName": "type", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "synonymId": "479ad964-ecc6-44cf-91bc-1320ac064c57", "normalization": "北京大兴机场", "normalizationAlias": "大兴机场", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["大兴机场"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableName": "车", "tableEnName": "type", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "synonymId": "d9965f2d-28d2-45b5-85ff-dddf545166db", "normalization": "帕萨特", "normalizationAlias": "pasta", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["pasta"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "synonymId": "3f967f39-92d0-4f35-8d6c-643ca3b952d0", "normalization": "北京大兴机场", "normalizationAlias": "大兴机场", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["大兴机场"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "synonymId": "6937446c-1db1-46a6-ac95-d532e19e1c3e", "normalization": "电子柜台", "normalizationAlias": "电子商务柜台", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["电子商务柜台"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "synonymId": "7278e4be-2d12-4671-813b-4d6130c49b27", "normalization": "服务区域", "normalizationAlias": "覆盖区域", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["覆盖区域"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "synonymId": "9d917a5c-a77b-4025-95dd-2d33b33841cb", "normalization": "天津", "normalizationAlias": "津沽", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["津沽"]}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableName": "机场", "tableEnName": "airport", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "synonymId": "bdca5619-8d5b-47b4-8f0f-b9422048c0fc", "normalization": "服务人数", "normalizationAlias": "吞吐量", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "hasActiveCopy": false, "hasRel": false, "categoryName": null, "normalizationAliasList": ["吞吐量"]}], "tablePropertyRelations": [{"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "14afbdd9-0a0d-4a08-afe3-85f731246769", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品期限分类1", "propertyEnName": "typeone", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "15514e10-9a68-47e1-92a9-71c7b9ecf949", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "非工作日赎回", "propertyEnName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "1ce5a328-1189-4700-b9cf-148847c552b0", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "定开型", "propertyEnName": "open", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "26fde9d0-7b28-453a-9c26-906843e0cd03", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "T加0", "propertyEnName": "dayzero", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "28697b10-ba02-48b3-97a2-84b14c14a9cc", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "现金管理类", "propertyEnName": "manage", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "28b7c993-dc10-4a7b-867e-ce316d895f0d", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "收益率", "propertyEnName": "rate", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "3500246a-e20f-41a2-9551-7a8b8e276766", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "周期型", "propertyEnName": "cycle", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "3957daa4-653a-4b36-b769-817477d22214", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "高净值", "propertyEnName": "high", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "3bf70455-4ffc-4c10-a0d6-5b915af9e13c", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品风险等级", "propertyEnName": "level", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "3c0c22ee-1cd6-4777-90da-8c89faca2dea", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品支持客户风险等级", "propertyEnName": "levelone", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "5115fdbf-787f-430b-bc72-ef33081e2213", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "近一周收益率", "propertyEnName": "weekrate1", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "5644a7d3-49a0-48b6-849f-27688d7add2c", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "分行专属", "propertyEnName": "zhuanshu", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "576f5b2d-f127-4f12-8df7-eec5240993ce", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "代理公司", "propertyEnName": "company", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "658ca97a-4826-4fe7-b889-09e0a4da2242", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "代发", "propertyEnName": "replace", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "6c449482-538b-440a-b7be-a360d1c2e384", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "私行", "propertyEnName": "own", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "72b1d40f-11c2-45c7-bf3b-64e6c479c680", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "实时赎回", "propertyEnName": "shu<PERSON>", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "769e3455-c90e-425b-a208-06809c471574", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "热销产品", "propertyEnName": "hot", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "81f5129b-c310-4d0a-91c8-0f5ebd8630e7", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "近三年收益率", "propertyEnName": "yearrate3", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "86daea23-b598-4dd9-b4cd-cc569dd9d92c", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "最短持有期", "propertyEnName": "short", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "8e6daeda-474b-499b-8d11-05723d8ce29f", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "支持预约赎回", "propertyEnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "8f73ba3c-a355-485b-8243-e70690bde1c2", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "自动赎回", "propertyEnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "8fa2f2fd-64e3-4fdc-9969-f09b70db3862", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "新客", "propertyEnName": "new", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "T加1", "propertyEnName": "plusone", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "自动到期", "propertyEnName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "a5738489-1705-436e-98f8-38f140d9f771", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "中移动", "propertyEnName": "move", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "a7dcc521-75b1-45fd-93d9-98291e29ef3d", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品收益率2", "propertyEnName": "<PERSON><PERSON>ne", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "aec01279-476d-4054-82b8-eb8ea9380fdc", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品起售日1", "propertyEnName": "sale", "propertyType": "date", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "固定持有期", "propertyEnName": "long", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "c23283bb-aff3-4737-a71a-a351aab93b02", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "固定期限类", "propertyEnName": "fixedone", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "c4e83057-7cc5-4aa6-8a03-c6510b498e1e", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品起购金额1", "propertyEnName": "money", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "cf00a1a6-3962-47ba-9812-9fb050901b45", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "近一个月收益率", "propertyEnName": "monthrate1", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "d3da52ba-653d-4cf8-bcd2-8f674e521434", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "募集型固定持有期", "propertyEnName": "fixed", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "d85cafad-b9d6-42c1-9f61-57465db53ff7", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "半开放式", "propertyEnName": "half", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "f2b882fb-e669-47f8-82a0-375f1233b456", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品代码", "propertyEnName": "num", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "fb055a48-dc89-494c-9076-00031878f5ad", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "产品类型", "propertyEnName": "type", "propertyType": "string", "synonym": "类型", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "propertyId": "fe8242e5-61c5-4359-bd2f-3352fa32c5f8", "oldPropertyId": null, "sort": 999, "tableName": "理财产品", "tableEnName": "finance", "propertyName": "封闭式", "propertyEnName": "close", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "30f09027-2d89-42dc-a623-877a23350e93", "oldPropertyId": null, "sort": 1, "tableName": "车", "tableEnName": "type", "propertyName": "汽车", "propertyEnName": "car", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "ae6be8ae-8f62-4a12-8b83-0ca12e203a40", "oldPropertyId": null, "sort": 2, "tableName": "车", "tableEnName": "type", "propertyName": "价格", "propertyEnName": "price", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "3a37e2df-d6b3-4529-a942-965b5eae20ac", "oldPropertyId": null, "sort": 3, "tableName": "车", "tableEnName": "type", "propertyName": "税费", "propertyEnName": "Taxes", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa", "oldPropertyId": null, "sort": 4, "tableName": "车", "tableEnName": "type", "propertyName": "级别", "propertyEnName": "level", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "a29743a0-6a1e-4855-973c-0a42e2a3fa42", "oldPropertyId": null, "sort": 5, "tableName": "车", "tableEnName": "type", "propertyName": "车身", "propertyEnName": "body", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4", "oldPropertyId": null, "sort": 6, "tableName": "车", "tableEnName": "type", "propertyName": "能源", "propertyEnName": "energy", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "ad072374-8993-4b0c-ad29-38bf6ed5c593", "oldPropertyId": null, "sort": 7, "tableName": "车", "tableEnName": "type", "propertyName": "厂商", "propertyEnName": "com<PERSON>y", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "957b9ebb-6245-475e-96de-4ed6958923b9", "oldPropertyId": null, "sort": 8, "tableName": "车", "tableEnName": "type", "propertyName": "国别", "propertyEnName": "country", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "02616606-4258-406b-8788-caafe919f8de", "oldPropertyId": null, "sort": 9, "tableName": "车", "tableEnName": "type", "propertyName": "变速箱", "propertyEnName": "Gearbox", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "087bdd27-a298-48eb-b9f5-799ddd598ede", "oldPropertyId": null, "sort": 10, "tableName": "车", "tableEnName": "type", "propertyName": "排量", "propertyEnName": "pailiang", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "3a033272-1e7d-4a07-a9f8-964f71fe3985", "oldPropertyId": null, "sort": 11, "tableName": "车", "tableEnName": "type", "propertyName": "驱动", "propertyEnName": "driver", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "bd9aa337-d19f-4cf2-acfb-0633213389eb", "oldPropertyId": null, "sort": 12, "tableName": "车", "tableEnName": "type", "propertyName": "排放", "propertyEnName": "emission", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "dc6c4a06-396c-47d7-ac88-2d82e9ff449b", "oldPropertyId": null, "sort": 13, "tableName": "车", "tableEnName": "type", "propertyName": "座位数", "propertyEnName": "seat", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "6795fdd2-dc2a-4929-a578-a32b05bcbc85", "oldPropertyId": null, "sort": 14, "tableName": "车", "tableEnName": "type", "propertyName": "全景天窗", "propertyEnName": "skylight", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "propertyId": "12ebb51e-5c9c-4e7b-b57e-174fe585233c", "oldPropertyId": null, "sort": 15, "tableName": "车", "tableEnName": "type", "propertyName": "上市日期", "propertyEnName": "Listingdate", "propertyType": "date", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "1098b1aa-0600-4061-b477-7fab6e2552e4", "oldPropertyId": null, "sort": 1, "tableName": "机场", "tableEnName": "airport", "propertyName": "所在城市", "propertyEnName": "city", "propertyType": "string", "synonym": "城市", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "90aacea8-17a0-4f72-84d8-dec68debd802", "oldPropertyId": null, "sort": 2, "tableName": "机场", "tableEnName": "airport", "propertyName": "出发航站楼", "propertyEnName": "leavebuilding", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "4a185acc-2beb-4b4c-bd56-31b49601d1f8", "oldPropertyId": null, "sort": 3, "tableName": "机场", "tableEnName": "airport", "propertyName": "出发楼层", "propertyEnName": "leavefloor", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "f81c5934-ba0f-4024-a86d-7fa9aafbc130", "oldPropertyId": null, "sort": 4, "tableName": "机场", "tableEnName": "airport", "propertyName": "到达航站楼", "propertyEnName": "arrivebuilding", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "d55409b3-22b8-4761-80a1-a6ecc24377af", "oldPropertyId": null, "sort": 5, "tableName": "机场", "tableEnName": "airport", "propertyName": "到达楼层", "propertyEnName": "arrivefloor", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "6a39e79b-0522-4a2c-8d8c-91efbc4cc863", "oldPropertyId": null, "sort": 6, "tableName": "机场", "tableEnName": "airport", "propertyName": "贵宾休息室", "propertyEnName": "VIProom", "propertyType": "bool", "synonym": "贵宾区", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "896c53ab-1366-4497-a23a-b781e53c7d9a", "oldPropertyId": null, "sort": 7, "tableName": "机场", "tableEnName": "airport", "propertyName": "电子商务柜台", "propertyEnName": "businesscounter", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "23254437-75d8-4092-b89a-3a31d767abdb", "oldPropertyId": null, "sort": 8, "tableName": "机场", "tableEnName": "airport", "propertyName": "飞行区等级", "propertyEnName": "rank", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "0643030d-4362-477b-9aea-a4bb7e2f2982", "oldPropertyId": null, "sort": 9, "tableName": "机场", "tableEnName": "airport", "propertyName": "服务区域", "propertyEnName": "area", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "674bbb73-8f3f-44ee-b71a-679da4d7c0eb", "oldPropertyId": null, "sort": 10, "tableName": "机场", "tableEnName": "airport", "propertyName": "机场安保人数", "propertyEnName": "worker", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "603729ee-867c-4085-b5d3-d18637e3743d", "oldPropertyId": null, "sort": 11, "tableName": "机场", "tableEnName": "airport", "propertyName": "是否可以使用电子登机牌", "propertyEnName": "eleboardpass", "propertyType": "bool", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d", "oldPropertyId": null, "sort": 12, "tableName": "机场", "tableEnName": "airport", "propertyName": "状态", "propertyEnName": "status", "propertyType": "string", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "13f8a2a4-0ac2-4877-898b-278792f7eabb", "oldPropertyId": null, "sort": 13, "tableName": "机场", "tableEnName": "airport", "propertyName": "吞吐量", "propertyEnName": "amount", "propertyType": "num", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}, {"_effect": 0, "agentId": null, "id": null, "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "propertyId": "4798c68f-80b6-4ed1-b35f-8154e979f68b", "oldPropertyId": null, "sort": 14, "tableName": "机场", "tableEnName": "airport", "propertyName": "启用日期", "propertyEnName": "date", "propertyType": "date", "synonym": "", "reply": null, "replyType": null, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "dynamic": false, "multiValue": false, "unitBasic": null, "unitDefault": null, "unitReply": null, "optimizeBind": false, "optimizeOtherBind": false}], "tableRecords": [{"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "068cb88c-0373-4672-bdc1-9aad876b6e8b", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "季季鑫", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"净值类募集型固定持有期\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"-0.4\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"10.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-09-29\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"中等风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"平衡型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"4\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5401194011\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"8.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"3.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"中短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "净值类募集型固定持有期", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "-0.4", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "10.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-09-29", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "中等风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "平衡型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "4", "f2b882fb-e669-47f8-82a0-375f1233b456": "5401194011", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "8.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "3.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "中短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "152d591a-4dff-4f10-8beb-0b3a4bf896e0", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "上海浦东机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"true\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"L岛29号对面\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2020-01-17\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"上海市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"1.22\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T4\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"1355\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"3\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"3\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"4\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"true\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"上海\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T2\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "true", "896c53ab-1366-4497-a23a-b781e53c7d9a": "L岛29号对面", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2020-01-17", "0643030d-4362-477b-9aea-a4bb7e2f2982": "上海市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "1.22", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T4", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "1355", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "3", "d55409b3-22b8-4761-80a1-a6ecc24377af": "3", "23254437-75d8-4092-b89a-3a31d767abdb": "4", "603729ee-867c-4085-b5d3-d18637e3743d": "true", "1098b1aa-0600-4061-b477-7fab6e2552e4": "上海", "90aacea8-17a0-4f72-84d8-dec68debd802": "T2"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "17241081-9a19-4303-b6f5-aeed5e10219e", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "上海虹桥机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"true\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"虹桥T2航站楼4号门内\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2021-01-01\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"上海市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"1.31\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T5\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"1933\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"3\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"5\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"4\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"true\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"上海\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T2\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "true", "896c53ab-1366-4497-a23a-b781e53c7d9a": "虹桥T2航站楼4号门内", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2021-01-01", "0643030d-4362-477b-9aea-a4bb7e2f2982": "上海市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "1.31", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T5", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "1933", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "3", "d55409b3-22b8-4761-80a1-a6ecc24377af": "5", "23254437-75d8-4092-b89a-3a31d767abdb": "4", "603729ee-867c-4085-b5d3-d18637e3743d": "true", "1098b1aa-0600-4061-b477-7fab6e2552e4": "上海", "90aacea8-17a0-4f72-84d8-dec68debd802": "T2"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "175c0014-0b21-439a-8b1f-bed10ffacdb7", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "5系", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"659900\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国六\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"三厢\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"false\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"德系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"3\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"全时四驱\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"659900\",\"02616606-4258-406b-8788-caafe919f8de\":\"8档 手自一体\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"中大型车\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"2019-09-06\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"合资\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"宝马\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "659900", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国六", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "三厢", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "false", "957b9ebb-6245-475e-96de-4ed6958923b9": "德系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "3", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "全时四驱", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "659900", "02616606-4258-406b-8788-caafe919f8de": "8档 手自一体", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "中大型车", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "2019-09-06", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "合资", "30f09027-2d89-42dc-a623-877a23350e93": "宝马"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "1ad6438b-dd4a-4745-8aec-9b401a5d77c1", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "途锐", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"835800\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国五\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"SUV\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"true\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"德系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"3\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"全时四驱\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"835800\",\"02616606-4258-406b-8788-caafe919f8de\":\"8档 手自一体\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"中大型SUV\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"2006-08-03\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"进口\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"大众\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "835800", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国五", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "SUV", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "true", "957b9ebb-6245-475e-96de-4ed6958923b9": "德系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "3", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "全时四驱", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "835800", "02616606-4258-406b-8788-caafe919f8de": "8档 手自一体", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "中大型SUV", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "2006-08-03", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "进口", "30f09027-2d89-42dc-a623-877a23350e93": "大众"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "1b41092d-ce43-4569-a150-24b54dfc703f", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "北京大兴机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"false\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"T1航站楼C12柜台\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2021-01-01\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"北京市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"1.34\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T4\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"2167\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"2\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"2\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"4\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"false\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"北京\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T1\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "false", "896c53ab-1366-4497-a23a-b781e53c7d9a": "T1航站楼C12柜台", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2021-01-01", "0643030d-4362-477b-9aea-a4bb7e2f2982": "北京市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "1.34", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T4", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "2167", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "2", "d55409b3-22b8-4761-80a1-a6ecc24377af": "2", "23254437-75d8-4092-b89a-3a31d767abdb": "4", "603729ee-867c-4085-b5d3-d18637e3743d": "false", "1098b1aa-0600-4061-b477-7fab6e2552e4": "北京", "90aacea8-17a0-4f72-84d8-dec68debd802": "T1"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "21f27b62-61c0-4ddf-8337-2b5e2655d7bf", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "招银9个月13号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销-定开产品（同“净值类周期型非货基”）\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"5.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"-1.3\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"招行代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-08-10\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"保守型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"2\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"3501210323\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"2.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"11.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"中短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销-定开产品（同“净值类周期型非货基”）", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "5.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "-1.3", "576f5b2d-f127-4f12-8df7-eec5240993ce": "招行代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-08-10", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "保守型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "2", "f2b882fb-e669-47f8-82a0-375f1233b456": "3501210323", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "2.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "11.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "中短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "238a8c30-5561-492a-9c76-c5ee1f8e9685", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "CR-V", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"154800\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国五\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"SUV\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"true\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"日系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"1.5\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"全时四驱\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"154800\",\"02616606-4258-406b-8788-caafe919f8de\":\"6档 手动\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"紧凑型SUV\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"1989-12-01\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"合资\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"本田\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "154800", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国五", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "SUV", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "true", "957b9ebb-6245-475e-96de-4ed6958923b9": "日系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "1.5", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "全时四驱", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "154800", "02616606-4258-406b-8788-caafe919f8de": "6档 手动", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "紧凑型SUV", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "1989-12-01", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "合资", "30f09027-2d89-42dc-a623-877a23350e93": "本田"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "3bf9986d-5114-40bd-aae7-4f74a875e78e", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "信银理财固收稳健两年封闭式19号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"3.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"9.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2021-12-01\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"保守型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"2\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501040804\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"-1.3\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"-1.3\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "3.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "9.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2021-12-01", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "保守型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "2", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501040804", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "-1.3", "cf00a1a6-3962-47ba-9812-9fb050901b45": "-1.3", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "3e660167-e649-4a78-b2d3-2e5f397a35a3", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "广州白云机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"true\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"T2航站楼G02柜台\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2021-01-01\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"广州市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"0.99\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T3\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"1230\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"2\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"2\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"4\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"true\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"广州\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T2\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "true", "896c53ab-1366-4497-a23a-b781e53c7d9a": "T2航站楼G02柜台", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2021-01-01", "0643030d-4362-477b-9aea-a4bb7e2f2982": "广州市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "0.99", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T3", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "1230", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "2", "d55409b3-22b8-4761-80a1-a6ecc24377af": "2", "23254437-75d8-4092-b89a-3a31d767abdb": "4", "603729ee-867c-4085-b5d3-d18637e3743d": "true", "1098b1aa-0600-4061-b477-7fab6e2552e4": "广州", "90aacea8-17a0-4f72-84d8-dec68debd802": "T2"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "438ea21d-6d10-43f3-8301-579b9f4e11aa", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "哈弗H6", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"134000\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国五\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"SUV\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"true\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"国产\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"1.5\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"前轮驱动\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"134000\",\"02616606-4258-406b-8788-caafe919f8de\":\"7档 双离合\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"紧凑型SUV\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"2019-08-10\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"自主\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"长城\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "134000", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国五", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "SUV", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "true", "957b9ebb-6245-475e-96de-4ed6958923b9": "国产", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "1.5", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "前轮驱动", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "134000", "02616606-4258-406b-8788-caafe919f8de": "7档 双离合", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "紧凑型SUV", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "2019-08-10", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "自主", "30f09027-2d89-42dc-a623-877a23350e93": "长城"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "453720e4-78ad-42c1-82c6-4606d80ff832", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "科迈罗", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"399900\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国五\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"跑车\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"true\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"美系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"2\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"后轮驱动\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"399900\",\"02616606-4258-406b-8788-caafe919f8de\":\"8档 手自一体\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"中型跑车\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"2020-10-08\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"进口\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"雪佛兰\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "399900", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国五", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "跑车", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "true", "957b9ebb-6245-475e-96de-4ed6958923b9": "美系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "2", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "后轮驱动", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "399900", "02616606-4258-406b-8788-caafe919f8de": "8档 手自一体", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "中型跑车", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "2020-10-08", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "进口", "30f09027-2d89-42dc-a623-877a23350e93": "雪佛兰"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "4655e9d5-6c65-4e6d-a881-b5e2b91160ee", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "天添盈增利1号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"true\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"货基T+0货币型\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"true\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"0\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"0.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-09-28\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"true\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"稳健型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"3\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501022410\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"true\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"true\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"true\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"true\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"true\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"true\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"10.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"6.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"灵活\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"true\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"true\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"true\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "true", "fb055a48-dc89-494c-9076-00031878f5ad": "货基T+0货币型", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "true", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "0", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "0.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-09-28", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "true", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "稳健型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "3", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501022410", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "true", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "true", "c23283bb-aff3-4737-a71a-a351aab93b02": "true", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "true", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "true", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "true", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "10.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "6.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "灵活", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "true", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "true", "3500246a-e20f-41a2-9551-7a8b8e276766": "true", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "4aef2953-938e-40cc-927f-ce5ca1f10d9a", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "天添盈增利2号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"货基T+0货币型\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"高\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"-1.1\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"15.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-08-11\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"稳健型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"3\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"85010801555\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"14.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"2.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "货基T+0货币型", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "高", "5115fdbf-787f-430b-bc72-ef33081e2213": "-1.1", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "15.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-08-11", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "稳健型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "3", "f2b882fb-e669-47f8-82a0-375f1233b456": "85010801555", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "14.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "2.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "4d867a84-5b90-4563-aabf-c7caa0e1eaa4", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "招睿青葵一年半003B", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"高\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"5.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"10.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"招行代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2021-09-30\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"进取型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"6\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"2345654313\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"-1.1\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"-1.1\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "高", "5115fdbf-787f-430b-bc72-ef33081e2213": "5.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "10.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "招行代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2021-09-30", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "进取型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "6", "f2b882fb-e669-47f8-82a0-375f1233b456": "2345654313", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "-1.1", "cf00a1a6-3962-47ba-9812-9fb050901b45": "-1.1", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "515d4b14-b275-4770-a771-7573d65979c6", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "周周享盈增利1号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"净值类周期型货基\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"0\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"4.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-08-15\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"保守型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"2\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501516301\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"15.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"14.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"灵活\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "净值类周期型货基", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "0", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "4.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-08-15", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "保守型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "2", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501516301", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "15.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "14.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "灵活", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "571b5d91-1dcf-4739-ac1b-9076d7f89909", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "交银理财稳享现金添利", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"true\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销-现金管理类产品（货基T+1）\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"true\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"4.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"12.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"交银代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-10-22\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"true\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"保守型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"2\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"4501212425\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"true\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"true\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"true\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"true\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"true\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"true\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"1.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"0.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"灵活\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"true\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"true\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"true\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "true", "fb055a48-dc89-494c-9076-00031878f5ad": "代销-现金管理类产品（货基T+1）", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "true", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "4.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "12.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "交银代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-10-22", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "true", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "保守型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "2", "f2b882fb-e669-47f8-82a0-375f1233b456": "4501212425", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "true", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "true", "c23283bb-aff3-4737-a71a-a351aab93b02": "true", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "true", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "true", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "true", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "1.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "0.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "灵活", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "true", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "true", "3500246a-e20f-41a2-9551-7a8b8e276766": "true", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "5e08a8ad-e926-43ce-ac02-f2ee776004fa", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "月月定开2号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"净值类周期型非货基\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"9.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"3.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-08-13\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较高风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"成长型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"5\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501520003\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"5.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"4.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"中短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "净值类周期型非货基", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "9.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "3.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-08-13", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较高风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "成长型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "5", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501520003", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "5.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "4.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "中短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "5fecc03b-7b33-4b1d-85c5-6f12fa5c7659", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "A4L", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"349400\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国六\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"三厢\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"false\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"德系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"1.4\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"前轮驱动\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"349400\",\"02616606-4258-406b-8788-caafe919f8de\":\"7档 双离合\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"中型车\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"2008-02-03\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"合资\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"奥迪\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "349400", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国六", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "三厢", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "false", "957b9ebb-6245-475e-96de-4ed6958923b9": "德系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "1.4", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "前轮驱动", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "349400", "02616606-4258-406b-8788-caafe919f8de": "7档 双离合", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "中型车", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "2008-02-03", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "合资", "30f09027-2d89-42dc-a623-877a23350e93": "奥迪"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "74bdb827-2b68-4b98-915d-3c3c08f67430", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "X5", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"994900\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国六\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"SUV\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"false\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"德系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"3\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"全时四驱\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"994900\",\"02616606-4258-406b-8788-caafe919f8de\":\"8档 手自一体\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"中大型SUV\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"1999-09-09\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"进口\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"宝马\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "994900", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国六", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "SUV", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "false", "957b9ebb-6245-475e-96de-4ed6958923b9": "德系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "3", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "全时四驱", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "994900", "02616606-4258-406b-8788-caafe919f8de": "8档 手自一体", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "中大型SUV", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "1999-09-09", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "进口", "30f09027-2d89-42dc-a623-877a23350e93": "宝马"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "78f6e484-33d8-4b20-97aa-d5e3faa293a7", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "招睿青葵一年半09A", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"14.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"6.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"招行代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2021-11-11\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较高风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"平衡型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"6\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"6543234565\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"true\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"true\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"true\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"true\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"6.4\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"6.4\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"true\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"true\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "14.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "6.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "招行代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2021-11-11", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较高风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "平衡型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "6", "f2b882fb-e669-47f8-82a0-375f1233b456": "6543234565", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "true", "c23283bb-aff3-4737-a71a-a351aab93b02": "true", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "true", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "true", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "6.4", "cf00a1a6-3962-47ba-9812-9fb050901b45": "6.4", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "true", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "true", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "845d15fd-860a-41cd-9e9f-beef0cadae5f", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "招行1个月", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销-封闭式\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"15.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"3.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"招行代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2021-09-30\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"中等风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"成长型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"0\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"8501616201\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"6.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"6.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销-封闭式", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "15.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "3.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "招行代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2021-09-30", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "中等风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "成长型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "0", "f2b882fb-e669-47f8-82a0-375f1233b456": "8501616201", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "6.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "6.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "8da98e0f-f608-477b-a901-c09fbbf28f27", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "兴银添利宝", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销-现金管理类产品（货基T+1）\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"11.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"6.4\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"兴银代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-10-31\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"高风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"进取型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"6\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501610355\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"0.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"10.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"灵活\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销-现金管理类产品（货基T+1）", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "11.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "6.4", "576f5b2d-f127-4f12-8df7-eec5240993ce": "兴银代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-10-31", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "高风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "进取型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "6", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501610355", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "0.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "10.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "灵活", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "a86bc1fa-8d34-4431-be20-df9249c0b9d9", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "途胜", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"239900\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国五\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"SUV\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"true\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"韩系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"1.6\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"前轮驱动\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"239900\",\"02616606-4258-406b-8788-caafe919f8de\":\"7档 双离合\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"紧凑型SUV\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"2014-05-08\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"合资\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"现代\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "239900", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国五", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "SUV", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "true", "957b9ebb-6245-475e-96de-4ed6958923b9": "韩系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "1.6", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "前轮驱动", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "239900", "02616606-4258-406b-8788-caafe919f8de": "7档 双离合", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "紧凑型SUV", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "2014-05-08", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "合资", "30f09027-2d89-42dc-a623-877a23350e93": "现代"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "aca712c7-aaa5-4b6f-9625-c7d67f72c735", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "S级", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"1730000\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国六\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"三厢\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"true\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"德系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"3\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"后轮驱动\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"1730000\",\"02616606-4258-406b-8788-caafe919f8de\":\"9档 手自一体\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"豪华型\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"1984-05-12\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"进口\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"奔驰\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "1730000", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国六", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "三厢", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "true", "957b9ebb-6245-475e-96de-4ed6958923b9": "德系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "3", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "后轮驱动", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "1730000", "02616606-4258-406b-8788-caafe919f8de": "9档 手自一体", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "豪华型", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "1984-05-12", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "进口", "30f09027-2d89-42dc-a623-877a23350e93": "奔驰"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "acdc4bab-6996-4214-89ac-304ed941db25", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "月月定开3号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"true\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"净值类周期型非货基\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"true\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"10.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"-0.4\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-09-26\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"高风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"true\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"进取型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"6\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501520004\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"true\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"true\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"true\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"true\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"true\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"true\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"4.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"15.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"中短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"true\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"true\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"true\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "true", "fb055a48-dc89-494c-9076-00031878f5ad": "净值类周期型非货基", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "true", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "10.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "-0.4", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-09-26", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "高风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "true", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "进取型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "6", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501520004", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "true", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "true", "c23283bb-aff3-4737-a71a-a351aab93b02": "true", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "true", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "true", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "true", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "4.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "15.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "中短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "true", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "true", "3500246a-e20f-41a2-9551-7a8b8e276766": "true", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "b90163f3-e006-4824-8d7d-bd17e7227f92", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "招行18个月", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销-封闭式\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"高\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"15.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"-1.1\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"招行代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-08-14\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"高风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"进取型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"6\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"8501616201\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"9.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"12.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销-封闭式", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "高", "5115fdbf-787f-430b-bc72-ef33081e2213": "15.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "-1.1", "576f5b2d-f127-4f12-8df7-eec5240993ce": "招行代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-08-14", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "高风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "进取型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "6", "f2b882fb-e669-47f8-82a0-375f1233b456": "8501616201", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "9.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "12.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "ba91f4b1-5342-4933-8145-c9c3edc44f17", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "月月定开1号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"净值类周期型菲货基\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"高\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"14.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"9.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-09-27\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"保守型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"2\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501291701\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"12.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"1.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "净值类周期型菲货基", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "高", "5115fdbf-787f-430b-bc72-ef33081e2213": "14.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "9.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-09-27", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "保守型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "2", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501291701", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "12.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "1.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "bb3c43d7-6839-4378-8874-3f38006e2fd8", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "天添盈增利5号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"货基T+1货币型\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"6.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"2.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-10-23\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"稳健型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"3\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501212320\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"11.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"9.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"中短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "货基T+1货币型", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "6.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "2.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-10-23", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "稳健型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "3", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501212320", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "11.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "9.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "中短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "bb3cb856-3272-4bfc-844b-a5c41b9131f1", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "天津滨海机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"true\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"T1航站楼B2柜台\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2019-11-02\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"天津市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"0.98\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T3\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"1233\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"2\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"3\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"0\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"true\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"天津\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T1\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "true", "896c53ab-1366-4497-a23a-b781e53c7d9a": "T1航站楼B2柜台", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2019-11-02", "0643030d-4362-477b-9aea-a4bb7e2f2982": "天津市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "0.98", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T3", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "1233", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "2", "d55409b3-22b8-4761-80a1-a6ecc24377af": "3", "23254437-75d8-4092-b89a-3a31d767abdb": "0", "603729ee-867c-4085-b5d3-d18637e3743d": "true", "1098b1aa-0600-4061-b477-7fab6e2552e4": "天津", "90aacea8-17a0-4f72-84d8-dec68debd802": "T1"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "bfca95b5-6d74-43b0-9008-3e0c45164ae8", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "交银7天", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"true\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销-净值类最短持有期\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"true\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"高\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"1.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"6.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"交银代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-10-24\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"中等风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"true\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"平衡型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"4\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501510020\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"true\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"true\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"true\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"true\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"true\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"true\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"3.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"5.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长朗\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"true\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"true\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"true\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "true", "fb055a48-dc89-494c-9076-00031878f5ad": "代销-净值类最短持有期", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "true", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "高", "5115fdbf-787f-430b-bc72-ef33081e2213": "1.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "6.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "交银代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-10-24", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "中等风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "true", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "平衡型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "4", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501510020", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "true", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "true", "c23283bb-aff3-4737-a71a-a351aab93b02": "true", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "true", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "true", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "true", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "3.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "5.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长朗", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "true", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "true", "3500246a-e20f-41a2-9551-7a8b8e276766": "true", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "c0d4969b-712d-4e5b-b06e-c70dbf9f185c", "categoryId": "b925d8f9-51f1-4bff-a040-938874c9127a", "recordName": "帕萨特", "recordValue": "{\"3a37e2df-d6b3-4529-a942-965b5eae20ac\":\"288900\",\"bd9aa337-d19f-4cf2-acfb-0633213389eb\":\"国五\",\"a29743a0-6a1e-4855-973c-0a42e2a3fa42\":\"三厢\",\"6795fdd2-dc2a-4929-a578-a32b05bcbc85\":\"true\",\"957b9ebb-6245-475e-96de-4ed6958923b9\":\"德系\",\"dc6c4a06-396c-47d7-ac88-2d82e9ff449b\":\"5\",\"087bdd27-a298-48eb-b9f5-799ddd598ede\":\"2\",\"3a033272-1e7d-4a07-a9f8-964f71fe3985\":\"前轮驱动\",\"ae6be8ae-8f62-4a12-8b83-0ca12e203a40\":\"288900\",\"02616606-4258-406b-8788-caafe919f8de\":\"7档 双离合\",\"44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4\":\"汽油\",\"10327b7e-f93a-42c9-a9ea-44b3faf2eaaa\":\"中型车\",\"12ebb51e-5c9c-4e7b-b57e-174fe585233c\":\"2012-01-01\",\"ad072374-8993-4b0c-ad29-38bf6ed5c593\":\"合资\",\"30f09027-2d89-42dc-a623-877a23350e93\":\"大众\"}", "values": {"3a37e2df-d6b3-4529-a942-965b5eae20ac": "288900", "bd9aa337-d19f-4cf2-acfb-0633213389eb": "国五", "a29743a0-6a1e-4855-973c-0a42e2a3fa42": "三厢", "6795fdd2-dc2a-4929-a578-a32b05bcbc85": "true", "957b9ebb-6245-475e-96de-4ed6958923b9": "德系", "dc6c4a06-396c-47d7-ac88-2d82e9ff449b": "5", "087bdd27-a298-48eb-b9f5-799ddd598ede": "2", "3a033272-1e7d-4a07-a9f8-964f71fe3985": "前轮驱动", "ae6be8ae-8f62-4a12-8b83-0ca12e203a40": "288900", "02616606-4258-406b-8788-caafe919f8de": "7档 双离合", "44d6c4c3-e7c2-407b-b011-1e6ce7bba1f4": "汽油", "10327b7e-f93a-42c9-a9ea-44b3faf2eaaa": "中型车", "12ebb51e-5c9c-4e7b-b57e-174fe585233c": "2012-01-01", "ad072374-8993-4b0c-ad29-38bf6ed5c593": "合资", "30f09027-2d89-42dc-a623-877a23350e93": "大众"}, "nameValues": null, "tableId": "671cab76-8cde-4e4e-b1dc-76524dc37321", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "c92f74f9-f769-4d14-bb52-8c5ed67e107d", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "招睿青葵一年半09B", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"true\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"true\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"1.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"1.3\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"招行代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2021-09-30\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"true\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"保守型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"6\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"265432334\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"true\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"true\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"true\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"true\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"true\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"true\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"-0.4\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"-0.4\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"true\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"true\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"true\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "true", "fb055a48-dc89-494c-9076-00031878f5ad": "代销", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "true", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "1.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "1.3", "576f5b2d-f127-4f12-8df7-eec5240993ce": "招行代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2021-09-30", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "true", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "保守型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "6", "f2b882fb-e669-47f8-82a0-375f1233b456": "265432334", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "true", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "true", "c23283bb-aff3-4737-a71a-a351aab93b02": "true", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "true", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "true", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "true", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "-0.4", "cf00a1a6-3962-47ba-9812-9fb050901b45": "-0.4", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "true", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "true", "3500246a-e20f-41a2-9551-7a8b8e276766": "true", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "ccae1396-9fb7-49d0-96c5-879d2d85d5d2", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "北京首都机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"true\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"T2航站楼F岛岛头\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2020-12-14\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"北京市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"1.43\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T2\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"1320\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"2\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"1\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"4\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"true\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"北京\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T2\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "true", "896c53ab-1366-4497-a23a-b781e53c7d9a": "T2航站楼F岛岛头", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2020-12-14", "0643030d-4362-477b-9aea-a4bb7e2f2982": "北京市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "1.43", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T2", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "1320", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "2", "d55409b3-22b8-4761-80a1-a6ecc24377af": "1", "23254437-75d8-4092-b89a-3a31d767abdb": "4", "603729ee-867c-4085-b5d3-d18637e3743d": "true", "1098b1aa-0600-4061-b477-7fab6e2552e4": "北京", "90aacea8-17a0-4f72-84d8-dec68debd802": "T2"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "cf5ed47e-e105-4ff2-822f-0e025b5e8bf3", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "太原武宿机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"false\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"航站楼82号柜台\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2021-03-18\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"太原市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"0.45\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T2\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"422\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"5\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"4\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"3\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"true\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"太原\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T1\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "false", "896c53ab-1366-4497-a23a-b781e53c7d9a": "航站楼82号柜台", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2021-03-18", "0643030d-4362-477b-9aea-a4bb7e2f2982": "太原市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "0.45", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T2", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "422", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "5", "d55409b3-22b8-4761-80a1-a6ecc24377af": "4", "23254437-75d8-4092-b89a-3a31d767abdb": "3", "603729ee-867c-4085-b5d3-d18637e3743d": "true", "1098b1aa-0600-4061-b477-7fab6e2552e4": "太原", "90aacea8-17a0-4f72-84d8-dec68debd802": "T1"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "d056e35f-61be-4a6d-b56f-0bbbfbcdd607", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "建信理财嘉鑫最低持有100天第一期", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"true\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"true\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"10.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"12.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2021-09-30\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"true\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"进取型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"0\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"98766543\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"true\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"true\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"true\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"true\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"true\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"true\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"9.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"9.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"中短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"true\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"true\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"true\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "true", "fb055a48-dc89-494c-9076-00031878f5ad": "代销", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "true", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "10.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "12.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2021-09-30", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "true", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "进取型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "0", "f2b882fb-e669-47f8-82a0-375f1233b456": "98766543", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "true", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "true", "c23283bb-aff3-4737-a71a-a351aab93b02": "true", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "true", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "true", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "true", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "9.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "9.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "中短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "true", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "true", "3500246a-e20f-41a2-9551-7a8b8e276766": "true", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "d2c3d947-4d92-45f2-80af-09b14c34c6fd", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "建信理财嘉鑫最低持有200天第一期", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"高\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"11.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"15.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2021-09-30\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"进取型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"5\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"4564342345\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"4.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"4.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "高", "5115fdbf-787f-430b-bc72-ef33081e2213": "11.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "15.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2021-09-30", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "进取型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "5", "f2b882fb-e669-47f8-82a0-375f1233b456": "4564342345", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "4.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "4.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "d3816720-14ae-4084-8db4-89edc9b40f7e", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "月月定开4号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"净值类周期型非货基\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"高\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"12.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"4.01\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-09-30\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较高风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"成长型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"5\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"5501040803\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"6.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"3.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"长期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "净值类周期型非货基", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "高", "5115fdbf-787f-430b-bc72-ef33081e2213": "12.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "4.01", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-09-30", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较高风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "成长型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "5", "f2b882fb-e669-47f8-82a0-375f1233b456": "5501040803", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "6.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "3.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "长期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:44", "updated": "2022-07-04 20:05:44", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "d4360d13-88a0-4a8e-b4e9-b892b55bd25d", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "天年优享12号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"凈值类T+1非货\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"6.4\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"1.3\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-08-12\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"中等风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"平衡型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"4\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"4501419445\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"7.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"4\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"灵活\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "凈值类T+1非货", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "6.4", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "1.3", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-08-12", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "中等风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "平衡型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "4", "f2b882fb-e669-47f8-82a0-375f1233b456": "4501419445", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "7.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "4", "14afbdd9-0a0d-4a08-afe3-85f731246769": "灵活", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "de81b5d7-c38d-44bf-b6d6-d6ccbf109875", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "交银理财稳享固收7天持有期", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"false\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"代销-定开产品（同“净值类周期型非货基”）\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"中\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"5.01\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"-1.3\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"交银代销\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"false\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-08-10\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"低风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"false\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"保守型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"2\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"35012103231\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"false\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"false\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"false\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"false\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"false\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"false\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"2.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"11.01\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"短期\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"false\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"false\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "false", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "代销-定开产品（同“净值类周期型非货基”）", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "中", "5115fdbf-787f-430b-bc72-ef33081e2213": "5.01", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "-1.3", "576f5b2d-f127-4f12-8df7-eec5240993ce": "交银代销", "1ce5a328-1189-4700-b9cf-148847c552b0": "false", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-08-10", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "低风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "false", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "保守型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "2", "f2b882fb-e669-47f8-82a0-375f1233b456": "35012103231", "a5738489-1705-436e-98f8-38f140d9f771": "false", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "false", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "false", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "false", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "false", "8e6daeda-474b-499b-8d11-05723d8ce29f": "false", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "2.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "11.01", "14afbdd9-0a0d-4a08-afe3-85f731246769": "短期", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "false", "658ca97a-4826-4fe7-b889-09e0a4da2242": "false"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "e77d71ff-ca51-425f-8fdc-1f92d3a13cf1", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "石家庄正定机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"false\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"航站楼3层2号柜台\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2016-01-13\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"石家庄市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"0.34\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T2\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"562\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"3\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"1\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"3\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"false\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"石家庄\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T2\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "false", "896c53ab-1366-4497-a23a-b781e53c7d9a": "航站楼3层2号柜台", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2016-01-13", "0643030d-4362-477b-9aea-a4bb7e2f2982": "石家庄市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "0.34", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T2", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "562", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "3", "d55409b3-22b8-4761-80a1-a6ecc24377af": "1", "23254437-75d8-4092-b89a-3a31d767abdb": "3", "603729ee-867c-4085-b5d3-d18637e3743d": "false", "1098b1aa-0600-4061-b477-7fab6e2552e4": "石家庄", "90aacea8-17a0-4f72-84d8-dec68debd802": "T2"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "e7e3033b-f50d-45f7-906b-53a55cdf21e6", "categoryId": "a79069b5-88e1-4c9d-a5f0-273adb96afef", "recordName": "天添盈增利3号", "recordValue": "{\"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38\":\"true\",\"26fde9d0-7b28-453a-9c26-906843e0cd03\":\"false\",\"fb055a48-dc89-494c-9076-00031878f5ad\":\"货基T+0货币型\",\"d3da52ba-653d-4cf8-bcd2-8f674e521434\":\"false\",\"c4e83057-7cc5-4aa6-8a03-c6510b498e1e\":\"低\",\"5115fdbf-787f-430b-bc72-ef33081e2213\":\"-1.3\",\"81f5129b-c310-4d0a-91c8-0f5ebd8630e7\":\"10\",\"576f5b2d-f127-4f12-8df7-eec5240993ce\":\"母行\",\"1ce5a328-1189-4700-b9cf-148847c552b0\":\"true\",\"aec01279-476d-4054-82b8-eb8ea9380fdc\":\"2020-10-25\",\"3bf70455-4ffc-4c10-a0d6-5b915af9e13c\":\"较高风险\",\"28697b10-ba02-48b3-97a2-84b14c14a9cc\":\"true\",\"8fa2f2fd-64e3-4fdc-9969-f09b70db3862\":\"false\",\"3c0c22ee-1cd6-4777-90da-8c89faca2dea\":\"成长型\",\"a7dcc521-75b1-45fd-93d9-98291e29ef3d\":\"5\",\"f2b882fb-e669-47f8-82a0-375f1233b456\":\"4601515118\",\"a5738489-1705-436e-98f8-38f140d9f771\":\"true\",\"b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6\":\"true\",\"86daea23-b598-4dd9-b4cd-cc569dd9d92c\":\"false\",\"72b1d40f-11c2-45c7-bf3b-64e6c479c680\":\"true\",\"769e3455-c90e-425b-a208-06809c471574\":\"false\",\"c23283bb-aff3-4737-a71a-a351aab93b02\":\"false\",\"6c449482-538b-440a-b7be-a360d1c2e384\":\"true\",\"3957daa4-653a-4b36-b769-817477d22214\":\"false\",\"d85cafad-b9d6-42c1-9f61-57465db53ff7\":\"false\",\"fe8242e5-61c5-4359-bd2f-3352fa32c5f8\":\"true\",\"8e6daeda-474b-499b-8d11-05723d8ce29f\":\"true\",\"5644a7d3-49a0-48b6-849f-27688d7add2c\":\"false\",\"28b7c993-dc10-4a7b-867e-ce316d895f0d\":\"13.01\",\"cf00a1a6-3962-47ba-9812-9fb050901b45\":\"2\",\"14afbdd9-0a0d-4a08-afe3-85f731246769\":\"灵活\",\"a1e7385c-c1e8-4c71-8aa7-519cc2e3954e\":\"false\",\"15514e10-9a68-47e1-92a9-71c7b9ecf949\":\"false\",\"3500246a-e20f-41a2-9551-7a8b8e276766\":\"false\",\"8f73ba3c-a355-485b-8243-e70690bde1c2\":\"true\",\"658ca97a-4826-4fe7-b889-09e0a4da2242\":\"true\"}", "values": {"986bf4c7-3d18-4ba2-bfae-3ffe0c26cd38": "true", "26fde9d0-7b28-453a-9c26-906843e0cd03": "false", "fb055a48-dc89-494c-9076-00031878f5ad": "货基T+0货币型", "d3da52ba-653d-4cf8-bcd2-8f674e521434": "false", "c4e83057-7cc5-4aa6-8a03-c6510b498e1e": "低", "5115fdbf-787f-430b-bc72-ef33081e2213": "-1.3", "81f5129b-c310-4d0a-91c8-0f5ebd8630e7": "10", "576f5b2d-f127-4f12-8df7-eec5240993ce": "母行", "1ce5a328-1189-4700-b9cf-148847c552b0": "true", "aec01279-476d-4054-82b8-eb8ea9380fdc": "2020-10-25", "3bf70455-4ffc-4c10-a0d6-5b915af9e13c": "较高风险", "28697b10-ba02-48b3-97a2-84b14c14a9cc": "true", "8fa2f2fd-64e3-4fdc-9969-f09b70db3862": "false", "3c0c22ee-1cd6-4777-90da-8c89faca2dea": "成长型", "a7dcc521-75b1-45fd-93d9-98291e29ef3d": "5", "f2b882fb-e669-47f8-82a0-375f1233b456": "4601515118", "a5738489-1705-436e-98f8-38f140d9f771": "true", "b7bcbb65-4cf4-4532-8e05-00c1e33ac1c6": "true", "86daea23-b598-4dd9-b4cd-cc569dd9d92c": "false", "72b1d40f-11c2-45c7-bf3b-64e6c479c680": "true", "769e3455-c90e-425b-a208-06809c471574": "false", "c23283bb-aff3-4737-a71a-a351aab93b02": "false", "6c449482-538b-440a-b7be-a360d1c2e384": "true", "3957daa4-653a-4b36-b769-817477d22214": "false", "d85cafad-b9d6-42c1-9f61-57465db53ff7": "false", "fe8242e5-61c5-4359-bd2f-3352fa32c5f8": "true", "8e6daeda-474b-499b-8d11-05723d8ce29f": "true", "5644a7d3-49a0-48b6-849f-27688d7add2c": "false", "28b7c993-dc10-4a7b-867e-ce316d895f0d": "13.01", "cf00a1a6-3962-47ba-9812-9fb050901b45": "2", "14afbdd9-0a0d-4a08-afe3-85f731246769": "灵活", "a1e7385c-c1e8-4c71-8aa7-519cc2e3954e": "false", "15514e10-9a68-47e1-92a9-71c7b9ecf949": "false", "3500246a-e20f-41a2-9551-7a8b8e276766": "false", "8f73ba3c-a355-485b-8243-e70690bde1c2": "true", "658ca97a-4826-4fe7-b889-09e0a4da2242": "true"}, "nameValues": null, "tableId": "dd5f89b5-25c5-451c-9edf-28ecd1c361c7", "version": 1, "created": "2022-07-04 20:05:45", "updated": "2022-07-04 20:05:45", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "e8976ec2-77bc-42f0-85c1-d0b5adb9eb34", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "长白山机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"false\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"F岛72号\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"运营中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2005-06-21\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"白山市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"0.54\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T1\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"367\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"4\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"3\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"4\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"false\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"白山\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T3\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "false", "896c53ab-1366-4497-a23a-b781e53c7d9a": "F岛72号", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "运营中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2005-06-21", "0643030d-4362-477b-9aea-a4bb7e2f2982": "白山市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "0.54", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T1", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "367", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "4", "d55409b3-22b8-4761-80a1-a6ecc24377af": "3", "23254437-75d8-4092-b89a-3a31d767abdb": "4", "603729ee-867c-4085-b5d3-d18637e3743d": "false", "1098b1aa-0600-4061-b477-7fab6e2552e4": "白山", "90aacea8-17a0-4f72-84d8-dec68debd802": "T3"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}, {"_effect": 1, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "eb5e8240-9199-4bce-96e8-990625919e1b", "categoryId": "f5b2688d-999e-45c3-b6b3-b3d2da69ff45", "recordName": "大连金州湾机场", "recordValue": "{\"6a39e79b-0522-4a2c-8d8c-91efbc4cc863\":\"false\",\"896c53ab-1366-4497-a23a-b781e53c7d9a\":\"航站楼2号柜台\",\"5f3fe6d7-d8f4-4869-a8ca-196bb684d43d\":\"建设中\",\"4798c68f-80b6-4ed1-b35f-8154e979f68b\":\"2014-01-22\",\"0643030d-4362-477b-9aea-a4bb7e2f2982\":\"大连市\",\"13f8a2a4-0ac2-4877-898b-278792f7eabb\":\"0.31\",\"f81c5934-ba0f-4024-a86d-7fa9aafbc130\":\"T5\",\"674bbb73-8f3f-44ee-b71a-679da4d7c0eb\":\"312\",\"4a185acc-2beb-4b4c-bd56-31b49601d1f8\":\"1\",\"d55409b3-22b8-4761-80a1-a6ecc24377af\":\"4\",\"23254437-75d8-4092-b89a-3a31d767abdb\":\"3\",\"603729ee-867c-4085-b5d3-d18637e3743d\":\"true\",\"1098b1aa-0600-4061-b477-7fab6e2552e4\":\"大连\",\"90aacea8-17a0-4f72-84d8-dec68debd802\":\"T1\"}", "values": {"6a39e79b-0522-4a2c-8d8c-91efbc4cc863": "false", "896c53ab-1366-4497-a23a-b781e53c7d9a": "航站楼2号柜台", "5f3fe6d7-d8f4-4869-a8ca-196bb684d43d": "建设中", "4798c68f-80b6-4ed1-b35f-8154e979f68b": "2014-01-22", "0643030d-4362-477b-9aea-a4bb7e2f2982": "大连市", "13f8a2a4-0ac2-4877-898b-278792f7eabb": "0.31", "f81c5934-ba0f-4024-a86d-7fa9aafbc130": "T5", "674bbb73-8f3f-44ee-b71a-679da4d7c0eb": "312", "4a185acc-2beb-4b4c-bd56-31b49601d1f8": "1", "d55409b3-22b8-4761-80a1-a6ecc24377af": "4", "23254437-75d8-4092-b89a-3a31d767abdb": "3", "603729ee-867c-4085-b5d3-d18637e3743d": "true", "1098b1aa-0600-4061-b477-7fab6e2552e4": "大连", "90aacea8-17a0-4f72-84d8-dec68debd802": "T1"}, "nameValues": null, "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "version": 1, "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "lastEditUsername": "user1", "lastEditUserId": "t1000000001", "createdUserId": "t1000000001", "createdUsername": "user1", "userId": null, "userName": null, "hasActiveCopy": false, "categoryName": null, "tableName": null}], "tableQaNoResponses": [], "slotValues": [], "tableQaTemplates": [], "tableQaUserAnswers": [{"_effect": 0, "agentId": "2cf1fb9b-06d6-42ba-9eb7-3acbbc0496d9", "id": "22260a63-90a1-4510-888c-f76536c3b95d", "sentenceType": "propertyQuery", "sentenceCondition": "all", "sentenceRange": "P=吞吐量", "md5": "87c6ca435a66f430cfbabd833a3e345c", "replyType": "plainText", "replyContent": "|name|有|amount|亿人次", "created": "2022-07-01 17:14:43", "updated": "2022-07-01 17:14:43", "createdUserId": "t1000000001", "createdUsername": "user1", "lastEditUserId": "t1000000001", "lastEditUsername": "user1", "tableId": "9c4c122a-47c0-4897-9b06-f1f97412f123", "hasActiveCopy": false, "tableZhName": null, "categoryName": null}], "tableQaUnits": []}}}