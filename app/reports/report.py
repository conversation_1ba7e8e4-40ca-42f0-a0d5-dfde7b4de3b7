#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
This module provide configigure file management service in i18n environment.

Authors: <AUTHORS>
Date: 2022/11/01 20:05:00
"""
import json
import os
import time
import sys
dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(dir)
from config import FileAddress
from app.reports import report
from flask import request, send_file, jsonify

from .data_process import analysis_res_save, large_model_analysis_res_save
from ..models.models import Task, Analysis, AttitudeAnalysis, IntentsAnalysis, EntityAnalysis
from ..models.exts import get_db
from datetime import datetime

global db
db = get_db()
session = db.session

import sys

reload(sys)
sys.setdefaultencoding('utf8')


@report.route('getReportList', methods=['GET'])
def get_report_list():
    """
    获取报告列表
    包含分页功能
    pn代表页面位置
    ps代表页面大小
    """
    data_args = request.args
    pn = data_args.get('pn', 1, type=int)
    ps = data_args.get('ps', 20, type=int)
    version = data_args.get('version')
    env_name = data_args.get('envName')
    engine = data_args.get('engine')
    task_list = []
    # print pn, ps
    try:
        print pn, ps, version, env_name, engine
        tmp_data = session.query(Task)
        if version is not None and version != "":
            tmp_data = tmp_data.filter(Task.version == '%s' % version)

        if env_name is not None and env_name != "":
            tmp_data = tmp_data.filter(Task.env_name == '%s' % env_name)

        if engine is not None and engine != "":
            tmp_data = tmp_data.filter(Task.engine == '%s' % engine)
        # 查询数据库中总条数
        total = tmp_data.count()

        # task_info_list = session.query(Task).filter(and_(Task.version == version, Task.env_name == env_name, Task.engine == engine))\
        #     .offset((pn - 1) * ps).limit(ps).all()
        # task_info_list = session.query(Task)\
        #     .filter(Task.version.like(version))\
        #     .filter(Task.env_name.like(env_name))\
        #     .filter(Task.engine.like(engine))\
        #     .offset((pn - 1) * ps).limit(ps).all()
        # 分页查询
        # task_info_list = tmp_data.offset((pn - 1) * ps).limit(ps).all()
        task_info_list = tmp_data.order_by(Task.id.desc()).offset((pn - 1) * ps).limit(ps).all()
        for task in task_info_list:
            print task.task2dict()
            task_list.append(task.task2dict())
        res = {
            'code': 200,
            'data': {
                'pn': pn,
                'ps': ps,
                'total': total,
                'list': task_list
            },
            'msg': 'OK'
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        res = {
            'code': 500,
            'msg': '服务器异常'
        }
        return jsonify(res), 500


@report.route('deleteReport', methods=['POST'])
def delete_report():
    """
    删除报告
    :return:
    """
    data_form = request.form
    print data_form
    if data_form is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    ids = data_form.get('ids')
    if ids is None:
        res = {
            'code': 404,
            'msg': '参数格式不正确'
        }
        return jsonify(res), 404
    try:
        print type(ids)
        id_list = ids.split(',')
        print id_list
        for db_id in id_list:
            print db_id
            task = session.query(Task).filter(Task.id == db_id).first()
            task_id = task.task2dict()['task_id']
            print type(task.task2dict())
            print task.task2dict()
            session.query(Task).filter(Task.id == db_id).delete()
            session.query(Analysis).filter(Analysis.task_id == task_id).delete()
            file_path = task.result_dir.encode('utf-8')
            if os.path.exists(file_path):
                os.remove(file_path)
        session.commit()
        res = {
            'code': 200,
            'msg': "OK"
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        session.rollback()
        res = {
            'code': 500,
            'msg': '服务器异常，删除失败'
        }
        return jsonify(res), 500


@report.route('getEnvList', methods=['GET'])
def get_env_list():
    """
    获取环境信息，env、version、engine
    :return:
    """
    try:
        env_info_list, version_info_list, engine_info_list = [], [], []

        env_list = session.query(Task).with_entities(Task.env_name).distinct().all()
        for env_info in env_list:
            env_info_list.append(env_info['env_name'])

        version_list = session.query(Task).with_entities(Task.version).distinct().all()
        for version_info in version_list:
            version_info_list.append(version_info['version'])

        engine_list = session.query(Task).with_entities(Task.engine).distinct().all()
        for engine_info in engine_list:
            engine_info_list.append(engine_info['engine'])
        res = {
            'code': 200,
            'data': {
                'envList': env_info_list,
                'versionList': version_info_list,
                'engineList': engine_info_list
            },
            'msg': 'OK'
        }
        return jsonify(res), 200
    except Exception as e:
        print e.message
        res = {
            'code': 500,
            'msg': '服务器异常'
        }
        return jsonify(res), 500


@report.route('downloadReport', methods=['GET'])
def download_report():
    """
    下载文件
    :return:
    """

    # 获取文件名
    print "downloadReport"
    file_path = str(request.args.get('fileName')).encode('utf-8')
    file_name = str(file_path).split('/')[-1].encode('utf-8')

    print str(file_path)
    if file_path:
        # response = send_file(file_path, as_attachment=True)
        # response.headers["Access-Control-Expose-Headers"] = "Content-disposition"
        # print type(response)
        # 判断文件存不存在
        if os.path.exists(file_path):
            response = send_file(file_path, as_attachment=True, mimetype='application/octet-stream')
            response.headers['Content-Disposition'] = 'attachment;filename=' + file_name
            return response, 200
        else:
            data = {
                "code": 404,
                "msg": "抱歉，文件未找到!"
            }
            return jsonify(data), 404
    else:
        data = {
            "code": 404,
            "msg": "抱歉，文件未找到!"
        }
        return jsonify(data), 404


@report.route('uploadReport', methods=['post'])
def upload_report():
    """
    上传文件
    file和engine为必传
    :return:
    """
    table_dict = {
        'tableqa_rounds': Analysis,
        'intents': IntentsAnalysis,
        'sys_intents': IntentsAnalysis,
        'sys_entity': EntityAnalysis,
        'attitude': AttitudeAnalysis,
    }
    file_sys = request.files.get('file')
    engine = request.form.get('engine')

    env_name = '-' if request.form.get('env') is None else request.form.get('env')
    version = '-' if request.form.get('version') is None else request.form.get('version')
    print env_name
    print version
    if file_sys is None:
        return {'code': 400, 'msg': '请上传文件'}, 400
    if engine is None:
        return {'code': 400, 'msg': '必须选择文件引擎'}, 400

    now_time_stamp = time.time()
    dt = datetime.fromtimestamp(now_time_stamp)
    finish_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    task_id = 'local-' + str(now_time_stamp)
    create_time = '-'
    result_dir = FileAddress.result_dir.encode('utf-8')
    file_path = result_dir + '/' + file_sys.filename
    file_path = file_path.encode('utf-8')

    try:
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)
        new_task = Task(version=version,
                        env_name=env_name,
                        engine=engine,
                        result_dir=file_path.decode('utf8'),
                        task_id=task_id,
                        create_time=create_time,
                        finish_time=finish_time)
        db.session.add(new_task)
        file_sys.save(file_path)

        if engine == 'large_model':
            # 保存时会进行计算，覆盖结果
            save_analysis_res = large_model_analysis_res_save(file_path, task_id, version, finish_time)
            if save_analysis_res is None:
                return {
                   'code': 400,
                   'msg': '结果数据保存失败，请检查文件格式'
                }, 400
        else:
            col = analysis_res_save(file_path, engine)
            if col is None:
                data = {
                    'code': 400,
                    'msg': '结果数据保存失败，请检查文件格式'
                }
                db.session.rollback()
                if os.path.exists(file_path):
                    os.remove(file_path)
                return jsonify(data), 400
            col.task_id = task_id
            col.finish_time = finish_time
            col.version = version
            col.version_finish_time = version + '_' + finish_time
            db.session.add(col)
            db.session.commit()
    except Exception as e:
        print e
        print e.message
        db.session.rollback()
        # 回滚，如果存在文件进行删除
        if os.path.exists(file_path):
            os.remove(file_path)
        return {'code': 500, 'msg': '文件保存失败'}, 500

    return {'code': 200, 'msg': 'OK'}, 200

# @report.route('search', methods=['GET'])
# def search_report():
#     """
#     搜索
#     :return:
#     """
#     data_args = request.args
#     pn = data_args.get('pn', 1, type=int)
#     ps = data_args.get('ps', 20, type=int)
#
#     task_list = []


@report.route('getAnalysisRes', methods=['GET'])
def get_analysis_res():
    """
    获取效果结果数据
    :return:
    """
    id = request.args.get('id')
    print id
    task = session.query(Task).filter(Task.id == '%s' % id).one().task2dict()
    task_id = task['task_id']
    if 'local' in task_id:
        data = {
            'code': 400,
            'msg': '抱歉，本地任务不会保存数据！'
        }
        return jsonify(data), 400
    engine = task['engine']
    table_dict = {
        'tableqa_rounds': Analysis,
        'intents': IntentsAnalysis,
        'sys_intents': IntentsAnalysis,
        'sys_entity': EntityAnalysis,
        'attitude': AttitudeAnalysis,
    }
    table = table_dict[engine]
    try:
        col = session.query(table).filter(table.task_id == task_id).one()
    except Exception as e:
        print e.message
        data = {
            'code': 400,
            'msg': '结果数据并未保存，建议重新执行任务！'
        }
        return jsonify(data), 400
    res = col.object2dict()
    data = {
        'code': 200,
        'data': res,
        'msg': 'OK'
    }
    return jsonify(data), 200
